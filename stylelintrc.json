{"extends": ["stylelint-config-standard", "stylelint-config-tailwindcss"], "rules": {"at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "layer", "variants", "responsive", "screen"]}], "at-rule-no-deprecated": null, "no-descending-specificity": null, "function-no-unknown": [true, {"ignoreFunctions": ["theme"]}], "color-hex-length": null, "custom-property-empty-line-before": null, "selector-class-pattern": null, "rule-empty-line-before": null, "at-rule-empty-line-before": null, "color-function-notation": null, "alpha-value-notation": null, "declaration-empty-line-before": null, "font-family-name-quotes": null, "length-zero-no-unit": null, "no-invalid-position-at-import-rule": null}, "overrides": [{"files": ["**/*.css", "**/*.scss"], "customSyntax": "postcss-syntax"}]}