#!/bin/bash

# Create temporary deployment directory
echo "Creating temporary deployment directory..."
mkdir -p temp_deploy
cp -r src public package.json package-lock.json next.config.js tsconfig.json postcss.config.js tailwind.config.ts components.json .dockerignore Dockerfile.prod docker-compose.yml temp_deploy/

# Create remote directory and set permissions
echo "Setting up remote directory..."
ssh webmaster@10.112.0.205 "echo 'Dam2024!' | sudo -S mkdir -p /opt/asset-hub && echo 'Dam2024!' | sudo -S chown webmaster:webmaster /opt/asset-hub"

# Copy files to remote server
echo "Copying files to remote server..."
scp -r temp_deploy/* webmaster@10.112.0.205:/opt/asset-hub/
scp remote_setup.sh webmaster@10.112.0.205:/opt/asset-hub/

# Setup and run on remote server
echo "Setting up and running on remote server..."
ssh webmaster@10.112.0.205 "cd /opt/asset-hub && \
    chmod +x remote_setup.sh && \
    ./remote_setup.sh && \
    docker-compose up -d --build"

# Cleanup
echo "Cleaning up..."
rm -rf temp_deploy

echo "Deployment completed!" 