#!/bin/bash

# Asset Hub Test Runner
# This script helps run different test suites for the folder detection functionality

set -e

echo "🧪 Asset Hub Test Runner"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js and npm are available
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed or not in PATH"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed or not in PATH"
    exit 1
fi

# Check if Jest dependencies are installed
if [ ! -d "node_modules" ]; then
    print_warning "node_modules not found. Installing dependencies..."
    npm install
fi

# Function to run a specific test suite
run_test_suite() {
    local test_name=$1
    local test_command=$2
    
    print_status "Running $test_name tests..."
    echo "Command: $test_command"
    echo "----------------------------------------"
    
    if eval $test_command; then
        print_success "$test_name tests passed!"
    else
        print_error "$test_name tests failed!"
        return 1
    fi
    echo ""
}

# Main test execution
case "${1:-all}" in
    "detection")
        print_status "Running folder detection tests only"
        run_test_suite "Folder Detection" "npm run test:detection"
        ;;
    "api")
        print_status "Running folder tracking API tests only"
        run_test_suite "Folder Tracking API" "npm run test:api"
        ;;
    "refresh")
        print_status "Running smart refresh tests only"
        run_test_suite "Smart Refresh" "npm run test:refresh"
        ;;
    "integration")
        print_status "Running integration tests only"
        run_test_suite "Integration" "npm run test:integration"
        ;;
    "coverage")
        print_status "Running all tests with coverage report"
        run_test_suite "All Tests (with coverage)" "npm run test:coverage"
        ;;
    "watch")
        print_status "Running tests in watch mode"
        print_warning "Press Ctrl+C to exit watch mode"
        npm run test:watch
        ;;
    "all")
        print_status "Running all test suites"
        
        # Run each test suite individually for better reporting
        run_test_suite "Folder Detection" "npm run test:detection" || exit 1
        run_test_suite "Folder Tracking API" "npm run test:api" || exit 1
        run_test_suite "Smart Refresh" "npm run test:refresh" || exit 1
        run_test_suite "Integration" "npm run test:integration" || exit 1
        
        print_success "All test suites completed successfully! 🎉"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [test-type]"
        echo ""
        echo "Available test types:"
        echo "  detection    - Run folder detection tests"
        echo "  api         - Run folder tracking API tests"
        echo "  refresh     - Run smart refresh tests"
        echo "  integration - Run integration tests"
        echo "  coverage    - Run all tests with coverage report"
        echo "  watch       - Run tests in watch mode"
        echo "  all         - Run all test suites (default)"
        echo "  help        - Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                    # Run all tests"
        echo "  $0 detection         # Run only folder detection tests"
        echo "  $0 coverage          # Run all tests with coverage"
        echo "  $0 watch             # Run tests in watch mode"
        ;;
    *)
        print_error "Unknown test type: $1"
        print_status "Use '$0 help' to see available options"
        exit 1
        ;;
esac
