# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# database
/prisma/db.sqlite
/prisma/db.sqlite-journal
db.sqlite

# next.js
/.next/
/out/
next-env.d.ts

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files

# vercel
.vercel

# typescript
*.tsbuildinfo

# idea files
.idea
.aider*

.cache
.npm

# Asset Hub specific
/storage/
/test-storage/
*.db
*.log
photos.db
server.log
server-new.log
dev-server-output.log
nextjs.log
app.log

# Thumbnails and generated files
/storage/thumbnails/
/storage/database.db

# Test files
test-thumb.jpg
test.jpg