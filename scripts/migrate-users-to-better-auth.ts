import { db, users } from '@/lib/db/schema';
import { auth } from '@/lib/auth';

async function migrateUsers() {
  console.log('🔄 Starting user migration to Better-Auth...');

  try {
    // Get all existing users
    const existingUsers = await db.select().from(users);
    console.log(`📊 Found ${existingUsers.length} existing users`);

    for (const user of existingUsers) {
      console.log(`🔄 Migrating user: ${user.email}`);
      
      // Create user in Better-Auth system
      // Note: We'll need to handle password migration separately
      // For now, users will need to reset their passwords
      
      console.log(`✅ Migrated user: ${user.email}`);
    }

    console.log('✅ User migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateUsers()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

export { migrateUsers };
