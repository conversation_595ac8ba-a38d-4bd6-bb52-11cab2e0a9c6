// Simple script to test Better-Auth functionality
const baseUrl = 'http://localhost:3000';

async function testSignUp() {
  console.log('🔄 Testing user registration...');
  
  try {
    const response = await fetch(`${baseUrl}/api/auth/sign-up/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Asharq!@2025',
        name: 'Admin User',
      }),
    });

    const data = await response.text();
    console.log('📝 Sign-up response:', response.status, data);
    
    if (response.ok) {
      console.log('✅ User registration successful');
    } else {
      console.log('❌ User registration failed');
    }
  } catch (error) {
    console.error('❌ Sign-up error:', error);
  }
}

async function testSignIn() {
  console.log('🔄 Testing user login...');
  
  try {
    const response = await fetch(`${baseUrl}/api/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Asharq!@2025',
      }),
    });

    const data = await response.text();
    console.log('📝 Sign-in response:', response.status, data);
    
    if (response.ok) {
      console.log('✅ User login successful');
    } else {
      console.log('❌ User login failed');
    }
  } catch (error) {
    console.error('❌ Sign-in error:', error);
  }
}

async function runTests() {
  console.log('🚀 Starting Better-Auth tests...');
  await testSignUp();
  await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
  await testSignIn();
  console.log('✅ Tests completed');
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  runTests();
}
