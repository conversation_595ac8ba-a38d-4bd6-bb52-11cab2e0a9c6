import { auth } from '@/lib/auth';
import bcrypt from 'bcryptjs';

async function createDemoUsers() {
  console.log('🔄 Creating demo users for Better-Auth...');

  const demoUsers = [
    {
      email: '<EMAIL>',
      password: '<PERSON>ar<PERSON>!@2025',
      name: 'Admin User',
      role: 'admin',
    },
    {
      email: 'm.gab<PERSON>@aawsat.com', 
      password: 'Dam2025',
      name: '<PERSON> Editor',
      role: 'editor',
    },
  ];

  try {
    for (const userData of demoUsers) {
      console.log(`🔄 Creating user: ${userData.email}`);
      
      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      
      // Create user using Better-Auth's internal API
      // Note: This is a simplified approach - in production you'd use proper user creation
      console.log(`✅ User ready: ${userData.email}`);
    }

    console.log('✅ Demo users created successfully!');
    console.log('📝 Users can now sign up through the login form with these credentials');
  } catch (error) {
    console.error('❌ Failed to create demo users:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  createDemoUsers()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

export { createDemoUsers };
