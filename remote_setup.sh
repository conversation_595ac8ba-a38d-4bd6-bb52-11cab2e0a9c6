#!/bin/bash

# Remove old versions if they exist
echo "Removing old Docker versions..."
echo 'Dam2024!' | sudo -S apt-get remove -y docker docker-engine docker.io containerd runc

# Update package index
echo "Updating package index..."
echo 'Dam2024!' | sudo -S apt-get update

# Install prerequisites
echo "Installing prerequisites..."
echo 'Dam2024!' | sudo -S apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# Add Docker's official GPG key
echo "Adding Docker's GPG key..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Set up the stable repository
echo "Setting up Docker repository..."
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
echo "Installing Docker Engine..."
echo 'Dam2024!' | sudo -S apt-get update
echo 'Dam2024!' | sudo -S apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Add user to docker group
echo "Adding user to docker group..."
echo 'Dam2024!' | sudo -S usermod -aG docker $USER

# Start Docker service
echo "Starting Docker service..."
echo 'Dam2024!' | sudo -S systemctl start docker
echo 'Dam2024!' | sudo -S systemctl enable docker

# Verify installation
echo "Verifying Docker installation..."
docker --version
docker-compose --version 