import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';

describe('Smart Refresh Functionality', () => {
  let testBasePath: string;
  let testFolderPath: string;

  beforeEach(async () => {
    // Create temporary test directory
    testBasePath = '/tmp/asset-hub-refresh-test';
    testFolderPath = path.join(testBasePath, 'TEST-REFRESH');
    
    // Clean up any existing test directories
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch (error) {
      // Ignore if directory doesn't exist
    }
    
    // Create fresh test directory structure
    await fs.mkdir(testBasePath, { recursive: true });
    await fs.mkdir(testFolderPath, { recursive: true });
  });

  afterEach(async () => {
    // Clean up test directories
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Recent File Detection', () => {
    it('should detect files created today', async () => {
      const today = new Date();
      const todayFiles = ['today-1.jpg', 'today-2.png', 'today-3.gif'];
      
      // Create files with today's timestamp
      for (const file of todayFiles) {
        const filePath = path.join(testFolderPath, file);
        await fs.writeFile(filePath, `content for ${file}`);
        
        // Verify file has recent timestamp
        const stats = await fs.stat(filePath);
        const fileDate = new Date(stats.mtime);
        const daysDiff = Math.floor((today.getTime() - fileDate.getTime()) / (1000 * 60 * 60 * 24));
        expect(daysDiff).toBeLessThanOrEqual(1);
      }
      
      // Simulate smart refresh detection
      const recentFiles = await findRecentFiles(testFolderPath, 1);
      expect(recentFiles).toHaveLength(todayFiles.length);
    });

    it('should detect files created within specified days back', async () => {
      const daysBack = 3;
      const recentFiles = ['recent-1.jpg', 'recent-2.png'];
      const oldFiles = ['old-1.jpg', 'old-2.png'];
      
      // Create recent files
      for (const file of recentFiles) {
        await fs.writeFile(path.join(testFolderPath, file), `content for ${file}`);
      }
      
      // Create old files and modify their timestamps
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 10); // 10 days ago
      
      for (const file of oldFiles) {
        const filePath = path.join(testFolderPath, file);
        await fs.writeFile(filePath, `content for ${file}`);
        await fs.utimes(filePath, oldDate, oldDate);
      }
      
      // Find files within the last 3 days
      const foundRecentFiles = await findRecentFiles(testFolderPath, daysBack);
      
      // Should only find the recent files, not the old ones
      expect(foundRecentFiles.length).toBe(recentFiles.length);
      
      // Verify the found files are the recent ones
      const foundFileNames = foundRecentFiles.map(f => path.basename(f));
      expect(foundFileNames.sort()).toEqual(recentFiles.sort());
    });

    it('should handle empty directories during smart refresh', async () => {
      const emptyFolder = path.join(testFolderPath, 'empty-folder');
      await fs.mkdir(emptyFolder);
      
      const recentFiles = await findRecentFiles(emptyFolder, 1);
      expect(recentFiles).toHaveLength(0);
    });
  });

  describe('Folder Change Detection', () => {
    it('should detect new folders created recently', async () => {
      const newFolders = ['new-folder-1', 'new-folder-2'];
      
      // Create new folders
      for (const folder of newFolders) {
        await fs.mkdir(path.join(testFolderPath, folder));
      }
      
      // Find recently created folders
      const recentFolders = await findRecentFolders(testFolderPath, 1);
      expect(recentFolders.length).toBe(newFolders.length);
    });

    it('should detect folders with recent file additions', async () => {
      // Create folder structure
      const folder1 = path.join(testFolderPath, 'folder-with-new-files');
      const folder2 = path.join(testFolderPath, 'folder-without-changes');
      
      await fs.mkdir(folder1);
      await fs.mkdir(folder2);
      
      // Add old file to folder2
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 5);
      const oldFilePath = path.join(folder2, 'old-file.jpg');
      await fs.writeFile(oldFilePath, 'old content');
      await fs.utimes(oldFilePath, oldDate, oldDate);
      
      // Add new file to folder1
      await fs.writeFile(path.join(folder1, 'new-file.jpg'), 'new content');
      
      // Find folders with recent activity
      const activeFolders = await findFoldersWithRecentActivity(testFolderPath, 1);
      
      // Should find folder1 but not folder2
      const activeFolderNames = activeFolders.map(f => path.basename(f));
      expect(activeFolderNames).toContain('folder-with-new-files');
      expect(activeFolderNames).not.toContain('folder-without-changes');
    });
  });

  describe('Performance Optimization', () => {
    it('should efficiently scan large directory structures', async () => {
      // Create a moderately large directory structure
      const folderCount = 10;
      const filesPerFolder = 20;
      
      const startTime = Date.now();
      
      // Create folders and files
      for (let i = 0; i < folderCount; i++) {
        const folderPath = path.join(testFolderPath, `folder-${i}`);
        await fs.mkdir(folderPath);
        
        for (let j = 0; j < filesPerFolder; j++) {
          await fs.writeFile(
            path.join(folderPath, `file-${j}.jpg`),
            `content for folder ${i} file ${j}`
          );
        }
      }
      
      // Perform smart refresh scan
      const recentFiles = await findRecentFiles(testFolderPath, 1);
      
      const endTime = Date.now();
      const scanTime = endTime - startTime;
      
      // Should find all files (they're all recent)
      expect(recentFiles.length).toBe(folderCount * filesPerFolder);
      
      // Should complete in reasonable time (less than 5 seconds for this test size)
      expect(scanTime).toBeLessThan(5000);
    });

    it('should skip scanning old folders when possible', async () => {
      // Create old folder with old files
      const oldFolder = path.join(testFolderPath, 'old-folder');
      await fs.mkdir(oldFolder);
      
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 10);
      
      // Create old files
      for (let i = 0; i < 50; i++) {
        const filePath = path.join(oldFolder, `old-file-${i}.jpg`);
        await fs.writeFile(filePath, `old content ${i}`);
        await fs.utimes(filePath, oldDate, oldDate);
      }
      
      // Set folder timestamp to old date
      await fs.utimes(oldFolder, oldDate, oldDate);
      
      // Create new folder with new files
      const newFolder = path.join(testFolderPath, 'new-folder');
      await fs.mkdir(newFolder);
      await fs.writeFile(path.join(newFolder, 'new-file.jpg'), 'new content');
      
      // Smart refresh should skip the old folder
      const recentFiles = await findRecentFiles(testFolderPath, 3);
      
      // Should only find the new file, not the 50 old files
      expect(recentFiles.length).toBe(1);
      expect(recentFiles[0]).toContain('new-file.jpg');
    });
  });

  describe('Error Handling', () => {
    it('should handle permission errors gracefully', async () => {
      // This test would need to be adapted based on actual permission handling
      expect(true).toBe(true); // Placeholder
    });

    it('should handle corrupted or inaccessible files', async () => {
      // Create a file and then make it inaccessible (simulate corruption)
      const testFile = path.join(testFolderPath, 'test-file.jpg');
      await fs.writeFile(testFile, 'test content');
      
      // The smart refresh should handle this gracefully
      const recentFiles = await findRecentFiles(testFolderPath, 1);
      expect(recentFiles.length).toBeGreaterThanOrEqual(0);
    });
  });
});

// Helper functions to simulate smart refresh functionality
async function findRecentFiles(basePath: string, daysBack: number): Promise<string[]> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysBack);
  
  const recentFiles: string[] = [];
  
  async function scanDirectory(dirPath: string) {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.isFile()) {
          const stats = await fs.stat(fullPath);
          if (stats.mtime > cutoffDate) {
            recentFiles.push(fullPath);
          }
        }
      }
    } catch (error) {
      // Handle errors gracefully
      console.warn(`Error scanning directory ${dirPath}:`, error);
    }
  }
  
  await scanDirectory(basePath);
  return recentFiles;
}

async function findRecentFolders(basePath: string, daysBack: number): Promise<string[]> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysBack);
  
  const recentFolders: string[] = [];
  
  try {
    const entries = await fs.readdir(basePath, { withFileTypes: true });
    
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const fullPath = path.join(basePath, entry.name);
        const stats = await fs.stat(fullPath);
        
        if (stats.mtime > cutoffDate) {
          recentFolders.push(fullPath);
        }
      }
    }
  } catch (error) {
    console.warn(`Error scanning for recent folders:`, error);
  }
  
  return recentFolders;
}

async function findFoldersWithRecentActivity(basePath: string, daysBack: number): Promise<string[]> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysBack);
  
  const activeFolders: string[] = [];
  
  try {
    const entries = await fs.readdir(basePath, { withFileTypes: true });
    
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const folderPath = path.join(basePath, entry.name);
        
        // Check if folder has recent files
        const recentFiles = await findRecentFiles(folderPath, daysBack);
        if (recentFiles.length > 0) {
          activeFolders.push(folderPath);
        }
      }
    }
  } catch (error) {
    console.warn(`Error scanning for folder activity:`, error);
  }
  
  return activeFolders;
}
