import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';
import { FolderTrackingService } from '@/lib/storage/folderTrackingService';

describe('Folder Detection and Tracking', () => {
  let folderService: FolderTrackingService;
  let testBasePath: string;
  let testFolderPath: string;

  beforeEach(async () => {
    // Create temporary test directory
    testBasePath = '/tmp/asset-hub-test';
    testFolderPath = path.join(testBasePath, 'TEST-FOLDER-TRACKING');
    
    // Clean up any existing test directories
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch (error) {
      // Ignore if directory doesn't exist
    }
    
    // Create fresh test directory structure
    await fs.mkdir(testBasePath, { recursive: true });
    await fs.mkdir(testFolderPath, { recursive: true });
    
    // Initialize folder service with test path
    folderService = FolderTrackingService.getInstance();
  });

  afterEach(async () => {
    // Clean up test directories
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Basic Folder Creation and Detection', () => {
    it('should detect new folder creation', async () => {
      const newFolderPath = path.join(testFolderPath, 'new-subfolder');
      
      // Create new folder
      await fs.mkdir(newFolderPath, { recursive: true });
      
      // Check if folder exists
      const stats = await fs.stat(newFolderPath);
      expect(stats.isDirectory()).toBe(true);
    });

    it('should detect new file creation in folder', async () => {
      const testFilePath = path.join(testFolderPath, 'test-image.jpg');
      
      // Create test file
      await fs.writeFile(testFilePath, 'fake image content');
      
      // Check if file exists
      const stats = await fs.stat(testFilePath);
      expect(stats.isFile()).toBe(true);
      expect(stats.size).toBeGreaterThan(0);
    });

    it('should detect multiple files in folder', async () => {
      const files = ['test1.jpg', 'test2.png', 'test3.gif'];
      
      // Create multiple test files
      for (const file of files) {
        await fs.writeFile(path.join(testFolderPath, file), `content for ${file}`);
      }
      
      // Read directory contents
      const dirContents = await fs.readdir(testFolderPath);
      expect(dirContents).toHaveLength(files.length);
      expect(dirContents.sort()).toEqual(files.sort());
    });
  });

  describe('Nested Folder Structure', () => {
    it('should handle nested folder creation', async () => {
      const nestedPath = path.join(testFolderPath, 'level1', 'level2', 'level3');
      
      // Create nested folder structure
      await fs.mkdir(nestedPath, { recursive: true });
      
      // Verify nested structure exists
      const stats = await fs.stat(nestedPath);
      expect(stats.isDirectory()).toBe(true);
    });

    it('should detect files in nested folders', async () => {
      const subfolders = ['subfolder1', 'subfolder2'];
      const filesPerFolder = ['image1.jpg', 'image2.png'];
      
      // Create subfolders and files
      for (const subfolder of subfolders) {
        const subfolderPath = path.join(testFolderPath, subfolder);
        await fs.mkdir(subfolderPath, { recursive: true });
        
        for (const file of filesPerFolder) {
          await fs.writeFile(
            path.join(subfolderPath, file), 
            `content for ${subfolder}/${file}`
          );
        }
      }
      
      // Verify structure
      for (const subfolder of subfolders) {
        const subfolderPath = path.join(testFolderPath, subfolder);
        const contents = await fs.readdir(subfolderPath);
        expect(contents.sort()).toEqual(filesPerFolder.sort());
      }
    });
  });

  describe('File Operations', () => {
    it('should detect file deletion', async () => {
      const testFile = path.join(testFolderPath, 'to-be-deleted.jpg');
      
      // Create file
      await fs.writeFile(testFile, 'temporary content');
      
      // Verify file exists
      let exists = true;
      try {
        await fs.access(testFile);
      } catch {
        exists = false;
      }
      expect(exists).toBe(true);
      
      // Delete file
      await fs.unlink(testFile);
      
      // Verify file is deleted
      exists = true;
      try {
        await fs.access(testFile);
      } catch {
        exists = false;
      }
      expect(exists).toBe(false);
    });

    it('should detect file modification', async () => {
      const testFile = path.join(testFolderPath, 'modifiable.txt');
      const originalContent = 'original content';
      const modifiedContent = 'modified content';
      
      // Create file with original content
      await fs.writeFile(testFile, originalContent);
      const originalStats = await fs.stat(testFile);
      
      // Wait a bit to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Modify file
      await fs.writeFile(testFile, modifiedContent);
      const modifiedStats = await fs.stat(testFile);
      
      // Verify modification
      expect(modifiedStats.mtime.getTime()).toBeGreaterThan(originalStats.mtime.getTime());
      expect(modifiedStats.size).not.toBe(originalStats.size);
      
      // Verify content changed
      const readContent = await fs.readFile(testFile, 'utf-8');
      expect(readContent).toBe(modifiedContent);
    });
  });

  describe('Folder Deletion', () => {
    it('should detect folder deletion', async () => {
      const tempFolder = path.join(testFolderPath, 'temp-folder');
      
      // Create folder
      await fs.mkdir(tempFolder, { recursive: true });
      
      // Verify folder exists
      let exists = true;
      try {
        await fs.access(tempFolder);
      } catch {
        exists = false;
      }
      expect(exists).toBe(true);
      
      // Delete folder
      await fs.rmdir(tempFolder);
      
      // Verify folder is deleted
      exists = true;
      try {
        await fs.access(tempFolder);
      } catch {
        exists = false;
      }
      expect(exists).toBe(false);
    });

    it('should handle recursive folder deletion', async () => {
      const nestedFolder = path.join(testFolderPath, 'parent', 'child');
      const testFile = path.join(nestedFolder, 'test.txt');
      
      // Create nested structure with file
      await fs.mkdir(nestedFolder, { recursive: true });
      await fs.writeFile(testFile, 'test content');
      
      // Verify structure exists
      const stats = await fs.stat(nestedFolder);
      expect(stats.isDirectory()).toBe(true);
      
      // Delete entire parent folder recursively
      await fs.rm(path.join(testFolderPath, 'parent'), { recursive: true });
      
      // Verify deletion
      let exists = true;
      try {
        await fs.access(path.join(testFolderPath, 'parent'));
      } catch {
        exists = false;
      }
      expect(exists).toBe(false);
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle large number of files', async () => {
      const fileCount = 100;
      const files: string[] = [];
      
      // Create many files
      for (let i = 0; i < fileCount; i++) {
        const fileName = `test-file-${i.toString().padStart(3, '0')}.jpg`;
        files.push(fileName);
        await fs.writeFile(
          path.join(testFolderPath, fileName), 
          `content for file ${i}`
        );
      }
      
      // Verify all files exist
      const dirContents = await fs.readdir(testFolderPath);
      expect(dirContents).toHaveLength(fileCount);
      expect(dirContents.sort()).toEqual(files.sort());
    });

    it('should handle special characters in filenames', async () => {
      const specialFiles = [
        'file with spaces.jpg',
        'file-with-dashes.png',
        'file_with_underscores.gif',
        'file.with.dots.jpeg',
        'file(with)parentheses.webp'
      ];
      
      // Create files with special characters
      for (const fileName of specialFiles) {
        await fs.writeFile(
          path.join(testFolderPath, fileName), 
          `content for ${fileName}`
        );
      }
      
      // Verify all files exist
      const dirContents = await fs.readdir(testFolderPath);
      expect(dirContents.sort()).toEqual(specialFiles.sort());
    });

    it('should handle empty folders', async () => {
      const emptyFolder = path.join(testFolderPath, 'empty-folder');
      
      // Create empty folder
      await fs.mkdir(emptyFolder);
      
      // Verify folder exists and is empty
      const stats = await fs.stat(emptyFolder);
      expect(stats.isDirectory()).toBe(true);
      
      const contents = await fs.readdir(emptyFolder);
      expect(contents).toHaveLength(0);
    });
  });
});
