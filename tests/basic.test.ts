import { describe, it, expect } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';

describe('Basic Test Suite', () => {
  it('should run a simple test', () => {
    expect(1 + 1).toBe(2);
  });

  it('should be able to create and read files', async () => {
    const testDir = '/tmp/basic-test';
    const testFile = path.join(testDir, 'test.txt');
    
    // Clean up first
    try {
      await fs.rm(testDir, { recursive: true, force: true });
    } catch {
      // Ignore
    }
    
    // Create directory and file
    await fs.mkdir(testDir, { recursive: true });
    await fs.writeFile(testFile, 'Hello, World!');
    
    // Read and verify
    const content = await fs.readFile(testFile, 'utf-8');
    expect(content).toBe('Hello, World!');
    
    // Clean up
    await fs.rm(testDir, { recursive: true, force: true });
  });

  it('should be able to work with dates', () => {
    const now = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    expect(now.getTime()).toBeGreaterThan(yesterday.getTime());
  });
});
