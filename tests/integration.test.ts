import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';

describe('Asset Hub Integration Tests', () => {
  let testBasePath: string;
  let testFolderPath: string;

  beforeEach(async () => {
    // Create temporary test directory that mimics the real structure
    testBasePath = '/tmp/asset-hub-integration-test';
    testFolderPath = path.join(testBasePath, 'TEST-FOLDER-TRACKING');
    
    // Clean up any existing test directories
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch (error) {
      // Ignore if directory doesn't exist
    }
    
    // Create fresh test directory structure
    await fs.mkdir(testBasePath, { recursive: true });
    await fs.mkdir(testFolderPath, { recursive: true });
  });

  afterEach(async () => {
    // Clean up test directories
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Real-world Scenario: Manual Testing Simulation', () => {
    it('should replicate the manual testing workflow', async () => {
      // Step 1: Create initial folder structure (like your manual test)
      const subfolder1 = path.join(testFolderPath, 'test-subfolder-1');
      const subfolder2 = path.join(testFolderPath, 'test-subfolder-2');
      
      await fs.mkdir(subfolder1, { recursive: true });
      await fs.mkdir(subfolder2, { recursive: true });
      
      // Step 2: Add initial test images
      await fs.writeFile(path.join(testFolderPath, 'test-image-1.jpg'), 'fake image content 1');
      await fs.writeFile(path.join(subfolder1, 'test-image-2.jpg'), 'fake image content 2');
      await fs.writeFile(path.join(subfolder2, 'test-image-3.jpg'), 'fake image content 3');
      
      // Verify initial structure
      let mainFolderStats = await calculateFolderStats(testFolderPath);
      expect(mainFolderStats.fileCount).toBe(1); // Only direct files
      expect(mainFolderStats.imageCount).toBe(1);
      
      let subfolder1Stats = await calculateFolderStats(subfolder1);
      expect(subfolder1Stats.fileCount).toBe(1);
      expect(subfolder1Stats.imageCount).toBe(1);
      
      let subfolder2Stats = await calculateFolderStats(subfolder2);
      expect(subfolder2Stats.fileCount).toBe(1);
      expect(subfolder2Stats.imageCount).toBe(1);
      
      // Step 3: Add more files (simulating your cp commands)
      await fs.writeFile(path.join(testFolderPath, 'test-image-4.jpg'), 'fake image content 4');
      await fs.writeFile(path.join(subfolder1, 'test-image-5.jpg'), 'fake image content 5');
      
      // Detect changes
      const updatedMainStats = await calculateFolderStats(testFolderPath);
      const updatedSubfolder1Stats = await calculateFolderStats(subfolder1);
      
      const mainChanges = detectChanges(mainFolderStats, updatedMainStats);
      const subfolder1Changes = detectChanges(subfolder1Stats, updatedSubfolder1Stats);
      
      expect(mainChanges.hasChanges).toBe(true);
      expect(mainChanges.newFiles).toBe(1);
      expect(subfolder1Changes.hasChanges).toBe(true);
      expect(subfolder1Changes.newFiles).toBe(1);
      
      // Step 4: Delete files (simulating your rm commands)
      await fs.unlink(path.join(testFolderPath, 'test-image-1.jpg'));
      await fs.unlink(path.join(subfolder1, 'test-image-2.jpg'));
      
      const afterDeleteMainStats = await calculateFolderStats(testFolderPath);
      const afterDeleteSubfolder1Stats = await calculateFolderStats(subfolder1);
      
      const deleteMainChanges = detectChanges(updatedMainStats, afterDeleteMainStats);
      const deleteSubfolder1Changes = detectChanges(updatedSubfolder1Stats, afterDeleteSubfolder1Stats);
      
      expect(deleteMainChanges.hasChanges).toBe(true);
      expect(deleteMainChanges.deletedFiles).toBe(1);
      expect(deleteSubfolder1Changes.hasChanges).toBe(true);
      expect(deleteSubfolder1Changes.deletedFiles).toBe(1);
      
      // Step 5: Delete entire subfolder (simulating rm -rf)
      await fs.rm(subfolder2, { recursive: true });
      
      // Verify subfolder2 no longer exists
      let subfolder2Exists = true;
      try {
        await fs.access(subfolder2);
      } catch {
        subfolder2Exists = false;
      }
      expect(subfolder2Exists).toBe(false);
      
      // Step 6: Create new folder and add files
      const newFolder = path.join(testFolderPath, 'test-new-folder');
      await fs.mkdir(newFolder);
      await fs.writeFile(path.join(newFolder, 'new-test-image.jpg'), 'new image content');
      
      const newFolderStats = await calculateFolderStats(newFolder);
      expect(newFolderStats.fileCount).toBe(1);
      expect(newFolderStats.imageCount).toBe(1);
    });

    it('should handle the TEST-IMPROVED-REFRESH scenario', async () => {
      // Simulate creating a file with timestamp-based name (like your test)
      const timestamp = new Date().toISOString().replace(/[:.]/g, '').slice(0, 15);
      const testFileName = `TEST-IMPROVED-REFRESH-${timestamp}.jpg`;
      const testFilePath = path.join(testFolderPath, testFileName);
      
      // Create the test file
      await fs.writeFile(testFilePath, 'test refresh content');
      
      // Verify file exists
      const stats = await fs.stat(testFilePath);
      expect(stats.isFile()).toBe(true);
      expect(stats.size).toBeGreaterThan(0);
      
      // Simulate smart refresh detection
      const recentFiles = await findRecentFiles(testFolderPath, 1);
      expect(recentFiles.length).toBe(1);
      expect(recentFiles[0]).toContain('TEST-IMPROVED-REFRESH');
      
      // Verify the file would be detected by search
      const searchResults = await searchFiles(testFolderPath, 'TEST-IMPROVED-REFRESH');
      expect(searchResults.length).toBe(1);
      expect(searchResults[0]).toContain(testFileName);
    });

    it('should handle rapid file operations', async () => {
      // Simulate rapid file creation/deletion like in your tests
      const operations = [
        { action: 'create', file: 'test-image-6.jpg' },
        { action: 'create', file: 'test-image-7.jpg' },
        { action: 'delete', file: 'test-image-6.jpg' },
        { action: 'delete', file: 'test-image-7.jpg' },
        { action: 'create', file: 'test-image-8.jpg' },
      ];
      
      let currentStats = await calculateFolderStats(testFolderPath);
      
      for (const op of operations) {
        const filePath = path.join(testFolderPath, op.file);
        
        if (op.action === 'create') {
          await fs.writeFile(filePath, `content for ${op.file}`);
        } else if (op.action === 'delete') {
          try {
            await fs.unlink(filePath);
          } catch {
            // File might not exist, ignore
          }
        }
        
        // Check stats after each operation
        const newStats = await calculateFolderStats(testFolderPath);
        const changes = detectChanges(currentStats, newStats);
        
        // Should detect changes for each operation
        if (op.action === 'create') {
          expect(changes.newFiles).toBeGreaterThanOrEqual(0);
        } else if (op.action === 'delete') {
          expect(changes.deletedFiles).toBeGreaterThanOrEqual(0);
        }
        
        currentStats = newStats;
      }
      
      // Final state should have only test-image-8.jpg
      const finalStats = await calculateFolderStats(testFolderPath);
      expect(finalStats.fileCount).toBe(1);
      
      const files = await fs.readdir(testFolderPath);
      expect(files).toContain('test-image-8.jpg');
      expect(files).not.toContain('test-image-6.jpg');
      expect(files).not.toContain('test-image-7.jpg');
    });
  });

  describe('Performance Under Load', () => {
    it('should handle large file operations efficiently', async () => {
      const startTime = Date.now();
      
      // Create a realistic test scenario with multiple folders and files
      const folderStructure = [
        'folder-1',
        'folder-2',
        'folder-3/subfolder-a',
        'folder-3/subfolder-b',
        'folder-4/deep/nested/structure',
      ];
      
      // Create folder structure
      for (const folder of folderStructure) {
        await fs.mkdir(path.join(testFolderPath, folder), { recursive: true });
      }
      
      // Add files to each folder
      const filesPerFolder = 10;
      for (const folder of folderStructure) {
        for (let i = 0; i < filesPerFolder; i++) {
          const fileName = `image-${i}.jpg`;
          const filePath = path.join(testFolderPath, folder, fileName);
          await fs.writeFile(filePath, `content for ${folder}/${fileName}`);
        }
      }
      
      // Perform comprehensive scan
      const allFiles = await findAllFiles(testFolderPath);
      const recentFiles = await findRecentFiles(testFolderPath, 1);
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // Verify results
      expect(allFiles.length).toBe(folderStructure.length * filesPerFolder);
      expect(recentFiles.length).toBe(folderStructure.length * filesPerFolder);
      
      // Should complete in reasonable time
      expect(totalTime).toBeLessThan(10000); // 10 seconds max
    });
  });
});

// Helper functions (reused from other test files)
async function calculateFolderStats(folderPath: string) {
  const stats = await fs.stat(folderPath);
  if (!stats.isDirectory()) {
    throw new Error('Path is not a directory');
  }
  
  const files = await fs.readdir(folderPath);
  let fileCount = 0;
  let imageCount = 0;
  let totalSize = 0;
  
  for (const file of files) {
    const filePath = path.join(folderPath, file);
    const fileStats = await fs.stat(filePath);
    
    if (fileStats.isFile()) {
      fileCount++;
      totalSize += fileStats.size;
      
      const ext = path.extname(file).toLowerCase();
      if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'].includes(ext)) {
        imageCount++;
      }
    }
  }
  
  return {
    name: path.basename(folderPath),
    path: folderPath,
    fileCount,
    imageCount,
    totalSize,
    lastModified: stats.mtime.toISOString(),
    lastScanned: new Date().toISOString(),
    contentHash: generateContentHash(files, totalSize),
  };
}

function detectChanges(oldStats: any, newStats: any) {
  const newFiles = Math.max(0, newStats.fileCount - oldStats.fileCount);
  const deletedFiles = Math.max(0, oldStats.fileCount - newStats.fileCount);
  const hasChanges = newFiles > 0 || deletedFiles > 0 || oldStats.contentHash !== newStats.contentHash;
  
  return {
    hasChanges,
    newFiles,
    deletedFiles,
    modifiedFiles: 0,
  };
}

function generateContentHash(files: string[], totalSize: number): string {
  const content = files.sort().join('') + totalSize.toString();
  return Buffer.from(content).toString('base64').slice(0, 16);
}

async function findRecentFiles(basePath: string, daysBack: number): Promise<string[]> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysBack);
  
  const recentFiles: string[] = [];
  
  async function scanDirectory(dirPath: string) {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.isFile()) {
          const stats = await fs.stat(fullPath);
          if (stats.mtime > cutoffDate) {
            recentFiles.push(fullPath);
          }
        }
      }
    } catch (error) {
      console.warn(`Error scanning directory ${dirPath}:`, error);
    }
  }
  
  await scanDirectory(basePath);
  return recentFiles;
}

async function findAllFiles(basePath: string): Promise<string[]> {
  const allFiles: string[] = [];
  
  async function scanDirectory(dirPath: string) {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.isFile()) {
          allFiles.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Error scanning directory ${dirPath}:`, error);
    }
  }
  
  await scanDirectory(basePath);
  return allFiles;
}

async function searchFiles(basePath: string, searchTerm: string): Promise<string[]> {
  const allFiles = await findAllFiles(basePath);
  return allFiles.filter(file => path.basename(file).includes(searchTerm));
}
