import { jest } from '@jest/globals';

// Mock environment variables for testing
// Set NODE_ENV for test environment
if (!process.env.NODE_ENV) {
  Object.defineProperty(process.env, 'NODE_ENV', {
    value: 'test',
    writable: true
  });
}
process.env.STORAGE_PATH = '/tmp/test-storage';
process.env.DATABASE_URL = 'postgresql://postgres:postgres@localhost:5433/asset_hub_test';

// Global test timeout
jest.setTimeout(30000);
