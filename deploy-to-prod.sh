#!/bin/bash

# Deploy to Production Script
# This script transfers changes from development to production

set -e  # Exit on any error

echo "🚀 Starting deployment to production..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Stop development server
print_status "Stopping development server..."
pkill -f "next dev" || true
sleep 2

# Step 2: Run tests (if any)
print_status "Running tests..."
if npm test -- --passWithNoTests; then
    print_success "Tests passed!"
else
    print_warning "Tests failed, but continuing with deployment..."
fi

# Step 3: Build the application
print_status "Building application..."
if npm run build; then
    print_success "Build completed successfully!"
else
    print_error "Build failed! Deployment aborted."
    exit 1
fi

# Step 4: Stop production containers
print_status "Stopping production containers..."
docker compose down

# Step 5: Rebuild and start production containers
print_status "Rebuilding and starting production containers..."
if docker compose up --build -d; then
    print_success "Production containers started!"
else
    print_error "Failed to start production containers!"
    exit 1
fi

# Step 6: Wait for services to be healthy
print_status "Waiting for services to be healthy..."
sleep 10

# Step 7: Run database migrations
print_status "Running database migrations..."
if npm run db:migrate; then
    print_success "Database migrations completed!"
else
    print_warning "Database migrations failed, but continuing..."
fi

# Step 8: Health check
print_status "Performing health check..."
if curl -f -s http://localhost:8080/api/health > /dev/null; then
    print_success "Health check passed!"
else
    print_warning "Health check failed, but containers are running. Check logs with: docker compose logs"
fi

# Step 9: Show status
print_status "Deployment status:"
docker compose ps

print_success "🎉 Deployment completed!"
echo ""
echo "📊 Services:"
echo "  • Production App: http://localhost:8080"
echo "  • Production DB:  localhost:5433"
echo ""
echo "📋 Management commands:"
echo "  • View logs:      docker compose logs -f"
echo "  • Stop prod:      docker compose down"
echo "  • Restart prod:   docker compose restart"
echo ""
echo "🔄 To continue development:"
echo "  • Start dev:      npm run dev"
echo "  • Dev will run on: http://localhost:3000"
