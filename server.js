// server.js
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')
const http = require('http')

const dev = process.env.NODE_ENV !== 'production'
const app = next.default({ dev })
const handle = app.getRequestHandler()
const port = parseInt(process.env.PORT || '3000', 10)

// Simple function to trigger the indexing API
function triggerIndexing() {
  console.log('> Triggering initial photo indexing...')
  const options = {
    hostname: 'localhost',
    port: port,
    path: '/api/photos/index',
    method: 'POST',
  }

  const req = http.request(options, (res) => {
    let data = ''
    res.on('data', (chunk) => {
      data += chunk
    })
    res.on('end', () => {
      try {
        const response = JSON.parse(data)
        console.log('> Initial indexing started:', response.message)
      } catch (e) {
        console.error('> Error parsing indexing response:', e)
      }
    })
  })

  req.on('error', (error) => {
    console.error('> Error triggering initial indexing:', error)
  })

  req.end()
}

app.prepare().then(() => {
  createServer((req, res) => {
    try {
      const parsedUrl = parse(req.url, true)
      handle(req, res, parsedUrl)
    } catch (err) {
      console.error('Error occurred handling', req.url, err)
      res.statusCode = 500
      res.end('internal server error')
    }
  }).listen(port, (err) => {
    if (err) throw err
    
    console.log(`> Ready on http://localhost:${port}`)
    
    // Trigger indexing after server has started
    setTimeout(triggerIndexing, 5000)
  })
}) 