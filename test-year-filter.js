#!/usr/bin/env node

// Test script to verify year filter functionality
import http from 'http';

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => resolve({ status: res.statusCode, data }));
    });
    req.on('error', reject);
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testYearFilter() {
  console.log('🧪 Testing Year Filter Functionality');
  console.log('=====================================');

  try {
    // Test 1: API endpoint with year filter
    console.log('\n1. Testing API endpoint with year=2024...');
    const apiResponse = await makeRequest('http://localhost:3000/api/photos?year=2024&limit=5');
    const apiData = JSON.parse(apiResponse.data);
    console.log(`✅ API Response: ${apiData.total} assets found for year 2024`);
    console.log(`✅ Filters applied: ${JSON.stringify(apiData.filters)}`);

    // Test 2: Frontend page without filter
    console.log('\n2. Testing frontend page without filter...');
    const homeResponse = await makeRequest('http://localhost:3000/');
    const hasNoAssetsFound = homeResponse.data.includes('No Assets Found');
    const hasLoadingAssets = homeResponse.data.includes('Loading assets');
    console.log(`✅ Home page status: ${homeResponse.status}`);
    console.log(`✅ Shows "No Assets Found": ${hasNoAssetsFound}`);
    console.log(`✅ Shows "Loading assets": ${hasLoadingAssets}`);

    // Test 3: Frontend page with year filter
    console.log('\n3. Testing frontend page with year=2024...');
    const filteredResponse = await makeRequest('http://localhost:3000/?year=2024');
    const filteredHasNoAssets = filteredResponse.data.includes('No Assets Found');
    const filteredHasLoading = filteredResponse.data.includes('Loading assets');
    console.log(`✅ Filtered page status: ${filteredResponse.status}`);
    console.log(`✅ Shows "No Assets Found": ${filteredHasNoAssets}`);
    console.log(`✅ Shows "Loading assets": ${filteredHasLoading}`);

    // Test 4: Check if filter component is loading
    console.log('\n4. Testing filter component loading...');
    const hasFilterComponent = filteredResponse.data.includes('Loading filter');
    const hasBailout = filteredResponse.data.includes('BAILOUT_TO_CLIENT_SIDE_RENDERING');
    console.log(`✅ Shows "Loading filter": ${hasFilterComponent}`);
    console.log(`✅ Has client-side bailout: ${hasBailout}`);

    // Summary
    console.log('\n📊 SUMMARY');
    console.log('===========');
    console.log(`✅ API working: ${apiData.total > 0 ? 'YES' : 'NO'} (${apiData.total} assets)`);
    console.log(`✅ Frontend loading: ${!filteredHasNoAssets && !filteredHasLoading ? 'YES' : 'NO'}`);
    console.log(`✅ Filter component: ${hasFilterComponent || hasBailout ? 'YES' : 'NO'}`);
    
    if (apiData.total > 0 && !filteredHasNoAssets && !filteredHasLoading) {
      console.log('\n🎉 SUCCESS: Year filter is working correctly!');
      console.log('   - API returns filtered results');
      console.log('   - Frontend loads without errors');
      console.log('   - Filter component is properly loading');
    } else {
      console.log('\n⚠️  ISSUES DETECTED:');
      if (apiData.total === 0) console.log('   - API not returning results');
      if (filteredHasNoAssets) console.log('   - Frontend showing "No Assets Found"');
      if (filteredHasLoading) console.log('   - Frontend stuck in loading state');
    }

  } catch (error) {
    console.error('❌ Test failed:', error instanceof Error ? error.message : String(error));
  }
}

testYearFilter();
