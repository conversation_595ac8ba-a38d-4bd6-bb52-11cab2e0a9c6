#!/bin/sh

# Create mount point if it doesn't exist
sudo mkdir -p /storage/photos/MASTER-PHOTOS

# Mount the SMB share
echo "Mounting SMB share..."
sudo mount -t cifs //10.112.0.205/MASTER-PHOTOS /storage/photos/MASTER-PHOTOS -o username=a.sirokh,password=Oo26032014,vers=3.0,iocharset=utf8,file_mode=0777,dir_mode=0777

# Check if mount was successful
if [ $? -eq 0 ]; then
  echo "SMB share mounted successfully"
else
  echo "Failed to mount SMB share"
  exit 1
fi 