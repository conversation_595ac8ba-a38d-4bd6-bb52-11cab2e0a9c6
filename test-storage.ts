import { storageService } from './src/lib/storage/storageService';

async function testStorage() {
  try {
    console.log('Starting storage test...');
    
    // Test scanning directory
    console.log('Scanning directory...');
    const assets = await storageService.scanDirectory();
    console.log('Found assets:', assets.length);
    
    // Test file watching
    console.log('Starting file watcher...');
    storageService.startWatching((event, path) => {
      console.log(`File ${event}: ${path}`);
    });
    
    console.log('Storage service is running. Add files to test directory to see changes.');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testStorage(); 