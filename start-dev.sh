#!/bin/bash

# Start Development Environment Script

set -e  # Exit on any error

echo "🔧 Starting development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Stop any running development servers
print_status "Stopping any running development servers..."
pkill -f "next dev" || true
sleep 2

# Step 2: Check if production is running
print_status "Checking production status..."
if docker compose ps | grep -q "Up"; then
    print_success "Production is running on port 8080"
else
    print_warning "Production is not running. Start it with: docker compose up -d"
fi

# Step 3: Install dependencies if needed
if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
    print_status "Installing/updating dependencies..."
    npm install
fi

# Step 4: Start development server
print_status "Starting development server..."
echo ""
print_success "🎉 Development environment ready!"
echo ""
echo "📊 Environment:"
echo "  • Development:    http://localhost:3000 (Next.js dev server)"
echo "  • Production:     http://localhost:8080 (Docker containers)"
echo "  • Database:       localhost:5433 (shared between dev & prod)"
echo ""
echo "🔄 Workflow:"
echo "  1. Develop on:    http://localhost:3000"
echo "  2. Test changes locally"
echo "  3. Deploy with:   ./deploy-to-prod.sh"
echo ""
echo "📋 Useful commands:"
echo "  • Stop dev:       Ctrl+C"
echo "  • Deploy:         ./deploy-to-prod.sh"
echo "  • Check prod:     docker compose ps"
echo ""
print_status "Starting Next.js development server..."

# Start the development server
npm run dev
