#!/bin/bash

# Stop any running Next.js server
pkill -f "next" || true
pkill -f "npm run dev" || true

# Wait a moment to ensure processes are terminated
sleep 1

# Start the server with nohup
cd /home/<USER>/asset-hub
nohup npm run dev > server.log 2>&1 &

# Wait a moment for the server to start
sleep 3

# Display server status
echo "Server restarted. Log output:"
tail -10 server.log

echo ""
echo "Server should be available at http://localhost:3000" 