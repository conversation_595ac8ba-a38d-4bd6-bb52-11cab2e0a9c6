// server.js
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')
const http = require('http')

const dev = process.env.NODE_ENV !== 'production'
const app = next.default({ dev })
const handle = app.getRequestHandler()
const port = parseInt(process.env.PORT || '3000', 10)

// Simple function to trigger the indexing API
function triggerIndexing() {
  console.log('> Checking if initial photo indexing is needed...')
  const options = {
    hostname: '127.0.0.1',
    port: port,
    path: '/api/storage/scan', // Use the new GET endpoint
    method: 'GET',
  }

  const req = http.request(options, (res) => {
    let data = ''
    res.on('data', (chunk) => {
      data += chunk
    })
    res.on('end', () => {
      try {
        const response = JSON.parse(data)
        if (response.indexingTriggered) {
          console.log('> Indexing started or triggered by startup.');
        } else {
          console.log('> Database already up to date. No indexing triggered.');
        }
      } catch (e) {
        console.error('> Error parsing indexing response:', e)
      }
    })
  })

  req.on('error', (error) => {
    console.error('> Error triggering initial indexing:', error)
  })

  req.end()
}

app.prepare().then(() => {
  createServer((req, res) => {
    // Be sure to pass `true` as the second argument to `url.parse`.
    // This tells it to parse the query portion of the URL.
    const parsedUrl = parse(req.url, true)
    handle(req, res, parsedUrl)
  }).listen(port, '0.0.0.0', (err) => {
    if (err) throw err
    console.log(`> Ready on http://10.112.0.205:${port}`)
    
    // Trigger initial indexing after server starts
    setTimeout(triggerIndexing, 5000)
  })
}) 