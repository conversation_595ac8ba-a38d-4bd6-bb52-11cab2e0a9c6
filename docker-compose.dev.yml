version: '3'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/asset_hub
      - STORAGE_PATH=/storage/photos/MASTER-PHOTOS
    volumes:
      - ./:/app
      - /app/node_modules
      - /storage/photos:/storage/photos
    depends_on:
      - db
    command: npm run dev

  db:
    image: postgres:13
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=asset_hub
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data

volumes:
  postgres_dev_data: