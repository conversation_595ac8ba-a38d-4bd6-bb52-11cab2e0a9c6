// Service Worker for Asset Hub - Performance Optimization
// Implements aggressive caching for thumbnails and API responses

const CACHE_NAME = 'asset-hub-v1';
const THUMBNAIL_CACHE = 'asset-hub-thumbnails-v1';
const API_CACHE = 'asset-hub-api-v1';

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate'
};

// URLs to cache with different strategies
const CACHE_RULES = [
  {
    pattern: /\/api\/storage\/thumbnail\//,
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    cache: THUMBNAIL_CACHE,
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  },
  {
    pattern: /\/api\/photos\?/,
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    cache: API_CACHE,
    maxAge: 5 * 60 * 1000 // 5 minutes
  },
  {
    pattern: /\/api\/assets\/stats/,
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    cache: API_CACHE,
    maxAge: 2 * 60 * 1000 // 2 minutes
  },
  {
    pattern: /\.(js|css|woff2|woff|ttf)$/,
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    cache: CACHE_NAME,
    maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
  }
];

// Install event - cache essential resources
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log('🔧 Service Worker: Caching essential resources');
      return cache.addAll([
        '/',
        '/dashboard',
        '/login'
      ]);
    })
  );
  
  // Skip waiting to activate immediately
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🔧 Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && 
              cacheName !== THUMBNAIL_CACHE && 
              cacheName !== API_CACHE) {
            console.log('🧹 Service Worker: Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Take control of all clients immediately
  self.clients.claim();
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Find matching cache rule
  const rule = CACHE_RULES.find(rule => rule.pattern.test(url.pathname + url.search));
  
  if (rule) {
    event.respondWith(handleCachedRequest(request, rule));
  } else {
    // Default to network for unmatched requests
    event.respondWith(fetch(request));
  }
});

// Handle cached requests based on strategy
async function handleCachedRequest(request, rule) {
  const cache = await caches.open(rule.cache);
  const cachedResponse = await cache.match(request);
  
  switch (rule.strategy) {
    case CACHE_STRATEGIES.CACHE_FIRST:
      return handleCacheFirst(request, cache, cachedResponse, rule);
      
    case CACHE_STRATEGIES.NETWORK_FIRST:
      return handleNetworkFirst(request, cache, cachedResponse, rule);
      
    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
      return handleStaleWhileRevalidate(request, cache, cachedResponse, rule);
      
    default:
      return fetch(request);
  }
}

// Cache-first strategy (for thumbnails and static assets)
async function handleCacheFirst(request, cache, cachedResponse, rule) {
  if (cachedResponse && !isExpired(cachedResponse, rule.maxAge)) {
    console.log('🎯 Cache hit (cache-first):', request.url);
    return cachedResponse;
  }
  
  try {
    console.log('🌐 Cache miss, fetching:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Clone and cache the response
      const responseToCache = networkResponse.clone();
      await cache.put(request, responseToCache);
    }
    
    return networkResponse;
  } catch (error) {
    console.log('❌ Network failed, returning cached response:', error);
    return cachedResponse || new Response('Network error', { status: 503 });
  }
}

// Network-first strategy
async function handleNetworkFirst(request, cache, cachedResponse, rule) {
  try {
    console.log('🌐 Network first, fetching:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const responseToCache = networkResponse.clone();
      await cache.put(request, responseToCache);
    }
    
    return networkResponse;
  } catch (error) {
    console.log('❌ Network failed, returning cached response:', error);
    if (cachedResponse && !isExpired(cachedResponse, rule.maxAge)) {
      return cachedResponse;
    }
    throw error;
  }
}

// Stale-while-revalidate strategy (for API responses)
async function handleStaleWhileRevalidate(request, cache, cachedResponse, rule) {
  // Return cached response immediately if available
  if (cachedResponse) {
    console.log('🎯 Returning cached response (stale-while-revalidate):', request.url);
    
    // Update cache in background if expired
    if (isExpired(cachedResponse, rule.maxAge)) {
      console.log('🔄 Background revalidation for:', request.url);
      fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
          cache.put(request, networkResponse.clone());
        }
      }).catch(error => {
        console.log('❌ Background revalidation failed:', error);
      });
    }
    
    return cachedResponse;
  }
  
  // No cached response, fetch from network
  try {
    console.log('🌐 No cache, fetching:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const responseToCache = networkResponse.clone();
      await cache.put(request, responseToCache);
    }
    
    return networkResponse;
  } catch (error) {
    console.log('❌ Network failed:', error);
    throw error;
  }
}

// Check if cached response is expired
function isExpired(response, maxAge) {
  const cachedDate = new Date(response.headers.get('date') || Date.now());
  const now = new Date();
  return (now.getTime() - cachedDate.getTime()) > maxAge;
}

// Message handling for cache management
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'CLEAR_CACHE':
      clearCache(payload.cacheName);
      break;
      
    case 'CLEAR_ALL_CACHES':
      clearAllCaches();
      break;
      
    case 'GET_CACHE_SIZE':
      getCacheSize().then(size => {
        event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
      });
      break;
  }
});

// Clear specific cache
async function clearCache(cacheName) {
  try {
    await caches.delete(cacheName);
    console.log('🧹 Cleared cache:', cacheName);
  } catch (error) {
    console.error('❌ Failed to clear cache:', error);
  }
}

// Clear all caches
async function clearAllCaches() {
  try {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
    console.log('🧹 Cleared all caches');
  } catch (error) {
    console.error('❌ Failed to clear all caches:', error);
  }
}

// Get total cache size
async function getCacheSize() {
  try {
    const cacheNames = await caches.keys();
    let totalSize = 0;
    
    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName);
      const keys = await cache.keys();
      
      for (const request of keys) {
        const response = await cache.match(request);
        if (response) {
          const blob = await response.blob();
          totalSize += blob.size;
        }
      }
    }
    
    return totalSize;
  } catch (error) {
    console.error('❌ Failed to calculate cache size:', error);
    return 0;
  }
}
