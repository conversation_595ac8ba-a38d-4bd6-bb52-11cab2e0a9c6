#!/bin/bash

# Kill any running instances of the app
pkill -f 'npx next dev' || true

# Wait a second
sleep 1

# Restart the app
echo "Starting the app..."
npx next dev -p 3000 > app.log 2>&1 &

# Wait for the app to start
sleep 3

# Check if the app is running
if curl -s http://localhost:3000/api/photos/status > /dev/null; then
  echo "App started successfully. Available at http://localhost:3000"
  echo "Log file: app.log"
else
  echo "Warning: App may not have started correctly. Check app.log for errors."
fi 