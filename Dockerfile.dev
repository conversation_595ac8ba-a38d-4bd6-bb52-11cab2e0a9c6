# Use Node.js as base image
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install dependencies
RUN apk add --no-cache python3 make g++ postgresql-client

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci

# Create mount point
RUN mkdir -p /storage/photos

# Expose development port
EXPOSE 8080

# Start development server
CMD ["npm", "run", "dev"] 