'use client'

import React from "react";
import { Asset } from "@/lib/types";
import { AssetCard } from "./AssetCard";
import { motion } from "framer-motion";

interface AssetGridProps {
  assets: Asset[];
  onAssetClick: (asset: Asset) => void;
}

export function AssetGrid({ assets, onAssetClick }: AssetGridProps) {
  if (!assets.length) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] bg-secondary/50 rounded-lg p-8 text-center">
        <p className="text-lg font-medium text-muted-foreground">No assets found</p>
        <p className="text-sm text-muted-foreground mt-1">Upload assets to get started</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {assets.map((asset, i) => (
        <motion.div
          key={asset.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, delay: i * 0.05 }}
        >
          <AssetCard asset={asset} onClick={() => onAssetClick(asset)} />
        </motion.div>
      ))}
    </div>
  );
}
