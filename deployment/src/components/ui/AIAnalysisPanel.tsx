'use client'

import React, { useState } from 'react';
import { Asset } from '@/lib/types';
import { generateTextWithImages } from '@/lib/api/util';
import { Loader2, <PERSON>rk<PERSON>, RefreshCw } from 'lucide-react';

interface AIAnalysisPanelProps {
  asset: Asset;
}

export function AIAnalysisPanel({ asset }: AIAnalysisPanelProps) {
  const [analysis, setAnalysis] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<string>('azure-gpt-4o');

  const analyzeImage = async () => {
    if (asset.type !== 'image') return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await generateTextWithImages(
        "Analyze this image in detail. Include information about: 1) What's in the image 2) Any notable objects, people, or scenes 3) The composition, lighting, and style 4) Any text visible in the image 5) The overall mood or theme. Format your response in clear paragraphs.",
        [asset.fileUrl],
        selectedModel
      );
      
      setAnalysis(result.text);
    } catch (err) {
      setError(`Error analyzing image: ${err instanceof Error ? err.message : String(err)}`);
      console.error('AI analysis error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  if (asset.type !== 'image') {
    return null;
  }

  return (
    <div className="border border-border rounded-lg overflow-hidden">
      <div className="bg-muted/20 px-4 py-2 border-b border-border flex justify-between items-center">
        <h3 className="font-medium flex items-center">
          <Sparkles className="h-4 w-4 mr-2 text-primary" />
          AI Image Analysis
        </h3>
        <div className="flex items-center space-x-2">
          <select 
            className="text-xs px-2 py-1 rounded border border-border bg-background"
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
          >
            <option value="azure-gpt-4o">GPT-4o</option>
          </select>
          <button
            onClick={analyzeImage}
            disabled={isLoading}
            className="text-xs px-2 py-1 rounded bg-primary text-primary-foreground flex items-center"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Analyzing...
              </>
            ) : analysis ? (
              <>
                <RefreshCw className="h-3 w-3 mr-1" />
                Refresh
              </>
            ) : (
              'Analyze'
            )}
          </button>
        </div>
      </div>
      
      <div className="p-4">
        {error ? (
          <div className="text-sm text-destructive">{error}</div>
        ) : isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
            <span>Analyzing image...</span>
          </div>
        ) : analysis ? (
          <div className="text-sm space-y-2 whitespace-pre-wrap">{analysis}</div>
        ) : (
          <div className="text-sm text-muted-foreground text-center py-6">
            Click "Analyze" to get AI insights about this image
          </div>
        )}
      </div>
    </div>
  );
}
