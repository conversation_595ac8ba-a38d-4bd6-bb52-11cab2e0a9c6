'use client'

import React, { useState } from "react";
import { Asset } from "@/lib/types";
import { cn, formatBytes } from "@/lib/utils";
import { motion } from "framer-motion";
import { File, FileVideo, FileAudio, FileText, Star, MoreHorizontal } from "lucide-react";

interface AssetCardProps {
  asset: Asset;
  onClick: () => void;
}

export function AssetCard({ asset, onClick }: AssetCardProps) {
  const [isHovering, setIsHovering] = useState(false);

  const getAssetIcon = () => {
    switch (asset.type) {
      case 'video':
        return <FileVideo className="h-6 w-6" />;
      case 'audio':
        return <FileAudio className="h-6 w-6" />;
      case 'document':
        return <FileText className="h-6 w-6" />;
      default:
        return <File className="h-6 w-6" />;
    }
  };

  return (
    <div
      className="group relative rounded-md border border-border overflow-hidden bg-card"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div 
        className="aspect-square cursor-pointer overflow-hidden"
        onClick={onClick}
      >
        {asset.type === 'image' ? (
          <img
            src={asset.thumbnailUrl}
            alt={asset.title}
            className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
        ) : asset.type === 'video' ? (
          <div className="relative h-full w-full bg-black/20">
            <img
              src={asset.thumbnailUrl}
              alt={asset.title}
              className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="rounded-full bg-black bg-opacity-50 p-3">
                <FileVideo className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-muted/30">
            {getAssetIcon()}
          </div>
        )}
      </div>

      <div className="p-2">
        <h3 className="text-sm font-medium truncate">{asset.title}</h3>
        <p className="text-xs text-muted-foreground">
          {formatBytes(asset.size)}
        </p>
      </div>
      
      {isHovering && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute top-2 right-2 flex space-x-1"
        >
          <button className="rounded-full bg-background bg-opacity-70 p-1.5 hover:bg-opacity-90">
            <Star className="h-4 w-4" />
          </button>
          <button className="rounded-full bg-background bg-opacity-70 p-1.5 hover:bg-opacity-90">
            <MoreHorizontal className="h-4 w-4" />
          </button>
        </motion.div>
      )}
    </div>
  );
}
