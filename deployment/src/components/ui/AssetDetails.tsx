'use client'

import React, { useState } from "react";
import { Asset } from "@/lib/types";
import { formatBytes, formatDate } from "@/lib/utils";
import { X, Download, Pencil, Trash, Save, Info } from "lucide-react";
import { motion } from "framer-motion";

import { MetadataDisplay } from "./MetadataDisplay";

interface AssetDetailsProps {
  asset: Asset;
  onClose: () => void;
  onUpdate: (id: string, updates: Partial<Asset>) => void;
  onDelete: (id: string) => void;
}

export function AssetDetails({ asset, onClose, onUpdate, onDelete }: AssetDetailsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedAsset, setEditedAsset] = useState<Asset>(asset);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedAsset(prev => ({ ...prev, [name]: value }));
  };

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsString = e.target.value;
    const tagsArray = tagsString.split(',').map(tag => tag.trim());
    setEditedAsset(prev => ({ ...prev, tags: tagsArray }));
  };

  const handleSave = () => {
    onUpdate(asset.id, editedAsset);
    setIsEditing(false);
  };

  const renderAssetPreview = () => {
    switch (asset.type) {
      case 'image':
        return <img src={asset.fileUrl} alt={asset.title} className="w-full h-auto rounded-md" />;
      case 'video':
        return (
          <video controls className="w-full h-auto rounded-md">
            <source src={asset.fileUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        );
      case 'audio':
        return (
          <audio controls className="w-full mt-4">
            <source src={asset.fileUrl} type="audio/mpeg" />
            Your browser does not support the audio element.
          </audio>
        );
      default:
        return (
          <div className="flex items-center justify-center h-48 bg-muted rounded-md">
            <p className="text-muted-foreground">Preview not available</p>
          </div>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    >
      <div className="bg-background border border-border rounded-lg shadow-lg max-w-4xl w-full mx-4 overflow-hidden max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center border-b border-border p-4">
          <h2 className="text-xl font-semibold">Asset Details</h2>
          <button onClick={onClose} className="p-1 rounded-md hover:bg-secondary">
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
          <div className="md:w-1/2 p-4 overflow-y-auto">
            {renderAssetPreview()}
            
            <div className="mt-4 space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Filename:</span>
                <span className="text-sm font-medium">{asset.filename}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Type:</span>
                <span className="text-sm font-medium capitalize">{asset.type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Size:</span>
                <span className="text-sm font-medium">{formatBytes(asset.size)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Created:</span>
                <span className="text-sm font-medium">{formatDate(asset.createdAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Modified:</span>
                <span className="text-sm font-medium">{formatDate(asset.updatedAt)}</span>
              </div>
            </div>
          </div>
          
          <div className="md:w-1/2 border-t md:border-t-0 md:border-l border-border p-4 overflow-y-auto">
            {isEditing ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Title</label>
                  <input
                    type="text"
                    name="title"
                    value={editedAsset.title}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-input rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Description</label>
                  <textarea
                    name="description"
                    value={editedAsset.description || ''}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-3 py-2 border border-input rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Tags (comma separated)</label>
                  <input
                    type="text"
                    value={editedAsset.tags.join(', ')}
                    onChange={handleTagsChange}
                    className="w-full px-3 py-2 border border-input rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                  />
                </div>
              </div>
            ) : (
              <div>
                <h3 className="text-xl font-medium mb-2">{asset.title}</h3>
                {asset.description && (
                  <p className="text-sm text-muted-foreground mb-4">{asset.description}</p>
                )}
                
                {asset.tags.length > 0 && (
                  <div className="mb-4">
                    <div className="text-sm font-medium mb-1">Tags</div>
                    <div className="flex flex-wrap gap-1">
                      {asset.tags.map(tag => (
                        <span key={tag} className="inline-flex items-center px-2 py-1 rounded-md bg-secondary text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
            {asset.type === 'image' && Object.keys(asset.metadata).length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-muted-foreground flex items-center">
                  <Info className="h-4 w-4 mr-1" />
                  Image Metadata
                </h4>
                <div className="mt-2 max-h-[400px] overflow-y-auto">
                  <MetadataDisplay metadata={asset.metadata} />
                </div>
              </div>
            )}
            

          </div>
        </div>
        
        <div className="border-t border-border p-4 flex justify-between">
          <div>
            {isEditing ? (
              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  className="inline-flex items-center rounded-md bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground hover:bg-primary hover:bg-opacity-90"
                >
                  <Save className="mr-1 h-4 w-4" />
                  Save Changes
                </button>
                <button
                  onClick={() => {
                    setEditedAsset(asset);
                    setIsEditing(false);
                  }}
                  className="inline-flex items-center rounded-md bg-secondary px-3 py-1.5 text-sm font-medium hover:bg-secondary-foreground hover:text-secondary"
                >
                  Cancel
                </button>
              </div>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={() => setIsEditing(true)}
                  className="inline-flex items-center rounded-md bg-secondary px-3 py-1.5 text-sm font-medium hover:bg-secondary hover:bg-opacity-70"
                >
                  <Pencil className="mr-1 h-4 w-4" />
                  Edit
                </button>
                <button
                  onClick={() => onDelete(asset.id)}
                  className="inline-flex items-center rounded-md bg-destructive px-3 py-1.5 text-sm font-medium text-destructive-foreground hover:bg-destructive hover:bg-opacity-90"
                >
                  <Trash className="mr-1 h-4 w-4" />
                  Delete
                </button>
              </div>
            )}
          </div>
          <button
            onClick={() => {
              // Create a temporary anchor element to download the file
              const link = document.createElement('a');
              link.href = asset.fileUrl;
              link.download = asset.filename;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }}
            className="inline-flex items-center rounded-md bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground hover:bg-primary hover:bg-opacity-90"
          >
            <Download className="mr-1 h-4 w-4" />
            Download
          </button>
        </div>
      </div>
    </motion.div>
  );
}
