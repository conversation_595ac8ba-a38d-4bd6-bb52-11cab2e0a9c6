'use client'

import React, { useState, useEffect } from "react";
import { AssetGrid } from "@/components/ui/AssetGrid";
import { AssetDetails } from "@/components/ui/AssetDetails";
import { useAssets, useSearch } from "@/lib/store";
import { Asset } from "@/lib/types";
import { Grid, List, SlidersHorizontal, Search } from "lucide-react";

export function DashboardView() {
  const { assets, updateAsset, deleteAsset } = useAssets();
  const { searchQuery, setSearchQuery, searchResults } = useSearch();
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filterOpen, setFilterOpen] = useState(false);
  
  // Log assets for debugging
  useEffect(() => {
    console.log("Assets in DashboardView:", assets);
  }, [assets]);

  const handleAssetClick = (asset: Asset) => {
    setSelectedAsset(asset);
  };

  const handleAssetUpdate = (id: string, updates: Partial<Asset>) => {
    updateAsset(id, updates);
    // Also update the selected asset if it's the one being edited
    if (selectedAsset && selectedAsset.id === id) {
      setSelectedAsset({
        ...selectedAsset,
        ...updates,
      });
    }
  };

  const handleAssetDelete = (id: string) => {
    deleteAsset(id);
    setSelectedAsset(null);
  };

  return (
    <div className="space-y-6 px-1">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <p className="text-muted-foreground">Total: {assets.length} assets</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <input
              type="search"
              placeholder="Search assets..."
              className="w-full md:w-64 h-9 rounded-md border border-input bg-background px-3 py-1 pl-9 text-sm focus:outline-none focus:ring-1 focus:ring-ring"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <button
            onClick={() => setFilterOpen(!filterOpen)}
            className={`p-2 rounded-md ${
              filterOpen ? "bg-secondary" : "hover:bg-secondary"
            }`}
          >
            <SlidersHorizontal className="h-5 w-5" />
          </button>
          
          <div className="flex rounded-md border border-border">
            <button
              onClick={() => setViewMode("grid")}
              className={`p-2 ${
                viewMode === "grid"
                  ? "bg-secondary text-primary"
                  : "hover:bg-secondary/70"
              }`}
            >
              <Grid className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`p-2 ${
                viewMode === "list"
                  ? "bg-secondary text-primary"
                  : "hover:bg-secondary/70"
              }`}
            >
              <List className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
      
      {filterOpen && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-border rounded-md">
          <div>
            <label className="block text-sm font-medium mb-1">File Type</label>
            <select className="w-full h-9 rounded-md border border-input bg-background px-3 text-sm">
              <option value="">All Types</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="audio">Audio</option>
              <option value="document">Documents</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Date Uploaded</label>
            <select className="w-full h-9 rounded-md border border-input bg-background px-3 text-sm">
              <option value="">Any Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Collection</label>
            <select className="w-full h-9 rounded-md border border-input bg-background px-3 text-sm">
              <option value="">All Collections</option>
            </select>
          </div>
        </div>
      )}
      
      {assets.length === 0 ? (
        <div className="flex flex-col items-center justify-center text-center bg-secondary/50 rounded-lg p-12">
          <div className="rounded-full bg-secondary p-5 mb-4">
            <Grid className="h-12 w-12 text-muted-foreground" />
          </div>
          <h2 className="text-2xl font-semibold mb-2">No assets yet</h2>
          <p className="text-muted-foreground mb-6">
            Upload your first assets to get started with your digital asset management
          </p>
          <a
            href="/upload"
            className="inline-flex items-center px-4 py-2 rounded-md bg-primary text-primary-foreground"
          >
            Upload Assets
          </a>
        </div>
      ) : (
        <AssetGrid 
          assets={searchResults.length > 0 ? searchResults : assets}
          onAssetClick={handleAssetClick}
        />
      )}
      
      {selectedAsset && (
        <AssetDetails
          asset={selectedAsset}
          onClose={() => setSelectedAsset(null)}
          onUpdate={handleAssetUpdate}
          onDelete={handleAssetDelete}
        />
      )}
    </div>
  );
}
