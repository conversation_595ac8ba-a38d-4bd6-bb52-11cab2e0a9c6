'use client'

import React from "react";
import Link from "next/link";
import { Heart, Github, ExternalLink } from "lucide-react";

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="px-4 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* Left side - Brand and description */}
          <div className="flex flex-col items-center md:items-start text-center md:text-left">
            <div className="flex items-center space-x-2 mb-2">
              <span className="font-semibold text-foreground">Asset Hub</span>
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                Digital Asset Manager
              </span>
            </div>
            <p className="text-sm text-muted-foreground max-w-md">
              Powerful digital asset management system for organizing, searching, and managing your media files.
            </p>
          </div>

          {/* Center - Quick links */}
          <div className="flex flex-col md:flex-row items-center gap-6">
            <div className="flex items-center space-x-6 text-sm">
              <Link 
                href="/settings" 
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                Settings
              </Link>
              <Link 
                href="/help" 
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                Help
              </Link>
              <Link 
                href="/about" 
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                About
              </Link>
            </div>
          </div>

          {/* Right side - Copyright and social */}
          <div className="flex flex-col items-center md:items-end text-center md:text-right">
            <div className="flex items-center space-x-4 mb-2">
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-foreground transition-colors"
                title="GitHub"
              >
                <Github className="h-4 w-4" />
              </a>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-foreground transition-colors"
                title="Documentation"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>© {currentYear} Asset Hub. Made with</span>
              <Heart className="h-3 w-3 mx-1 text-red-500" />
              <span>for digital creators</span>
            </div>
          </div>
        </div>

        {/* Bottom section - Additional info */}
        <div className="mt-6 pt-4 border-t border-border/50">
          <div className="flex flex-col md:flex-row justify-between items-center gap-2 text-xs text-muted-foreground">
            <div className="flex items-center space-x-4">
              <span>Version 1.0.0</span>
              <span>•</span>
              <span>Built with Next.js & TypeScript</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/privacy" className="hover:text-foreground transition-colors">
                Privacy Policy
              </Link>
              <span>•</span>
              <Link href="/terms" className="hover:text-foreground transition-colors">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
