'use client'

import React, { useState, useRef } from "react";
import { Search, Menu, Upload, <PERSON>, <PERSON><PERSON><PERSON>, User } from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useHotkeys } from "react-hotkeys-hook";

interface HeaderProps {
  toggleSidebar: () => void;
}

export function Header({ toggleSidebar }: HeaderProps) {
  const pathname = usePathname();
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const searchRef = useRef<HTMLInputElement>(null);

  // Focus search input when pressing Ctrl+K
  useHotkeys("ctrl+k", (e) => {
    e.preventDefault();
    setSearchOpen(true);
    setTimeout(() => searchRef.current?.focus(), 0);
  });

  return (
    <header className="border-b border-border bg-background px-4 py-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <button 
            onClick={toggleSidebar}
            className="mr-4 rounded-md p-2 hover:bg-secondary"
          >
            <Menu className="h-5 w-5" />
          </button>
          
          <nav className="hidden md:block">
            <ul className="flex space-x-6">
              <li>
                <Link 
                  href="/" 
                  className={cn(
                    "text-sm font-medium transition-colors hover:text-primary",
                    pathname === "/" ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  Dashboard
                </Link>
              </li>
              <li>
                <Link 
                  href="/collections" 
                  className={cn(
                    "text-sm font-medium transition-colors hover:text-primary",
                    pathname === "/collections" || pathname.startsWith("/collections/") 
                      ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  Collections
                </Link>
              </li>
              <li>
                <Link 
                  href="/recent" 
                  className={cn(
                    "text-sm font-medium transition-colors hover:text-primary",
                    pathname === "/recent" ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  Recent
                </Link>
              </li>
            </ul>
          </nav>
        </div>

        <div className="flex items-center space-x-2">
          {searchOpen ? (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <input
                ref={searchRef}
                type="search"
                placeholder="Search assets... (Ctrl+K)"
                className="h-9 w-64 rounded-md border border-input bg-background px-3 py-1 pl-9 text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onBlur={() => {
                  if (!searchQuery) setSearchOpen(false);
                }}
              />
            </div>
          ) : (
            <button
              onClick={() => setSearchOpen(true)}
              className="inline-flex items-center rounded-md px-3 py-1.5 text-sm font-medium text-muted-foreground hover:bg-secondary"
            >
              <Search className="mr-2 h-4 w-4" />
              <span className="hidden md:inline">Search... (Ctrl+K)</span>
            </button>
          )}
          
          <Link 
            href="/upload"
            className="inline-flex items-center rounded-md bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground hover:bg-primary hover:bg-opacity-90"
          >
            <Upload className="mr-2 h-4 w-4" />
            <span className="hidden md:inline">Upload</span>
          </Link>
          
          <button className="rounded-md p-2 text-muted-foreground hover:bg-secondary">
            <Bell className="h-5 w-5" />
          </button>

          <button className="rounded-md p-2 text-muted-foreground hover:bg-secondary">
            <Settings className="h-5 w-5" />
          </button>

          <button className="rounded-md p-2 text-muted-foreground hover:bg-secondary">
            <User className="h-5 w-5" />
          </button>
        </div>
      </div>
    </header>
  );
}
