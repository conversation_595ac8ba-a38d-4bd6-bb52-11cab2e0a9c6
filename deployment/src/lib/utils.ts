import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { AssetType } from "./types";
import { parse } from 'exifr';
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
export function formatDate(date: string): string {
  const d = new Date(date);
  return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
}
export function getFileType(filename: string): AssetType {
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp'];
  const videoExtensions = ['mp4', 'webm', 'mov', 'avi', 'wmv', 'flv', 'mkv'];
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac'];
  const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'rtf'];
  if (imageExtensions.includes(extension)) return 'image';
  if (videoExtensions.includes(extension)) return 'video';
  if (audioExtensions.includes(extension)) return 'audio';
  if (documentExtensions.includes(extension)) return 'document';
  return 'other';
}

// Function to create a data URL from a file
export const createFileUrl = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => {
      console.error('Error creating file URL:', error);
      reject(error);
    };
    reader.readAsDataURL(file);
  });
};

// Create a thumbnail URL for different asset types
export const createThumbnailUrl = async (file: File): Promise<string> => {
  // Use file.type to determine the type more accurately
  const fileType = file.type.startsWith('image/') ? 'image' : file.type.startsWith('video/') ? 'video' : file.type.startsWith('audio/') ? 'audio' : 'document';
  if (fileType === 'image') {
    // For images, create a resized thumbnail to save space
    try {
      // If the image is too large, use a default thumbnail instead
      if (file.size > 5000000) {
        // 5MB limit
        return `https://images.unsplash.com/photo-1533827432537-70133748f5c8?q=80&w=200&auto=format&fit=crop`;
      }
      return await createFileUrl(file);
    } catch (error) {
      console.error('Error creating image thumbnail:', error);
      return `https://images.unsplash.com/photo-1533827432537-70133748f5c8?q=80&w=200&auto=format&fit=crop`;
    }
  }

  // Default thumbnails for other file types
  const defaultThumbnails: Record<AssetType, string> = {
    video: 'https://images.unsplash.com/photo-1612538498456-e861df91d4d0?q=80&w=200&auto=format&fit=crop',
    audio: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?q=80&w=200&auto=format&fit=crop',
    document: "https://picsum.photos/200",
    other: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?q=80&w=200&auto=format&fit=crop',
    image: 'https://images.unsplash.com/photo-1533827432537-70133748f5c8?q=80&w=200&auto=format&fit=crop'
  };
  return defaultThumbnails[fileType];
};

// Extract EXIF metadata from image files
export const extractImageMetadata = async (file: File): Promise<Record<string, any>> => {
  if (!file.type.startsWith('image/')) {
    return {};
  }
  try {
    // Full extraction with all metadata including EXIF, IPTC, XMP, and more
    const metadata = await parse(file, {
      tiff: true,
      xmp: true,
      iptc: true,
      icc: true
    });
    console.log('Extracted metadata:', metadata);
    return metadata || {};
  } catch (error) {
    console.error('Error extracting EXIF metadata:', error);
    return {};
  }
};

// Convert file to base64 for AI processing
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};