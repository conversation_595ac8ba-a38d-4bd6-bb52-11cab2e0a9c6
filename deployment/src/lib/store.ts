'use client'

import { useEffect, useState } from "react";
import { Asset, Collection } from "./types";
import { v4 as uuidv4 } from "uuid";

// Helper functions for localStorage interactions
const getFromStorage = <T>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') return defaultValue;
  
  try {
    // Only access localStorage on client side
    const stored = typeof localStorage !== 'undefined' ? localStorage.getItem(key) : null;
    if (!stored) return defaultValue;
    return JSON.parse(stored) as T;
  } catch (error) {
    console.error(`Error parsing ${key} from localStorage:`, error);
    return defaultValue;
  }
};

const setInStorage = <T>(key: string, value: T): void => {
  if (typeof window === 'undefined') return;
  try {
    // Only access localStorage on client side
    if (typeof localStorage !== 'undefined') {
      // Try to stringify the value first to catch any circular reference errors
      const stringified = JSON.stringify(value);
      
      // Check if the stringified value is too large for localStorage
      // Most browsers have a 5MB limit, but we'll be conservative
      if (stringified.length > 4000000) { // ~4MB
        console.error(`Value for ${key} is too large for localStorage (${(stringified.length/1024/1024).toFixed(2)}MB)`);
        return;
      }
      
      try {
        localStorage.setItem(key, stringified);
        console.log(`Stored ${key} in localStorage (${(stringified.length/1024).toFixed(2)}KB)`);
      } catch (storageError) {
        console.error(`LocalStorage error: ${storageError instanceof Error ? storageError.message : String(storageError)}`);
        if (storageError instanceof DOMException && (
          // everything except Firefox
          storageError.code === 22 ||
          // Firefox
          storageError.code === 1014 ||
          // test name field too, because code might not be present
          storageError.name === 'QuotaExceededError' ||
          storageError.name === 'NS_ERROR_DOM_QUOTA_REACHED'
        )) {
          console.error('localStorage quota exceeded');
          // Maybe implement a fallback or cleanup old data
        }
      }
    }
  } catch (error) {
    console.error(`Error preparing ${key} for localStorage:`, error);
  }
};

// Hooks for assets
export function useAssets() {
  const [assets, setAssets] = useState<Asset[]>([]);
  
  useEffect(() => {
    // Get assets from localStorage whenever the component mounts
    const storedAssets = getFromStorage<Asset[]>("assets", []);
    console.log("Loaded assets from localStorage:", storedAssets);
    setAssets(storedAssets);
  }, []);
  
  const addAsset = (asset: Omit<Asset, "id" | "createdAt" | "updatedAt">) => {
    const now = new Date().toISOString();
    const newAsset: Asset = {
      ...asset,
      id: uuidv4(),
      createdAt: now,
      updatedAt: now
    };
    
    const updatedAssets = [...assets, newAsset];
    setAssets(updatedAssets);
    setInStorage("assets", updatedAssets);
    return newAsset;
  };
  
  const updateAsset = (id: string, updates: Partial<Asset>) => {
    const updatedAssets = assets.map(asset => 
      asset.id === id 
        ? { ...asset, ...updates, updatedAt: new Date().toISOString() } 
        : asset
    );
    setAssets(updatedAssets);
    setInStorage("assets", updatedAssets);
  };
  
  const deleteAsset = (id: string) => {
    const updatedAssets = assets.filter(asset => asset.id !== id);
    setAssets(updatedAssets);
    setInStorage("assets", updatedAssets);
  };
  
  const getAsset = (id: string) => {
    return assets.find(asset => asset.id === id);
  };
  
  return {
    assets,
    addAsset,
    updateAsset,
    deleteAsset,
    getAsset
  };
}

// Hooks for collections
export function useCollections() {
  const [collections, setCollections] = useState<Collection[]>([]);
  
  useEffect(() => {
    setCollections(getFromStorage<Collection[]>("collections", []));
  }, []);
  
  const addCollection = (collection: Omit<Collection, "id" | "createdAt" | "updatedAt">) => {
    const now = new Date().toISOString();
    const newCollection: Collection = {
      ...collection,
      id: uuidv4(),
      createdAt: now,
      updatedAt: now
    };
    
    const updatedCollections = [...collections, newCollection];
    setCollections(updatedCollections);
    setInStorage("collections", updatedCollections);
    return newCollection;
  };
  
  const updateCollection = (id: string, updates: Partial<Collection>) => {
    const updatedCollections = collections.map(collection => 
      collection.id === id 
        ? { ...collection, ...updates, updatedAt: new Date().toISOString() } 
        : collection
    );
    setCollections(updatedCollections);
    setInStorage("collections", updatedCollections);
  };
  
  const deleteCollection = (id: string) => {
    const updatedCollections = collections.filter(collection => collection.id !== id);
    setCollections(updatedCollections);
    setInStorage("collections", updatedCollections);
  };
  
  const getCollection = (id: string) => {
    return collections.find(collection => collection.id === id);
  };
  
  return {
    collections,
    addCollection,
    updateCollection,
    deleteCollection,
    getCollection
  };
}

// Hook for search functionality
export function useSearch() {
  const { assets } = useAssets();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Asset[]>([]);
  
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults(assets);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const results = assets.filter(asset => 
      asset.title.toLowerCase().includes(query) ||
      asset.description?.toLowerCase().includes(query) ||
      asset.tags.some(tag => tag.toLowerCase().includes(query))
    );
    
    setSearchResults(results);
  }, [searchQuery, assets]);
  
  return {
    searchQuery,
    setSearchQuery,
    searchResults
  };
}
