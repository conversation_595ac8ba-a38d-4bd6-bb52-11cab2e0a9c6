@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #FFFFFF;
    --foreground: #09090B;
    --card: #FFFFFF;
    --card-foreground: #09090B;
    --popover: #FFFFFF;
    --popover-foreground: #09090B;
    --primary: #18181B;
    --primary-foreground: #FAFAFA;
    --secondary: #F4F4F5;
    --secondary-foreground: #18181B;
    --muted: #F4F4F5;
    --muted-foreground: #71717A;
    --accent: #F4F4F5;
    --accent-foreground: #18181B;
    --destructive: #EF4444;
    --destructive-foreground: #FAFAFA;
    --border: #E4E4E7;
    --input: #E4E4E7;
    --ring: #09090B;
    --chart-1: #E86343;
    --chart-2: #2A9187;
    --chart-3: #2F3F4A;
    --chart-4: #D9B64E;
    --chart-5: #E67E33;
    --radius: 0.5rem;

    --sidebar-background: #FAFAFA;
    --sidebar-foreground: #3F3F46;
    --sidebar-primary: #18181B;
    --sidebar-primary-foreground: #FAFAFA;
    --sidebar-accent: #F4F4F5;
    --sidebar-accent-foreground: #18181B;
    --sidebar-border: #E5E7EB;
    --sidebar-ring: #3B82F6;
  }

  .dark {
    --background: #09090B;
    --foreground: #FAFAFA;
    --card: #09090B;
    --card-foreground: #FAFAFA;
    --popover: #09090B;
    --popover-foreground: #FAFAFA;
    --primary: #FAFAFA;
    --primary-foreground: #18181B;
    --secondary: #27272A;
    --secondary-foreground: #FAFAFA;
    --muted: #27272A;
    --muted-foreground: #A1A1AA;
    --accent: #27272A;
    --accent-foreground: #FAFAFA;
    --destructive: #7F1D1D;
    --destructive-foreground: #FAFAFA;
    --border: #27272A;
    --input: #27272A;
    --ring: #D4D4D8;
    --chart-1: #3B82F6;
    --chart-2: #2DD4BF;
    --chart-3: #FB923C;
    --chart-4: #C084FC;
    --chart-5: #F87171;

    --sidebar-background: #18181B;
    --sidebar-foreground: #F4F4F5;
    --sidebar-primary: #3B82F6;
    --sidebar-primary-foreground: #FFFFFF;
    --sidebar-accent: #27272A;
    --sidebar-accent-foreground: #F4F4F5;
    --sidebar-border: #27272A;
    --sidebar-ring: #3B82F6;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

nextjs-portal {
  display: none;
}