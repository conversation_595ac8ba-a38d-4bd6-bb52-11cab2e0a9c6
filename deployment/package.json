{"name": "creatr", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev", "lint": "eslint", "lint:fix": "eslint . --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "test": "jest", "test:types": "tsc --noEmit --pretty", "test:all": "pnpm run test && pnpm run test:types", "db:generate": "npx drizzle-kit generate && npx drizzle-kit migrate && git add . && git commit -m 'chore: update migration'"}, "dependencies": {"@jest/globals": "^29.7.0", "@types/eslint": "^8.56.10", "@types/jest": "^29.5.14", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "error-stack-parser": "^2.1.4", "eslint": "^8.57.0", "eslint-config-next": "^15.0.1", "eslint-import-resolver-node": "^0.3.9", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-creatr": "latest", "exifr": "^7.1.3", "framer-motion": "^12.10.5", "geist": "^1.3.0", "lucide-react": "^0.468.0", "next": "^15.0.1", "next-themes": "^0.4.4", "postcss": "^8.4.39", "postcss-syntax": "^0.36.2", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hotkeys-hook": "^5.0.1", "recharts": "^2.15.0", "source-map": "^0.7.4", "stylelint": "^16.13.0", "stylelint-config-standard": "^37.0.0", "stylelint-config-tailwindcss": "^0.0.7", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.2.5", "typescript": "^5.5.3", "uuid": "^11.1.0", "zustand": "^5.0.2"}, "overrides": {"drizzle-orm": "0.41.0-29155cc"}}