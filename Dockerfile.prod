# Build stage
FROM node:20-alpine AS builder
WORKDIR /app

# Install build dependencies
RUN apk update && apk add --no-cache build-base python3 py3-pip

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine AS runner
WORKDIR /app

# Install production dependencies
RUN apk update && apk add --no-cache cifs-utils wget sudo samba-client

# Set environment to production
ENV NODE_ENV=production

# Copy necessary files from builder
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/package*.json ./

# Copy credentials file if it exists
COPY .smbcredentials /app/.smbcredentials

# Install production dependencies only
RUN npm ci --only=production && npm install -g tsx

# Create mount point
RUN mkdir -p /storage/photos

# Copy start script
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh

# Expose the port the app runs on
EXPOSE 8080

# Start the application
CMD ["/app/start.sh"] 