'use client'

import { useEffect, useState } from 'react';

export default function SimpleTestPage() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    console.log('SimpleTest useEffect running...');
    
    const fetchData = async () => {
      try {
        console.log('Starting fetch...');
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/photos?limit=3&offset=0');
        console.log('Response received:', response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }
        
        const result = await response.json();
        console.log('Data parsed:', result);
        setData(result);
      } catch (err) {
        console.error('Error:', err);
        setError(err instanceof Error ? err.message : String(err));
      } finally {
        console.log('Setting loading to false');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  console.log('Render - loading:', loading, 'error:', error, 'data:', !!data);

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Simple Test</h1>
      
      <div className="space-y-4">
        <div>
          <strong>Loading:</strong> {loading ? 'TRUE' : 'FALSE'}
        </div>
        
        <div>
          <strong>Error:</strong> {error || 'None'}
        </div>
        
        <div>
          <strong>Data:</strong> {data ? 'Loaded' : 'None'}
        </div>
        
        {data && (
          <div>
            <strong>Assets Count:</strong> {data.assets?.length || 0}
          </div>
        )}
        
        {data && (
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        )}
      </div>
    </div>
  );
} 