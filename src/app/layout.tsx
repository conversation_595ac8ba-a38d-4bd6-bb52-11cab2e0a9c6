import "@/styles/globals.css";
import React from "react";

import { Geist<PERSON><PERSON> } from "geist/font/sans";
import { type Metadata } from "next";
import { AppLayout } from "@/components/layout/AppLayout";
import { ClientErrorHandler } from "@/components/layout/ClientErrorHandler";
import { BetterAuthGuard } from "@/components/auth/BetterAuthGuard";
import { ServiceWorkerRegistration } from "@/components/performance/ServiceWorkerRegistration";

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export const metadata: Metadata = {
  title: {
    default: "DAM System",
    template: "%s | DAM System",
  },
  description: "Digital Asset Management system inspired by Phraseanet",
  applicationName: "DAM System",
  keywords: ["DAM", "digital asset management", "media library", "phraseanet"],
  authors: [{ name: "DAM Team" }],
  creator: "DAM Team",
  publisher: "DAM Team",
  icons: {
    icon: [
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/favicon.ico", sizes: "48x48", type: "image/x-icon" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
  },
  manifest: "/site.webmanifest",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "DAM System",
  },
  formatDetection: {
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${GeistSans.variable}`}>
      <body className="bg-background text-foreground">
        <ServiceWorkerRegistration />
        <ClientErrorHandler>
          <AppLayout>{children}</AppLayout>
        </ClientErrorHandler>
      </body>
    </html>
  );
}
