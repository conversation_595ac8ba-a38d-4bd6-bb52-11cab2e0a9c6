'use client'

import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';

export default function HomePage() {
  useEffect(() => {
    console.log('🏠 HomePage: Checking authentication...');

    // Check authentication status
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    const user = localStorage.getItem('user');

    if (isAuthenticated === 'true' && user) {
      console.log('🏠 HomePage: User authenticated, redirecting to dashboard');
      window.location.href = '/dashboard';
    } else {
      console.log('🏠 HomePage: User not authenticated, redirecting to login');
      window.location.href = '/login';
    }
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="flex items-center gap-2">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span>Redirecting...</span>
      </div>
    </div>
  );
}
