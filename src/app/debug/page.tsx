'use client'

import { useAssets } from '@/lib/store';

export default function DebugPage() {
  const { assets, loading, error, scanProgress } = useAssets();

  return (
    <div className="p-4 space-y-4">
      <h1 className="text-2xl font-bold">Debug Page</h1>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-bold">Loading State:</h2>
        <p>{loading ? 'TRUE' : 'FALSE'}</p>
      </div>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-bold">Error State:</h2>
        <p>{error || 'None'}</p>
      </div>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-bold">Assets Count:</h2>
        <p>{assets.length}</p>
      </div>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-bold">Scan Progress:</h2>
        <p>Total: {scanProgress.total}</p>
        <p>Current: {scanProgress.current}</p>
        <p>Indexing: {scanProgress.isIndexing ? 'TRUE' : 'FALSE'}</p>
      </div>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-bold">First 3 Assets:</h2>
        <pre>{JSON.stringify(assets.slice(0, 3), null, 2)}</pre>
      </div>
    </div>
  );
} 