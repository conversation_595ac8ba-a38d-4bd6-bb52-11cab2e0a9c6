'use client'

'use client'

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { useAssets } from "@/lib/store";
import { formatBytes, formatDate } from "@/lib/utils";
import { ArrowLeft, Download, Pencil, Trash, Info } from "lucide-react";
import Link from "next/link";
import { Asset } from "@/lib/types";

import Image from 'next/image';

export default function AssetDetailsPage() {
  const params = useParams();
  const { getAsset, updateAsset, deleteAsset } = useAssets();
  const [asset, setAsset] = useState<Asset | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedAsset, setEditedAsset] = useState<Partial<Asset>>({});
  
  useEffect(() => {
    if (params?.id) {
      const foundAsset = getAsset(params.id as string);
      setAsset(foundAsset || null);
      if (foundAsset) {
        setEditedAsset({
          title: foundAsset.title,
          description: foundAsset.description,
          tags: [...foundAsset.tags],
        });
      }
    }
  }, [params, getAsset]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedAsset(prev => ({ ...prev, [name]: value }));
  };
  
  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsString = e.target.value;
    const tagsArray = tagsString.split(',').map(tag => tag.trim());
    setEditedAsset(prev => ({ ...prev, tags: tagsArray }));
  };
  
  const handleSave = () => {
    if (asset) {
      updateAsset(asset.id, editedAsset);
      setAsset({ ...asset, ...editedAsset });
      setIsEditing(false);
    }
  };
  
  const handleDelete = () => {
    if (asset) {
      deleteAsset(asset.id);
      window.location.href = "/";
    }
  };
  
  if (!asset) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <p className="text-xl">Asset not found</p>
        <Link href="/" className="text-primary mt-4">
          Return to dashboard
        </Link>
      </div>
    );
  }
  
  const renderAssetPreview = () => {
    switch (asset.type) {
      case 'image':
        return <Image src={asset.fileUrl} alt={asset.title} className="max-w-full rounded-lg" width={500} height={300} />;
      case 'video':
        return (
          <video controls className="w-full max-h-[70vh] rounded-lg">
            <source src={asset.fileUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        );
      case 'audio':
        return (
          <div className="p-8 bg-muted/20 rounded-lg">
            <audio controls className="w-full">
              <source src={asset.fileUrl} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        );
      default:
        return (
          <div className="flex items-center justify-center h-64 bg-muted/20 rounded-lg">
            <p className="text-muted-foreground">Preview not available</p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/" className="mr-4 p-2 rounded-full hover:bg-secondary">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-semibold">{asset.title}</h1>
            <p className="text-muted-foreground">{asset.filename}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="inline-flex items-center rounded-md bg-secondary px-3 py-1.5 text-sm font-medium hover:bg-secondary hover:bg-opacity-80"
          >
            <Pencil className="mr-2 h-4 w-4" />
            {isEditing ? "Cancel" : "Edit"}
          </button>
          <button
            onClick={handleDelete}
            className="inline-flex items-center rounded-md bg-destructive px-3 py-1.5 text-sm font-medium text-destructive-foreground hover:bg-destructive hover:bg-opacity-90"
          >
            <Trash className="mr-2 h-4 w-4" />
            Delete
          </button>
          <button
            onClick={() => {
              const link = document.createElement('a');
              link.href = asset.fileUrl;
              link.download = asset.filename;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }}
            className="inline-flex items-center rounded-md bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground hover:bg-primary hover:bg-opacity-90"
          >
            <Download className="mr-2 h-4 w-4" />
            Download
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 flex justify-center">
          {renderAssetPreview()}
        </div>
        
        <div className="space-y-4">
          {isEditing ? (
            <div className="space-y-4 border border-border rounded-lg p-4">
              <div>
                <label className="block text-sm font-medium mb-1">Title</label>
                <input
                  type="text"
                  name="title"
                  value={editedAsset.title || ""}
                  onChange={handleInputChange}
                  className="w-full rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <textarea
                  name="description"
                  value={editedAsset.description || ""}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Tags (comma separated)</label>
                <input
                  type="text"
                  value={editedAsset.tags?.join(", ") || ""}
                  onChange={handleTagsChange}
                  className="w-full rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                />
              </div>
              
              <button
                onClick={handleSave}
                className="w-full inline-flex justify-center items-center rounded-md bg-primary px-3 py-2 text-sm font-medium text-primary-foreground hover:bg-primary hover:bg-opacity-90"
              >
                Save Changes
              </button>
            </div>
          ) : (
            <div className="border border-border rounded-lg overflow-hidden">
              <div className="bg-muted/20 px-4 py-2 border-b border-border">
                <h3 className="font-medium">Asset Information</h3>
              </div>
              
              <div className="p-4 space-y-4">
                {asset.description && (
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Description</h4>
                    <p className="mt-1">{asset.description}</p>
                  </div>
                )}
                
                {asset.tags.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Tags</h4>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {asset.tags.map(tag => (
                        <span key={tag} className="px-2 py-1 bg-secondary rounded-md text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">File Details</h4>
                  <div className="mt-2 space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Type:</span>
                      <span className="font-medium capitalize">{asset.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Size:</span>
                      <span className="font-medium">{formatBytes(asset.size)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Created:</span>
                      <span className="font-medium">{formatDate(asset.createdAt)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Modified:</span>
                      <span className="font-medium">{formatDate(asset.updatedAt)}</span>
                    </div>
                  </div>
                </div>
                
                {asset.type === 'image' && Object.keys(asset.metadata).length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-muted-foreground flex items-center">
                      <Info className="h-4 w-4 mr-1" />
                      EXIF Metadata
                    </h4>
                    <div className="mt-2 max-h-60 overflow-y-auto">
                      <table className="w-full text-xs">
                        <tbody>
                          {Object.entries(asset.metadata).map(([key, value]) => {
                            // Skip complex objects or arrays
                            if (typeof value === 'object') return null;
                            
                            return (
                              <tr key={key} className="border-b border-border/30 last:border-0">
                                <td className="py-1 font-medium">{key}</td>
                                <td className="py-1 text-right">{String(value)}</td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          

          
          {!isEditing && (
            <div className="border border-border rounded-lg overflow-hidden">
              <div className="bg-muted/20 px-4 py-2 border-b border-border">
                <h3 className="font-medium">Actions</h3>
              </div>
              <div className="p-4 space-y-2">
                <button className="w-full inline-flex justify-center items-center rounded-md bg-secondary px-3 py-2 text-sm font-medium hover:bg-secondary hover:bg-opacity-70">
                  Add to Collection
                </button>
                <button className="w-full inline-flex justify-center items-center rounded-md bg-secondary px-3 py-2 text-sm font-medium hover:bg-secondary hover:bg-opacity-70">
                  Share Asset
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
