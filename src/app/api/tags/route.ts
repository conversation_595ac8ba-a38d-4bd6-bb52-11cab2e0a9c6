import { NextRequest, NextResponse } from 'next/server';
import { db, tags, assetTags, assets } from '@/lib/db/schema';
import { eq, sql, desc, asc } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

// GET /api/tags - Get all tags with usage counts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeUsage = searchParams.get('includeUsage') === 'true';

    if (includeUsage) {
      // Get tags with usage counts
      const tagsWithUsage = await db
        .select({
          id: tags.id,
          name: tags.name,
          color: tags.color,
          description: tags.description,
          isAutoGenerated: tags.isAutoGenerated,
          createdAt: tags.createdAt,
          updatedAt: tags.updatedAt,
          usageCount: sql<number>`COALESCE(COUNT(${assetTags.assetId}), 0)`.as('usage_count'),
        })
        .from(tags)
        .leftJoin(assetTags, eq(tags.id, assetTags.tagId))
        .groupBy(tags.id, tags.name, tags.color, tags.description, tags.isAutoGenerated, tags.createdAt, tags.updatedAt)
        .orderBy(desc(sql`usage_count`), asc(tags.name));

      return NextResponse.json({
        success: true,
        tags: tagsWithUsage,
      });
    } else {
      // Get all tags without usage counts (faster)
      const allTags = await db
        .select()
        .from(tags)
        .orderBy(asc(tags.name));

      return NextResponse.json({
        success: true,
        tags: allTags,
      });
    }
  } catch (error) {
    console.error('Error fetching tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tags', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// POST /api/tags - Create a new tag
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, color, description, isAutoGenerated = false } = body;

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { error: 'Tag name is required and must be a string' },
        { status: 400 }
      );
    }

    // Check if tag already exists
    const existingTag = await db
      .select()
      .from(tags)
      .where(eq(tags.name, name.toLowerCase().trim()))
      .limit(1);

    if (existingTag.length > 0) {
      return NextResponse.json(
        { error: 'Tag with this name already exists' },
        { status: 409 }
      );
    }

    // Create new tag
    const newTag = {
      id: uuidv4(),
      name: name.toLowerCase().trim(),
      color: color || null,
      description: description || null,
      isAutoGenerated: Boolean(isAutoGenerated),
    };

    const [createdTag] = await db
      .insert(tags)
      .values(newTag)
      .returning();

    return NextResponse.json({
      success: true,
      tag: createdTag,
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating tag:', error);
    return NextResponse.json(
      { error: 'Failed to create tag', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// PUT /api/tags - Update a tag
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, color, description } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Tag ID is required' },
        { status: 400 }
      );
    }

    // Check if tag exists
    const existingTag = await db
      .select()
      .from(tags)
      .where(eq(tags.id, id))
      .limit(1);

    if (existingTag.length === 0) {
      return NextResponse.json(
        { error: 'Tag not found' },
        { status: 404 }
      );
    }

    // Update tag
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (name !== undefined) {
      updateData.name = name.toLowerCase().trim();
    }
    if (color !== undefined) {
      updateData.color = color;
    }
    if (description !== undefined) {
      updateData.description = description;
    }

    const [updatedTag] = await db
      .update(tags)
      .set(updateData)
      .where(eq(tags.id, id))
      .returning();

    return NextResponse.json({
      success: true,
      tag: updatedTag,
    });

  } catch (error) {
    console.error('Error updating tag:', error);
    return NextResponse.json(
      { error: 'Failed to update tag', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// DELETE /api/tags - Delete a tag
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Tag ID is required' },
        { status: 400 }
      );
    }

    // Check if tag exists
    const existingTag = await db
      .select()
      .from(tags)
      .where(eq(tags.id, id))
      .limit(1);

    if (existingTag.length === 0) {
      return NextResponse.json(
        { error: 'Tag not found' },
        { status: 404 }
      );
    }

    // Delete tag (cascade will handle asset_tags)
    await db
      .delete(tags)
      .where(eq(tags.id, id));

    return NextResponse.json({
      success: true,
      message: 'Tag deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting tag:', error);
    return NextResponse.json(
      { error: 'Failed to delete tag', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
