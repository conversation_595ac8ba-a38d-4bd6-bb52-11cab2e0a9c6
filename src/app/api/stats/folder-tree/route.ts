import { NextResponse } from 'next/server';
import { db, folders, assets } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

export interface FolderTreeNode {
  id: string;
  name: string;
  path: string;
  parentPath: string | null;
  fileCount: number;
  imageCount: number;
  totalSize: string;
  lastModified: Date;
  lastScanned: Date;
  children: FolderTreeNode[];
  level: number;
}

export interface FolderTreeStats {
  totalFolders: number;
  totalFiles: number;
  totalImages: number;
  totalSize: string;
  lastUpdated: Date;
}

export async function GET() {
  try {
    console.log('📊 Building folder tree statistics...');

    // Always build from assets table to ensure fresh, up-to-date data
    // The folders table may not be updated when new assets are added
    console.log('📁 Building fresh folder tree from assets table...');
    const result = await buildTreeFromAssets();

    const response = NextResponse.json({
      success: true,
      tree: result.tree,
      stats: result.stats,
      source: 'assets_table',
      message: 'Built from fresh assets data for accurate statistics'
    });

    // Add cache-busting headers to ensure fresh data
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;

  } catch (error) {
    console.error('❌ Error building folder tree:', error);
    const errorResponse = NextResponse.json({
      success: false,
      error: 'Failed to build folder tree statistics',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });

    // Add cache-busting headers
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    errorResponse.headers.set('Pragma', 'no-cache');
    errorResponse.headers.set('Expires', '0');

    return errorResponse;
  }
}

function buildTreeFromFolders(foldersData: any[]): FolderTreeNode[] {
  // Convert database records to tree nodes
  const nodeMap = new Map<string, FolderTreeNode>();
  
  // Create all nodes first
  foldersData.forEach(folder => {
    const node: FolderTreeNode = {
      id: folder.id,
      name: folder.name,
      path: folder.path,
      parentPath: folder.parentPath,
      fileCount: folder.fileCount,
      imageCount: folder.imageCount,
      totalSize: folder.totalSize,
      lastModified: folder.lastModified,
      lastScanned: folder.lastScanned,
      children: [],
      level: 0
    };
    nodeMap.set(folder.path, node);
  });
  
  // Build the tree structure
  const rootNodes: FolderTreeNode[] = [];
  
  nodeMap.forEach(node => {
    if (node.parentPath && nodeMap.has(node.parentPath)) {
      // Add to parent's children
      const parent = nodeMap.get(node.parentPath)!;
      parent.children.push(node);
      node.level = parent.level + 1;
    } else {
      // Root level node
      rootNodes.push(node);
    }
  });
  
  // Sort children by name - most recent first
  const sortChildren = (nodes: FolderTreeNode[]) => {
    nodes.sort((a, b) => {
      // Sort years in descending order (most recent first)
      if (/^\d{4}$/.test(a.name) && /^\d{4}$/.test(b.name)) {
        return parseInt(b.name) - parseInt(a.name);
      }

      // Sort date folders in descending order (most recent first)
      if (/^\d{2}-\d{2}-\d{4}/.test(a.name) && /^\d{2}-\d{2}-\d{4}/.test(b.name)) {
        // Extract dates and compare (DD-MM-YYYY format)
        const parseDate = (name: string) => {
          const datePart = name.split(' ')[0]; // Get "15-06-2025" from "15-06-2025 folder"
          const [day, month, year] = datePart.split('-').map(Number);
          return new Date(year, month - 1, day); // month is 0-based in JavaScript
        };

        const dateA = parseDate(a.name);
        const dateB = parseDate(b.name);
        return dateB.getTime() - dateA.getTime(); // Most recent first
      }

      // Sort month folders in descending order (most recent first)
      if (/^\d{2}[A-Z]{3}\d{4}/.test(a.name) && /^\d{2}[A-Z]{3}\d{4}/.test(b.name)) {
        return b.name.localeCompare(a.name);
      }

      // Default: sort by name descending (most recent first)
      return b.name.localeCompare(a.name);
    });
    nodes.forEach(node => sortChildren(node.children));
  };
  
  sortChildren(rootNodes);
  return rootNodes;
}

function calculateStatsFromFolders(foldersData: any[]): FolderTreeStats {
  const totalFolders = foldersData.length;
  const totalFiles = foldersData.reduce((sum, folder) => sum + folder.fileCount, 0);
  const totalImages = foldersData.reduce((sum, folder) => sum + folder.imageCount, 0);
  const totalSizeNum = foldersData.reduce((sum, folder) => sum + parseInt(folder.totalSize || '0'), 0);
  const lastUpdated = foldersData.reduce((latest, folder) => {
    const folderDate = new Date(folder.lastScanned);
    return folderDate > latest ? folderDate : latest;
  }, new Date(0));
  
  return {
    totalFolders,
    totalFiles,
    totalImages,
    totalSize: totalSizeNum.toString(),
    lastUpdated
  };
}

async function buildTreeFromAssets(): Promise<{ tree: FolderTreeNode[], stats: FolderTreeStats }> {
  // Get all assets with their file paths
  const assetsData = await db.select({
    filePath: assets.filePath,
    type: assets.type,
    size: assets.size,
    lastModified: assets.lastModified
  }).from(assets);

  console.log(`📁 Processing ${assetsData.length} assets to build folder tree...`);

  // Build folder structure from file paths
  const folderMap = new Map<string, {
    fileCount: number;
    imageCount: number;
    totalSize: number;
    lastModified: Date;
    files: string[];
  }>();

  // Process each asset to extract folder information
  assetsData.forEach(asset => {
    const pathParts = asset.filePath.split('/');

    // Build all parent folder paths
    for (let i = 1; i <= pathParts.length - 1; i++) {
      const folderPath = pathParts.slice(0, i).join('/');
      if (!folderPath) continue;

      if (!folderMap.has(folderPath)) {
        folderMap.set(folderPath, {
          fileCount: 0,
          imageCount: 0,
          totalSize: 0,
          lastModified: new Date(0),
          files: []
        });
      }

      const folderStats = folderMap.get(folderPath)!;
      folderStats.fileCount++;
      if (asset.type === 'image') {
        folderStats.imageCount++;
      }
      folderStats.totalSize += asset.size;
      if (asset.lastModified > folderStats.lastModified) {
        folderStats.lastModified = asset.lastModified;
      }
      folderStats.files.push(asset.filePath);
    }
  });

  // Convert to tree nodes
  const nodeMap = new Map<string, FolderTreeNode>();

  folderMap.forEach((stats, path) => {
    const pathParts = path.split('/');
    const name = pathParts[pathParts.length - 1];
    const parentPath = pathParts.length > 1 ? pathParts.slice(0, -1).join('/') : null;

    const node: FolderTreeNode = {
      id: `folder-${path}`,
      name,
      path,
      parentPath,
      fileCount: stats.fileCount,
      imageCount: stats.imageCount,
      totalSize: stats.totalSize.toString(),
      lastModified: stats.lastModified,
      lastScanned: new Date(),
      children: [],
      level: 0
    };

    nodeMap.set(path, node);
  });

  // Build tree structure
  const rootNodes: FolderTreeNode[] = [];

  nodeMap.forEach(node => {
    if (node.parentPath && nodeMap.has(node.parentPath)) {
      const parent = nodeMap.get(node.parentPath)!;
      parent.children.push(node);
      node.level = parent.level + 1;
    } else {
      rootNodes.push(node);
    }
  });

  // Sort children - most recent first
  const sortChildren = (nodes: FolderTreeNode[]) => {
    nodes.sort((a, b) => {
      // Sort years in descending order (most recent first)
      if (/^\d{4}$/.test(a.name) && /^\d{4}$/.test(b.name)) {
        return parseInt(b.name) - parseInt(a.name);
      }

      // Sort date folders in descending order (most recent first)
      if (/^\d{2}-\d{2}-\d{4}/.test(a.name) && /^\d{2}-\d{2}-\d{4}/.test(b.name)) {
        // Extract dates and compare (DD-MM-YYYY format)
        const parseDate = (name: string) => {
          const datePart = name.split(' ')[0]; // Get "15-06-2025" from "15-06-2025 folder"
          const [day, month, year] = datePart.split('-').map(Number);
          return new Date(year, month - 1, day); // month is 0-based in JavaScript
        };

        const dateA = parseDate(a.name);
        const dateB = parseDate(b.name);
        return dateB.getTime() - dateA.getTime(); // Most recent first
      }

      // Sort month folders in descending order (most recent first)
      if (/^\d{2}[A-Z]{3}\d{4}/.test(a.name) && /^\d{2}[A-Z]{3}\d{4}/.test(b.name)) {
        return b.name.localeCompare(a.name);
      }

      // Default: sort by name descending (most recent first)
      return b.name.localeCompare(a.name);
    });
    nodes.forEach(node => sortChildren(node.children));
  };

  sortChildren(rootNodes);

  // Calculate overall stats
  const stats: FolderTreeStats = {
    totalFolders: folderMap.size,
    totalFiles: assetsData.length,
    totalImages: assetsData.filter(a => a.type === 'image').length,
    totalSize: assetsData.reduce((sum, a) => sum + a.size, 0).toString(),
    lastUpdated: new Date()
  };

  return { tree: rootNodes, stats };
}
