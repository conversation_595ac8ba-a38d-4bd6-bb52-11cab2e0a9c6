import { NextResponse } from 'next/server';
import { db, assets } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

export interface FileTypeStats {
  extension: string;
  count: number;
  totalSize: number;
  percentage: number;
}

export interface FileTypesResponse {
  success: boolean;
  fileTypes: FileTypeStats[];
  totalFiles: number;
  totalSize: number;
  lastUpdated: Date;
  error?: string;
}

export async function GET() {
  try {
    console.log('📊 Building file type statistics (JavaScript approach)...');

    // Use a simpler approach - get all filenames and process them in JavaScript
    console.log('📊 About to query database for all files...');
    const allFiles = await db
      .select({
        filename: assets.filename,
        size: assets.size
      })
      .from(assets);

    console.log(`📊 Retrieved ${allFiles?.length || 0} files from database`);
    console.log(`📊 allFiles type: ${typeof allFiles}, isArray: ${Array.isArray(allFiles)}`);

    // Process file extensions in JavaScript
    const extensionStats = new Map<string, { count: number; totalSize: number }>();
    let totalFiles = 0;
    let totalSize = 0;

    if (!allFiles || allFiles.length === 0) {
      console.log('📊 No files found in database');
      const response = NextResponse.json({
        success: true,
        fileTypes: [],
        totalFiles: 0,
        totalSize: 0,
        lastUpdated: new Date()
      });

      // Add cache-busting headers
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');

      return response;
    }

    for (const file of allFiles) {
      totalFiles++;
      totalSize += file.size || 0;

      // Extract extension
      const filename = file.filename || '';
      const lastDotIndex = filename.lastIndexOf('.');

      let extension = 'no extension';
      if (lastDotIndex > 0 && lastDotIndex < filename.length - 1) {
        extension = filename.substring(lastDotIndex + 1).toLowerCase();
        // Filter out very long extensions (likely not real extensions)
        if (extension.length > 10) {
          extension = 'no extension';
        }
      }

      const current = extensionStats.get(extension) || { count: 0, totalSize: 0 };
      current.count++;
      current.totalSize += file.size || 0;
      extensionStats.set(extension, current);
    }

    // Convert to array and sort by count
    console.log(`📊 extensionStats type: ${typeof extensionStats}, size: ${extensionStats?.size}`);

    if (!extensionStats || typeof extensionStats.entries !== 'function') {
      console.error('❌ extensionStats is not a valid Map:', extensionStats);
      throw new Error('extensionStats is not a valid Map');
    }

    const fileTypes: FileTypeStats[] = Array.from(extensionStats.entries())
      .map(([extension, stats]) => ({
        extension,
        count: stats.count,
        totalSize: stats.totalSize,
        percentage: totalFiles > 0 ? (stats.count / totalFiles) * 100 : 0
      }))
      .sort((a, b) => b.count - a.count);

    console.log(`📊 Found ${fileTypes.length} different file types from ${totalFiles} total files`);

    const response = NextResponse.json({
      success: true,
      fileTypes,
      totalFiles,
      totalSize,
      lastUpdated: new Date()
    });

    // Add cache-busting headers
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;

  } catch (error) {
    console.error('❌ Error building file type statistics:', error);
    const errorResponse = NextResponse.json({
      success: false,
      error: 'Failed to build file type statistics',
      details: error instanceof Error ? error.message : String(error),
      fileTypes: [],
      totalFiles: 0,
      totalSize: 0,
      lastUpdated: new Date()
    }, { status: 500 });

    // Add cache-busting headers
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    errorResponse.headers.set('Pragma', 'no-cache');
    errorResponse.headers.set('Expires', '0');

    return errorResponse;
  }
}
