import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

const indexingService = IndexingService.getInstance();

export async function GET() {
  try {
    console.log('🔍 Debug: Force indexing 2025 directory');
    
    // Trigger fast indexing of recent files (which should include 2025)
    console.log('🚀 Starting fast indexing of recent files...');
    await indexingService.fastIndexRecentFiles(7); // Last 7 days
    
    // Get the latest assets after indexing
    console.log('📊 Getting latest assets after indexing...');
    const assets = await indexingService.getLatestAssets(10, 0);
    
    const debugInfo = assets.map(asset => ({
      id: asset.id,
      filename: asset.filename,
      lastModified: {
        iso: asset.lastModified.toISOString(),
        year: asset.lastModified.getFullYear(),
        readable: asset.lastModified.toLocaleString()
      },
      createdAt: {
        iso: asset.createdAt.toISOString(),
        year: asset.createdAt.getFullYear(),
        readable: asset.createdAt.toLocaleString()
      }
    }));
    
    console.log('🔍 Latest assets after forced indexing:', debugInfo);
    
    return NextResponse.json({
      success: true,
      message: 'Forced indexing of recent files completed',
      latestAssets: debugInfo,
      currentTime: {
        iso: new Date().toISOString(),
        readable: new Date().toLocaleString()
      }
    });
  } catch (error) {
    console.error('Error in force index endpoint:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
