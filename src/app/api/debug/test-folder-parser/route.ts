import { NextResponse } from 'next/server';
import { extractDateFromFolderPath, folderDateToDate, testFolderDateParser } from '@/lib/utils/folderDateParser';

export async function GET() {
  try {
    console.log('🧪 Testing folder date parser...');
    
    // Test the parser
    testFolderDateParser();
    
    // Test with actual recent paths
    const recentPaths = [
      '/mnt/nas/photos/MASTER-PHOTOS/2025/01JAN2025 folder/01-01-2025 folder',
      '/mnt/nas/photos/MASTER-PHOTOS/2025/01JAN2025 folder/24-01-2025 folder',
      '/mnt/nas/photos/MASTER-PHOTOS/2024/12DEC2024 folder',
      '/mnt/nas/photos/MASTER-PHOTOS/2024/06JUN2024 folder',
      '/mnt/nas/photos/MASTER-PHOTOS/2022/12-DEC2022'
    ];

    const results = recentPaths.map(path => {
      const parsed = extractDateFromFolderPath(path);
      return {
        path,
        parsed,
        date: parsed ? folderDateToDate(parsed).toISOString() : null,
        timestamp: parsed ? folderDateToDate(parsed).getTime() : null
      };
    });

    // Sort by timestamp to see newest first
    results.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));

    console.log('📊 Parsed folder dates (newest first):');
    results.forEach(result => {
      if (result.parsed) {
        console.log(`📁 ${result.path}`);
        console.log(`   → ${result.date} (${result.parsed.confidence} confidence)`);
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Folder date parser test completed',
      results,
      sortedByNewest: results.map(r => ({
        path: r.path,
        date: r.date,
        confidence: r.parsed?.confidence,
        source: r.parsed?.source
      }))
    });
  } catch (error) {
    console.error('Error testing folder parser:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
