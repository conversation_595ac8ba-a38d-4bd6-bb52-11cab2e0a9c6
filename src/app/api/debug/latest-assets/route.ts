import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

const indexingService = IndexingService.getInstance();

export async function GET() {
  try {
    console.log('🔍 Debug: Getting latest assets with detailed date info');
    
    // Get the latest 10 assets to inspect their dates
    const assets = await indexingService.getLatestAssets(10, 0);
    
    const debugInfo = assets.map(asset => ({
      id: asset.id,
      filename: asset.filename,
      lastModified: {
        raw: asset.lastModified,
        iso: asset.lastModified.toISOString(),
        timestamp: asset.lastModified.getTime(),
        year: asset.lastModified.getFullYear(),
        readable: asset.lastModified.toLocaleString()
      },
      createdAt: {
        raw: asset.createdAt,
        iso: asset.createdAt.toISOString(),
        timestamp: asset.createdAt.getTime(),
        year: asset.createdAt.getFullYear(),
        readable: asset.createdAt.toLocaleString()
      }
    }));
    
    console.log('🔍 Debug info for latest assets:', debugInfo);
    
    return NextResponse.json({
      success: true,
      message: 'Debug info for latest assets',
      assets: debugInfo,
      currentTime: {
        iso: new Date().toISOString(),
        timestamp: Date.now(),
        readable: new Date().toLocaleString()
      }
    });
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
