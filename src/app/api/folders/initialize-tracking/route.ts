import { NextResponse } from 'next/server';
import { FolderTrackingService } from '@/lib/storage/folderTrackingService';
import { IndexingService } from '@/lib/storage/indexingService';
import fs from 'fs';
import path from 'path';

export async function POST() {
  try {
    console.log('🚀 Initializing folder tracking system...');
    
    const folderTracker = FolderTrackingService.getInstance();
    const indexingService = IndexingService.getInstance();
    
    // Get the base storage path
    const basePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
    
    if (!fs.existsSync(basePath)) {
      return NextResponse.json({
        success: false,
        error: 'Storage path does not exist',
        path: basePath
      }, { status: 400 });
    }
    
    console.log('📁 Scanning existing folder structure...');
    
    // Recursively scan all folders and initialize tracking
    let foldersProcessed = 0;
    let totalFolders = 0;
    
    const scanAndInitialize = async (dirPath: string, depth: number = 0): Promise<void> => {
      if (depth > 6) return; // Reasonable depth limit
      
      try {
        const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
        let hasImageFiles = false;
        
        // Check if this directory has image files
        for (const entry of entries) {
          if (entry.isFile() && isImageFile(entry.name)) {
            hasImageFiles = true;
            break;
          }
        }
        
        // If this directory has image files, initialize tracking for it
        if (hasImageFiles) {
          console.log(`📁 Initializing tracking for: ${path.basename(dirPath)}`);
          await folderTracker.getOrCreateFolder(dirPath);
          foldersProcessed++;
          totalFolders++;
          
          if (foldersProcessed % 10 === 0) {
            console.log(`📊 Processed ${foldersProcessed} folders...`);
          }
        }
        
        // Recursively process subdirectories
        for (const entry of entries) {
          if (entry.isDirectory()) {
            const fullPath = path.join(dirPath, entry.name);
            await scanAndInitialize(fullPath, depth + 1);
          }
        }
        
      } catch (error) {
        console.warn(`Could not process directory ${dirPath}:`, error);
      }
    };
    
    // Helper function to check if file is an image
    function isImageFile(filename: string): boolean {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.svg'];
      const ext = path.extname(filename).toLowerCase();
      return imageExtensions.includes(ext);
    }
    
    // Start the scanning process
    await scanAndInitialize(basePath);
    
    // Get summary of initialized folders
    const allFolders = await folderTracker.getAllFolders();
    const totalImageCount = allFolders.reduce((sum, folder) => sum + folder.imageCount, 0);
    const totalFileCount = allFolders.reduce((sum, folder) => sum + folder.fileCount, 0);
    
    console.log(`✅ Folder tracking initialization completed!`);
    console.log(`📊 Summary: ${allFolders.length} folders tracked, ${totalImageCount} images, ${totalFileCount} total files`);
    
    return NextResponse.json({
      success: true,
      message: 'Folder tracking system initialized successfully',
      summary: {
        foldersTracked: allFolders.length,
        totalImages: totalImageCount,
        totalFiles: totalFileCount,
        foldersProcessed: foldersProcessed
      },
      note: 'Folder tracking is now active. Future refresh operations will be much faster!'
    });
    
  } catch (error) {
    console.error('❌ Error initializing folder tracking:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to initialize folder tracking system',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const folderTracker = FolderTrackingService.getInstance();
    
    // Get current folder tracking status
    const allFolders = await folderTracker.getAllFolders();
    const totalImageCount = allFolders.reduce((sum, folder) => sum + folder.imageCount, 0);
    const totalFileCount = allFolders.reduce((sum, folder) => sum + folder.fileCount, 0);
    
    // Get some sample folders for display
    const sampleFolders = allFolders.slice(0, 10).map(folder => ({
      name: folder.name,
      path: folder.path,
      imageCount: folder.imageCount,
      fileCount: folder.fileCount,
      lastModified: folder.lastModified.toISOString(),
      lastScanned: folder.lastScanned.toISOString()
    }));
    
    return NextResponse.json({
      success: true,
      message: 'Folder tracking system status',
      isInitialized: allFolders.length > 0,
      summary: {
        foldersTracked: allFolders.length,
        totalImages: totalImageCount,
        totalFiles: totalFileCount
      },
      sampleFolders,
      note: allFolders.length === 0 
        ? 'Use POST to initialize folder tracking system'
        : 'Folder tracking is active and ready'
    });
    
  } catch (error) {
    console.error('❌ Error getting folder tracking status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get folder tracking status',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
