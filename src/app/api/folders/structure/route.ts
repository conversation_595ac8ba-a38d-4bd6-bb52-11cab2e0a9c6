import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Define folder node type for TypeScript
type FolderNode = {
  name: string;
  path: string;
  type: 'year' | 'month' | 'day';
  children: FolderNode[];
};

export async function GET() {
  try {
    // Use the same storage path as in IndexingService
    const storagePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';

    // During build time, the storage path might not exist, so return empty structure
    if (!fs.existsSync(storagePath)) {
      console.warn(`Storage path does not exist: ${storagePath}`);
      return NextResponse.json({
        success: true,
        folders: [],
        message: 'Storage path not available'
      });
    }

    console.log(`📁 Reading folder structure from: ${storagePath}`);

    // Build simple flat folder list for upload component
    const folders = await buildSimpleFolderList(storagePath);

    return NextResponse.json({
      success: true,
      folders: folders,
      totalFolders: folders.length
    });
  } catch (error) {
    console.error('Error getting folder structure:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get folder structure',
      details: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}

// Simple function to build flat folder list for upload component
async function buildSimpleFolderList(rootPath: string) {
  const folders: Array<{name: string, path: string, children: Array<{name: string, path: string, fileCount?: number}>}> = [];

  try {
    // Read year directories
    const yearEntries = await fs.promises.readdir(rootPath, { withFileTypes: true });
    const yearDirs = yearEntries.filter(entry => entry.isDirectory() && /^\d{4}$/.test(entry.name));

    // Sort years in descending order (most recent first)
    yearDirs.sort((a, b) => b.name.localeCompare(a.name));

    for (const yearDir of yearDirs) {
      const yearPath = path.join(rootPath, yearDir.name);
      const yearNode = {
        name: yearDir.name,
        path: yearDir.name,
        children: [] as Array<{name: string, path: string, fileCount?: number}>
      };

      try {
        // Read month directories within each year
        const monthEntries = await fs.promises.readdir(yearPath, { withFileTypes: true });
        const monthDirs = monthEntries.filter(entry => entry.isDirectory());

        // Sort months by extracting month number
        monthDirs.sort((a, b) => {
          const monthA = extractMonth(a.name) || 0;
          const monthB = extractMonth(b.name) || 0;
          return monthB - monthA; // Most recent month first
        });

        for (const monthDir of monthDirs) {
          const monthPath = path.join(yearPath, monthDir.name);

          // Count files in this month folder
          let fileCount = 0;
          try {
            const monthFiles = await fs.promises.readdir(monthPath, { withFileTypes: true });
            fileCount = monthFiles.filter(file =>
              file.isFile() && /\.(jpg|jpeg|png|gif|bmp|tiff|webp|mp4|mov|avi|mkv|mp3|wav|flac|pdf)$/i.test(file.name)
            ).length;
          } catch (error) {
            console.warn(`Could not count files in ${monthPath}:`, error);
          }

          yearNode.children.push({
            name: monthDir.name,
            path: `${yearDir.name}/${monthDir.name}`,
            fileCount
          });
        }

      } catch (error) {
        console.warn(`Could not read year folder ${yearPath}:`, error);
      }

      folders.push(yearNode);
    }

    console.log(`✅ Found ${folders.length} year folders with month subfolders`);

  } catch (error) {
    console.error('Error building simple folder list:', error);
  }

  return folders;
}

async function buildFolderStructure(rootPath: string): Promise<FolderNode[]> {
  const structure: FolderNode[] = [];
  
  try {
    // Read the root directory
    const entries = await fs.promises.readdir(rootPath, { withFileTypes: true });
    const dirs = entries.filter(entry => entry.isDirectory());
    
    // Sort directories by name (years in descending order)
    dirs.sort((a, b) => {
      // Extract year numbers for better sorting
      const yearA = extractYear(a.name);
      const yearB = extractYear(b.name);
      
      if (yearA !== null && yearB !== null) {
        return yearB - yearA; // Descending order
      }
      
      return b.name.localeCompare(a.name);
    });
    
    // Process each year directory
    for (const dir of dirs) {
      // Skip hidden directories
      if (dir.name.startsWith('.')) continue;
      
      const yearPath = path.join(rootPath, dir.name);
      const yearNode: FolderNode = {
        name: dir.name,
        path: yearPath,
        type: 'year',
        children: []
      };
      
      // Read month directories
      try {
        const monthEntries = await fs.promises.readdir(yearPath, { withFileTypes: true });
        const monthDirs = monthEntries.filter(entry => entry.isDirectory());
        
        // Sort month directories (typically they're already in order)
        monthDirs.sort((a, b) => {
          // Extract month numbers for better sorting
          const monthA = extractMonth(a.name);
          const monthB = extractMonth(b.name);
          
          if (monthA !== null && monthB !== null) {
            return monthA - monthB; // Ascending order for months
          }
          
          return a.name.localeCompare(b.name);
        });
        
        // Process each month directory
        for (const monthDir of monthDirs) {
          const monthPath = path.join(yearPath, monthDir.name);
          const monthNode: FolderNode = {
            name: monthDir.name,
            path: monthPath,
            type: 'month',
            children: []
          };
          
          // Read day directories
          try {
            const dayEntries = await fs.promises.readdir(monthPath, { withFileTypes: true });
            const dayDirs = dayEntries.filter(entry => entry.isDirectory());
            
            // Sort day directories
            dayDirs.sort((a, b) => {
              // Extract day numbers for better sorting
              const dayA = extractDay(a.name);
              const dayB = extractDay(b.name);
              
              if (dayA !== null && dayB !== null) {
                return dayA - dayB; // Ascending order for days
              }
              
              return a.name.localeCompare(b.name);
            });
            
            // Process each day directory
            for (const dayDir of dayDirs) {
              const dayPath = path.join(monthPath, dayDir.name);
              const dayNode: FolderNode = {
                name: dayDir.name,
                path: dayPath,
                type: 'day',
                children: []
              };
              
              monthNode.children.push(dayNode);
            }
          } catch (error) {
            console.error(`Error reading day directories in ${monthPath}:`, error);
          }
          
          yearNode.children.push(monthNode);
        }
      } catch (error) {
        console.error(`Error reading month directories in ${yearPath}:`, error);
      }
      
      structure.push(yearNode);
    }
  } catch (error) {
    console.error('Error building folder structure:', error);
  }
  
  return structure;
}

// Helper functions to extract numbers from folder names
function extractYear(folderName: string): number | null {
  const match = folderName.match(/(\d{4})/);
  return match ? parseInt(match[1], 10) : null;
}

function extractMonth(folderName: string): number | null {
  // Try to extract numeric month (01, 02, etc.)
  const match = folderName.match(/^(\d{1,2})[^0-9]/);
  if (match) {
    return parseInt(match[1], 10);
  }
  
  // Try to match month names
  const monthMap: {[key: string]: number} = {
    'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
    'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
  };
  
  for (const [abbr, num] of Object.entries(monthMap)) {
    if (folderName.toLowerCase().includes(abbr)) {
      return num;
    }
  }
  
  return null;
}

function extractDay(folderName: string): number | null {
  // Try to extract day number (01, 02, etc.) from formats like "01-05-2023"
  const match = folderName.match(/^(\d{1,2})[^0-9]/);
  return match ? parseInt(match[1], 10) : null;
} 