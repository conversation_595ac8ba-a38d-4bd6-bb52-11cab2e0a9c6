import { NextRequest, NextResponse } from 'next/server';
import { FolderTrackingService } from '@/lib/storage/folderTrackingService';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const folderPath = searchParams.get('path');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    const folderTracker = FolderTrackingService.getInstance();
    
    if (folderPath) {
      // Get statistics for a specific folder
      const basePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
      const fullPath = path.join(basePath, folderPath);
      
      const folderStats = await folderTracker.getFolderStats(fullPath);
      
      if (!folderStats) {
        return NextResponse.json({
          success: false,
          error: 'Folder not found in tracking system',
          path: folderPath
        }, { status: 404 });
      }
      
      return NextResponse.json({
        success: true,
        folder: {
          name: folderStats.name,
          path: folderStats.path,
          parentPath: folderStats.parentPath,
          imageCount: folderStats.imageCount,
          fileCount: folderStats.fileCount,
          totalSize: folderStats.totalSize,
          lastModified: folderStats.lastModified.toISOString(),
          lastScanned: folderStats.lastScanned.toISOString(),
          contentHash: folderStats.contentHash
        }
      });
    } else {
      // Get all folders with pagination
      const allFolders = await folderTracker.getAllFolders();
      
      // Apply pagination
      const paginatedFolders = allFolders.slice(offset, offset + limit);
      
      // Calculate summary statistics
      const totalImageCount = allFolders.reduce((sum, folder) => sum + folder.imageCount, 0);
      const totalFileCount = allFolders.reduce((sum, folder) => sum + folder.fileCount, 0);
      const totalSize = allFolders.reduce((sum, folder) => sum + parseInt(folder.totalSize || '0'), 0);
      
      // Format folders for response
      const formattedFolders = paginatedFolders.map(folder => ({
        id: folder.id,
        name: folder.name,
        path: folder.path,
        parentPath: folder.parentPath,
        imageCount: folder.imageCount,
        fileCount: folder.fileCount,
        totalSize: folder.totalSize,
        lastModified: folder.lastModified.toISOString(),
        lastScanned: folder.lastScanned.toISOString(),
        contentHash: folder.contentHash
      }));
      
      return NextResponse.json({
        success: true,
        folders: formattedFolders,
        pagination: {
          total: allFolders.length,
          limit,
          offset,
          hasMore: offset + limit < allFolders.length
        },
        summary: {
          totalFolders: allFolders.length,
          totalImages: totalImageCount,
          totalFiles: totalFileCount,
          totalSize: totalSize,
          averageImagesPerFolder: allFolders.length > 0 ? Math.round(totalImageCount / allFolders.length) : 0
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Error getting folder statistics:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get folder statistics',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { folderPath, action } = body;
    
    if (!folderPath) {
      return NextResponse.json({
        success: false,
        error: 'Folder path is required'
      }, { status: 400 });
    }
    
    const folderTracker = FolderTrackingService.getInstance();
    const basePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
    const fullPath = path.join(basePath, folderPath);
    
    if (action === 'update') {
      // Update folder statistics
      console.log(`🔄 Updating folder statistics for: ${folderPath}`);
      
      const changeInfo = await folderTracker.updateFolderStats(fullPath);
      
      return NextResponse.json({
        success: true,
        message: 'Folder statistics updated',
        changes: {
          hasChanges: changeInfo.hasChanges,
          newFiles: changeInfo.newFiles,
          deletedFiles: changeInfo.deletedFiles,
          modifiedFiles: changeInfo.modifiedFiles
        }
      });
      
    } else if (action === 'create') {
      // Create or update folder tracking
      console.log(`📁 Creating folder tracking for: ${folderPath}`);
      
      const folderInfo = await folderTracker.getOrCreateFolder(fullPath);
      
      return NextResponse.json({
        success: true,
        message: 'Folder tracking created/updated',
        folder: {
          name: folderInfo.name,
          path: folderInfo.path,
          imageCount: folderInfo.imageCount,
          fileCount: folderInfo.fileCount,
          totalSize: folderInfo.totalSize,
          lastModified: folderInfo.lastModified.toISOString(),
          lastScanned: folderInfo.lastScanned.toISOString()
        }
      });
      
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action. Use "update" or "create"'
      }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ Error processing folder statistics request:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to process folder statistics request',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
