import { NextResponse } from 'next/server';
import { db } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

export async function POST() {
  try {
    console.log('🚀 Running totalSize field fix migration...');
    
    // Check current column type
    const checkColumnType = await db.execute(sql`
      SELECT data_type 
      FROM information_schema.columns 
      WHERE table_name = 'folders' 
      AND column_name = 'total_size'
    `);
    
    const currentType = checkColumnType.rows[0]?.data_type;
    console.log(`📊 Current total_size column type: ${currentType}`);
    
    if (currentType === 'integer') {
      console.log('🔧 Converting INTEGER to TEXT for large folder support...');
      
      // Alter the folders table
      await db.execute(sql`
        ALTER TABLE folders 
        ALTER COLUMN total_size TYPE TEXT USING total_size::TEXT
      `);
      
      await db.execute(sql`
        ALTER TABLE folders 
        ALTER COLUMN total_size SET DEFAULT '0'
      `);
      
      // Also fix the folder_stats table
      await db.execute(sql`
        ALTER TABLE folder_stats 
        ALTER COLUMN total_size TYPE TEXT USING total_size::TEXT
      `);
      
      await db.execute(sql`
        ALTER TABLE folder_stats 
        ALTER COLUMN total_size SET DEFAULT '0'
      `);
      
      // Update any existing records
      await db.execute(sql`
        UPDATE folders 
        SET total_size = total_size::TEXT 
        WHERE total_size IS NOT NULL
      `);
      
      await db.execute(sql`
        UPDATE folder_stats 
        SET total_size = total_size::TEXT 
        WHERE total_size IS NOT NULL
      `);
      
      // Update table statistics
      await db.execute(sql`ANALYZE folders`);
      await db.execute(sql`ANALYZE folder_stats`);
      
      console.log('✅ totalSize field migration completed successfully');
      
      return NextResponse.json({
        success: true,
        message: 'totalSize field migration completed successfully',
        changes: {
          previousType: currentType,
          newType: 'text',
          tablesUpdated: ['folders', 'folder_stats']
        }
      });
      
    } else if (currentType === 'text') {
      console.log('✅ totalSize field is already TEXT type - no migration needed');
      
      return NextResponse.json({
        success: true,
        message: 'totalSize field is already TEXT type - no migration needed',
        currentType: currentType
      });
      
    } else {
      console.log(`⚠️ Unexpected column type: ${currentType}`);
      
      return NextResponse.json({
        success: false,
        error: `Unexpected column type: ${currentType}`,
        currentType: currentType
      }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ Error running totalSize field migration:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to run totalSize field migration',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Check current column type and provide status
    const checkFoldersType = await db.execute(sql`
      SELECT data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'folders' 
      AND column_name = 'total_size'
    `);
    
    const checkFolderStatsType = await db.execute(sql`
      SELECT data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'folder_stats' 
      AND column_name = 'total_size'
    `);
    
    const foldersInfo = checkFoldersType.rows[0];
    const folderStatsInfo = checkFolderStatsType.rows[0];
    
    const needsMigration = foldersInfo?.data_type === 'integer' || folderStatsInfo?.data_type === 'integer';
    
    return NextResponse.json({
      success: true,
      message: 'totalSize field migration status',
      status: {
        needsMigration,
        folders: {
          dataType: foldersInfo?.data_type,
          isNullable: foldersInfo?.is_nullable,
          columnDefault: foldersInfo?.column_default
        },
        folderStats: {
          dataType: folderStatsInfo?.data_type,
          isNullable: folderStatsInfo?.is_nullable,
          columnDefault: folderStatsInfo?.column_default
        }
      },
      recommendation: needsMigration 
        ? 'Use POST to run the totalSize field migration'
        : 'totalSize fields are properly configured'
    });
    
  } catch (error) {
    console.error('❌ Error checking totalSize field status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check totalSize field status',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
