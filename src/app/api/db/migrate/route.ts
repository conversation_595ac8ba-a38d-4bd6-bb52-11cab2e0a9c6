import { NextResponse } from 'next/server';
import { db } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

export async function POST() {
  try {
    console.log('🚀 Running folder tracking database migration...');
    
    // Run the folder tracking migration
    await db.execute(sql`
      -- Create folders table for tracking folder metadata and statistics
      CREATE TABLE IF NOT EXISTS folders (
        id TEXT PRIMARY KEY,
        path TEXT NOT NULL UNIQUE,
        parent_path TEXT,
        name TEXT NOT NULL,
        file_count INTEGER DEFAULT 0 NOT NULL,
        image_count INTEGER DEFAULT 0 NOT NULL,
        total_size TEXT DEFAULT '0' NOT NULL,
        last_modified TIMESTAMP NOT NULL,
        last_scanned TIMESTAMP NOT NULL DEFAULT NOW(),
        content_hash TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    await db.execute(sql`
      -- Create folder statistics table for historical tracking
      CREATE TABLE IF NOT EXISTS folder_stats (
        id TEXT PRIMARY KEY,
        folder_id TEXT NOT NULL REFERENCES folders(id) ON DELETE CASCADE,
        stat_date TIMESTAMP NOT NULL DEFAULT NOW(),
        file_count INTEGER DEFAULT 0 NOT NULL,
        image_count INTEGER DEFAULT 0 NOT NULL,
        total_size TEXT DEFAULT '0' NOT NULL,
        new_files INTEGER DEFAULT 0 NOT NULL,
        deleted_files INTEGER DEFAULT 0 NOT NULL,
        modified_files INTEGER DEFAULT 0 NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    await db.execute(sql`
      -- Create indexes for efficient queries
      CREATE INDEX IF NOT EXISTS idx_folders_path ON folders(path);
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_folders_parent_path ON folders(parent_path);
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_folders_last_modified ON folders(last_modified);
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_folders_last_scanned ON folders(last_scanned);
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_folders_content_hash ON folders(content_hash);
    `);
    
    await db.execute(sql`
      -- Indexes for folder stats
      CREATE INDEX IF NOT EXISTS idx_folder_stats_folder_id ON folder_stats(folder_id);
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_folder_stats_stat_date ON folder_stats(stat_date);
    `);
    
    // Test that the tables were created successfully
    const testFolders = await db.execute(sql`SELECT COUNT(*) FROM folders`);
    const testFolderStats = await db.execute(sql`SELECT COUNT(*) FROM folder_stats`);
    
    console.log('✅ Folder tracking migration completed successfully');
    console.log(`📊 Tables created: folders (${testFolders.rows[0].count} records), folder_stats (${testFolderStats.rows[0].count} records)`);
    
    return NextResponse.json({
      success: true,
      message: 'Folder tracking database migration completed successfully',
      tables: {
        folders: {
          created: true,
          recordCount: testFolders.rows[0].count
        },
        folder_stats: {
          created: true,
          recordCount: testFolderStats.rows[0].count
        }
      },
      note: 'You can now initialize folder tracking using /api/folders/initialize-tracking'
    });
    
  } catch (error) {
    console.error('❌ Error running folder tracking migration:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to run folder tracking migration',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Check if the folder tracking tables exist
    const checkFolders = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'folders'
      );
    `);
    
    const checkFolderStats = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'folder_stats'
      );
    `);
    
    const foldersTableExists = checkFolders.rows[0].exists;
    const folderStatsTableExists = checkFolderStats.rows[0].exists;
    
    let folderCount = 0;
    let folderStatsCount = 0;
    
    if (foldersTableExists) {
      const folderCountResult = await db.execute(sql`SELECT COUNT(*) FROM folders`);
      folderCount = folderCountResult.rows[0].count as number;
    }
    
    if (folderStatsTableExists) {
      const folderStatsCountResult = await db.execute(sql`SELECT COUNT(*) FROM folder_stats`);
      folderStatsCount = folderStatsCountResult.rows[0].count as number;
    }
    
    return NextResponse.json({
      success: true,
      message: 'Folder tracking migration status',
      migrationStatus: {
        foldersTableExists,
        folderStatsTableExists,
        isFullyMigrated: foldersTableExists && folderStatsTableExists
      },
      recordCounts: {
        folders: folderCount,
        folderStats: folderStatsCount
      },
      note: !foldersTableExists || !folderStatsTableExists 
        ? 'Use POST to run the folder tracking migration'
        : 'Folder tracking migration is complete'
    });
    
  } catch (error) {
    console.error('❌ Error checking folder tracking migration status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check folder tracking migration status',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
