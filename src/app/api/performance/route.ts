import { NextRequest, NextResponse } from 'next/server';
import { PerformanceMonitor } from '@/lib/services/performanceMonitor';

const performanceMonitor = PerformanceMonitor.getInstance();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange');
    const format = searchParams.get('format') || 'json';
    
    let dateRange;
    if (timeRange) {
      const hours = parseInt(timeRange);
      if (!isNaN(hours)) {
        dateRange = {
          start: new Date(Date.now() - hours * 60 * 60 * 1000),
          end: new Date()
        };
      }
    }
    
    const report = performanceMonitor.getReport(dateRange);
    
    if (format === 'summary') {
      return NextResponse.json({
        success: true,
        summary: report.summary,
        recommendations: report.recommendations,
        metricsCount: performanceMonitor.getMetricsCount()
      });
    }
    
    return NextResponse.json({
      success: true,
      report,
      metricsCount: performanceMonitor.getMetricsCount(),
      timeRange: timeRange ? `${timeRange} hours` : 'all time'
    });
    
  } catch (error) {
    console.error('❌ Error getting performance report:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate performance report',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'clear') {
      performanceMonitor.clearMetrics();
      
      return NextResponse.json({
        success: true,
        message: 'Performance metrics cleared'
      });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, { status: 400 });
    
  } catch (error) {
    console.error('❌ Error in performance POST:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to process performance request',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
