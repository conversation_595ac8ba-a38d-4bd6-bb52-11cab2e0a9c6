import { NextRequest, NextResponse } from 'next/server';
import { HuggingFaceVisionService } from '@/lib/services/huggingFaceVisionService';

// GET /api/ai/test-huggingface - Test Hugging Face connection
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Hugging Face connection...');

    // Test basic connection
    const connectionTest = await HuggingFaceVisionService.testConnection();
    
    if (!connectionTest) {
      return NextResponse.json({
        success: false,
        error: 'Hugging Face connection test failed',
        details: 'Could not establish connection to Hugging Face API'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Hugging Face connection successful',
      service: 'Hugging Face Inference API',
      models: [
        'google/vit-base-patch16-224 (Image Classification)',
        'facebook/detr-resnet-50 (Object Detection)'
      ],
      features: [
        'Free tier available',
        'Image classification',
        'Object detection',
        'No API key required for basic usage'
      ]
    });

  } catch (error) {
    console.error('❌ Hugging Face test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Hugging Face test failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// POST /api/ai/test-huggingface - Test Hugging Face vision with a specific image
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { imageUrl } = body;

    if (!imageUrl) {
      return NextResponse.json({
        success: false,
        error: 'Image URL is required'
      }, { status: 400 });
    }

    console.log('🧪 Testing Hugging Face vision with image:', imageUrl);

    const result = await HuggingFaceVisionService.analyzeImageForTags(imageUrl);

    return NextResponse.json({
      success: result.success,
      result: {
        tags: result.tags,
        analysis: result.analysis,
        tagCount: result.tags.length,
        averageConfidence: result.tags.length > 0 
          ? result.tags.reduce((sum, tag) => sum + tag.confidence, 0) / result.tags.length 
          : 0,
        highConfidenceTags: result.tags.filter(tag => tag.confidence > 0.6).length
      },
      error: result.error,
      service: 'Hugging Face Inference API'
    });

  } catch (error) {
    console.error('❌ Hugging Face vision test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Hugging Face vision test failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
