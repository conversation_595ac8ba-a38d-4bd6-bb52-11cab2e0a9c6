import { NextRequest, NextResponse } from 'next/server';
import { OpenAIVisionService } from '@/lib/services/openaiVisionService';

// GET /api/ai/test-openai - Test OpenAI connection and vision capabilities
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing OpenAI connection...');

    // Test basic connection
    const connectionTest = await OpenAIVisionService.testConnection();
    
    if (!connectionTest) {
      return NextResponse.json({
        success: false,
        error: 'OpenAI connection test failed',
        details: 'Could not establish connection to OpenAI API'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'OpenAI connection successful',
      service: 'OpenAI GPT-4 Vision',
      capabilities: [
        'Image analysis',
        'Content tagging',
        'Object detection',
        'Scene understanding'
      ]
    });

  } catch (error) {
    console.error('❌ OpenAI test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'OpenAI test failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// POST /api/ai/test-openai - Test OpenAI vision with a specific image
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { imageUrl } = body;

    if (!imageUrl) {
      return NextResponse.json({
        success: false,
        error: 'Image URL is required'
      }, { status: 400 });
    }

    console.log('🧪 Testing OpenAI vision with image:', imageUrl);

    const result = await OpenAIVisionService.analyzeImageForTags(imageUrl);

    return NextResponse.json({
      success: result.success,
      result: {
        tags: result.tags,
        analysis: result.analysis,
        tagCount: result.tags.length,
        averageConfidence: result.tags.length > 0 
          ? result.tags.reduce((sum, tag) => sum + tag.confidence, 0) / result.tags.length 
          : 0
      },
      error: result.error
    });

  } catch (error) {
    console.error('❌ OpenAI vision test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'OpenAI vision test failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
