import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

// Use the singleton instance
const indexingService = IndexingService.getInstance();

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const year = searchParams.get('year');
    const month = searchParams.get('month');
    const day = searchParams.get('day');

    if (!query || query.trim().length === 0) {
      return NextResponse.json({
        success: true,
        assets: [],
        total: 0
      });
    }

    console.log('🔍 Advanced search API called with:', { query, year, month, day });

    // Get search results from the indexing service (with AND/OR/phrase support)
    const assets = await indexingService.searchAssets(query.trim(), {
      year,
      month,
      day
    });

    console.log(`✅ Found ${assets.length} assets matching "${query}" with advanced search`);

    // Convert dates to ISO strings for JSON serialization
    const processedAssets = assets.map(asset => ({
      id: asset.id,
      filename: asset.filename,
      fileUrl: asset.fileUrl,
      thumbnailUrl: asset.thumbnailUrl,
      filePath: asset.filePath,
      type: asset.type,
      size: asset.size,
      lastModified: asset.lastModified.toISOString(),
      createdAt: asset.createdAt.toISOString(),
      updatedAt: asset.updatedAt.toISOString(),
      metadata: asset.metadata
    }));

    return NextResponse.json({
      success: true,
      assets: processedAssets,
      total: assets.length,
      filters: { year, month, day }
    });
  } catch (error) {
    console.error('Error searching photos:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error searching photos',
      errorDetails: error instanceof Error ? error.message : String(error),
      assets: [],
      total: 0
    }, { status: 500 });
  }
} 