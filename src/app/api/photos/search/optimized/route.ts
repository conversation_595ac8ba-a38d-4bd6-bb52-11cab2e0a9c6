import { NextRequest, NextResponse } from 'next/server';
import { SearchService } from '@/lib/services/searchService';
import { PerformanceMonitor } from '@/lib/services/performanceMonitor';

const searchService = SearchService.getInstance();
const performanceMonitor = PerformanceMonitor.getInstance();

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const type = searchParams.get('type') || undefined;
    const sortBy = searchParams.get('sortBy') as 'relevance' | 'date' | 'name' | 'size' || 'relevance';
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc';
    
    if (!query || query.trim().length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Query parameter "q" is required and cannot be empty'
      }, { status: 400 });
    }
    
    console.log(`🔍 Advanced search API: "${query}" (${sortBy} ${sortOrder})`);
    
    // Perform advanced search
    const searchResult = await searchService.search(query.trim(), {
      limit,
      offset,
      type,
      sortBy,
      sortOrder
    });
    
    // Convert dates to ISO strings for JSON serialization
    const processedAssets = searchResult.assets.map(asset => ({
      id: asset.id,
      title: asset.filename,
      description: '',
      filename: asset.filename,
      fileUrl: asset.fileUrl,
      thumbnailUrl: asset.thumbnailUrl,
      filePath: asset.filePath,
      type: asset.type,
      size: asset.size,
      lastModified: asset.lastModified.toISOString(),
      createdAt: asset.createdAt.toISOString(),
      updatedAt: asset.updatedAt.toISOString(),
      metadata: asset.metadata || {},
      tags: [],
      collectionId: undefined
    }));
    
    const totalTime = Date.now() - startTime;
    
    // Record performance
    performanceMonitor.recordApiRequest(
      '/api/photos/search/optimized',
      'GET',
      totalTime,
      200,
      {
        query,
        resultCount: processedAssets.length,
        strategy: searchResult.strategy,
        searchTime: searchResult.searchTime
      }
    );
    
    console.log(`✅ Search completed: ${processedAssets.length} results in ${totalTime}ms (${searchResult.strategy} strategy)`);
    
    return NextResponse.json({
      success: true,
      assets: processedAssets,
      total: searchResult.total,
      query: query.trim(),
      performance: {
        totalTime,
        searchTime: searchResult.searchTime,
        strategy: searchResult.strategy
      },
      pagination: {
        limit,
        offset,
        hasMore: processedAssets.length === limit
      },
      sorting: {
        sortBy,
        sortOrder
      }
    });
    
  } catch (error) {
    const errorTime = Date.now() - startTime;
    console.error('❌ Error in advanced search:', error);
    
    // Record error performance
    performanceMonitor.recordApiRequest(
      '/api/photos/search/optimized',
      'GET',
      errorTime,
      500,
      { error: true }
    );
    
    return NextResponse.json({
      success: false,
      error: 'Error performing search',
      errorDetails: error instanceof Error ? error.message : String(error),
      assets: [],
      total: 0,
      performance: {
        totalTime: errorTime,
        searchTime: 0,
        strategy: 'error'
      }
    }, { status: 500 });
  }
}

// POST endpoint for search suggestions
export async function POST(request: NextRequest) {
  try {
    const { action, query, limit } = await request.json();
    
    if (action === 'suggestions') {
      if (!query || typeof query !== 'string') {
        return NextResponse.json({
          success: false,
          error: 'Query is required for suggestions'
        }, { status: 400 });
      }
      
      const suggestions = await searchService.getSearchSuggestions(
        query.trim(),
        limit || 10
      );
      
      return NextResponse.json({
        success: true,
        suggestions,
        query: query.trim()
      });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, { status: 400 });
    
  } catch (error) {
    console.error('❌ Error in search POST:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error processing search request',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
