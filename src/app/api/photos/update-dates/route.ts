import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

const indexingService = IndexingService.getInstance();

export async function POST() {
  try {
    console.log('📅 Starting date update for existing assets...');

    // Use the existing method to update dates based on folder structure
    await indexingService.updateExistingAssetsWithFolderDates(1000);

    return NextResponse.json({
      success: true,
      message: 'Asset dates updated based on folder structure',
      note: 'All existing assets have been updated with dates extracted from their folder paths. This process updates the lastModified field for all assets to use folder structure dates.'
    });
  } catch (error) {
    console.error('❌ Error updating asset dates:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update asset dates',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      message: 'Update asset dates endpoint',
      note: 'Use POST to update all existing asset dates based on folder structure without full rescan'
    });
  } catch (error) {
    console.error('❌ Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get status'
    }, { status: 500 });
  }
}
