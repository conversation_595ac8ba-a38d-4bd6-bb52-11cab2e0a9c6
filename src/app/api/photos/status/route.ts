import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';
import { db } from '@/lib/db/schema';
import { assets } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

// Use the singleton instance
const indexingService = IndexingService.getInstance();

export async function GET() {
  try {
    // Get the current indexing status
    const status = indexingService.getIndexingStatus();

    // Convert numbers to strings to avoid integer overflow issues with large values
    // Use fallback values if properties are undefined
    let indexed = status.current || 0;
    let total = status.total || 0;

    // If IndexingService hasn't scanned yet (total is 0), get database count
    if (total === 0) {
      try {
        const result = await db.select({ count: sql<number>`count(*)` }).from(assets);
        const dbCount = result[0]?.count || 0;
        indexed = dbCount; // Database count represents indexed assets
        total = dbCount; // Use database count as total for now
        console.log(`Using database count: ${dbCount} assets indexed`);
      } catch (dbError) {
        console.error('Error getting database count:', dbError);
        // Fall back to IndexingService values
      }
    }

    return NextResponse.json({
      success: true,
      isIndexing: status.isIndexing || status.isFastIndexing, // Include both indexing states
      isFastIndexing: status.isFastIndexing,
      isFullIndexing: status.isIndexing,
      indexed: indexed,
      indexedStr: indexed.toString(),
      total: total,
      totalStr: total.toString(),
      percentage: status.percentage || 0,
      lastIndexTime: null, // Remove this property since it doesn't exist in the status
      // Enhanced progress information for modal display
      currentOperation: status.currentOperation || '',
      currentFolder: status.currentFolder || '',
      foldersToCheck: status.foldersToCheck || 0,
      foldersChecked: status.foldersChecked || 0,
      detectedChanges: status.detectedChanges || []
    });
  } catch (error) {
    console.error('Error getting indexing status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get indexing status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}