import { NextResponse } from 'next/server';
import { db } from '@/lib/db/schema';
import { assets } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

export async function GET() {
  try {
    console.log('📅 Fetching available years from assets last_modified column');

    // Query to get distinct years from the lastModified column
    const result = await db.select({
      year: sql<string>`EXTRACT(YEAR FROM ${assets.lastModified})::text`
    })
    .from(assets)
    .groupBy(sql`EXTRACT(YEAR FROM ${assets.lastModified})`)
    .orderBy(sql`EXTRACT(YEAR FROM ${assets.lastModified}) DESC`);

    const years = result.map(row => row.year).filter(year => year && year !== 'null');

    console.log(`✅ Found ${years.length} years:`, years);

    return NextResponse.json({
      success: true,
      years,
      total: years.length
    });
  } catch (error) {
    console.error('Error fetching years:', error);
    
    // Fallback to current year and recent years
    const currentYear = new Date().getFullYear();
    const fallbackYears = Array.from({ length: 5 }, (_, i) => String(currentYear - i));
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch years from database',
      errorDetails: error instanceof Error ? error.message : String(error),
      years: fallbackYears
    }, { status: 500 });
  }
} 