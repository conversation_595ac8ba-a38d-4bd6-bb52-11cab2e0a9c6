import { NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import { IndexingService } from '@/lib/storage/indexingService';
import path from 'path';

const execAsync = promisify(exec);
const indexingService = IndexingService.getInstance();

export async function POST() {
  const startTime = Date.now();
  
  try {
    console.log('🚀 FAST REFRESH: Starting lightning-fast refresh using terminal commands...');
    
    const storagePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
    
    // Step 1: Use terminal commands to find new files (COMPREHENSIVE - search all recent files)
    console.log('⚡ Step 1: Finding new files with terminal commands...');

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // JavaScript months are 0-based

    // Search strategy: Look for files modified in the last 7 days across the entire storage
    // This will catch new files and folders regardless of where they are
    // Include ALL supported file types: images, videos, audio, documents
    const findCommand = `find "${storagePath}" -type f \\( ` +
      // Images
      `-name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.svg" -o -name "*.webp" -o -name "*.bmp" -o -name "*.tiff" -o -name "*.tif" -o -name "*.raw" -o -name "*.cr2" -o -name "*.nef" -o -name "*.arw" -o -name "*.dng" -o -name "*.heic" -o -name "*.heif" ` +
      // Videos
      `-o -name "*.mp4" -o -name "*.webm" -o -name "*.mov" -o -name "*.avi" -o -name "*.wmv" -o -name "*.flv" -o -name "*.mkv" -o -name "*.m4v" -o -name "*.3gp" -o -name "*.ogv" ` +
      // Audio
      `-o -name "*.mp3" -o -name "*.wav" -o -name "*.ogg" -o -name "*.aac" -o -name "*.m4a" -o -name "*.flac" -o -name "*.wma" -o -name "*.opus" ` +
      // Documents
      `-o -name "*.pdf" -o -name "*.doc" -o -name "*.docx" -o -name "*.xls" -o -name "*.xlsx" -o -name "*.ppt" -o -name "*.pptx" -o -name "*.txt" -o -name "*.csv" -o -name "*.rtf" -o -name "*.odt" -o -name "*.ods" -o -name "*.odp" ` +
      `\\) -newermt "7 days ago" 2>/dev/null`;

    console.log(`🔍 Searching for files modified in last 7 days...`);
    const { stdout: recentFiles } = await execAsync(findCommand);
    let newFilePaths = recentFiles.trim().split('\n').filter(f => f.length > 0);

    console.log(`⚡ Found ${newFilePaths.length} files modified in last 7 days in ${Date.now() - startTime}ms`);

    // If no files found in last 7 days, fall back to searching current year's most recent folders
    if (newFilePaths.length === 0) {
      console.log('🔄 No files found in last 7 days, searching recent folders...');
      const fallbackCommand = `find "${storagePath}/${currentYear}" -type f \\( ` +
        // Images
        `-name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.svg" -o -name "*.webp" -o -name "*.bmp" -o -name "*.tiff" -o -name "*.tif" -o -name "*.raw" -o -name "*.cr2" -o -name "*.nef" -o -name "*.arw" -o -name "*.dng" -o -name "*.heic" -o -name "*.heif" ` +
        // Videos
        `-o -name "*.mp4" -o -name "*.webm" -o -name "*.mov" -o -name "*.avi" -o -name "*.wmv" -o -name "*.flv" -o -name "*.mkv" -o -name "*.m4v" -o -name "*.3gp" -o -name "*.ogv" ` +
        // Audio
        `-o -name "*.mp3" -o -name "*.wav" -o -name "*.ogg" -o -name "*.aac" -o -name "*.m4a" -o -name "*.flac" -o -name "*.wma" -o -name "*.opus" ` +
        // Documents
        `-o -name "*.pdf" -o -name "*.doc" -o -name "*.docx" -o -name "*.xls" -o -name "*.xlsx" -o -name "*.ppt" -o -name "*.pptx" -o -name "*.txt" -o -name "*.csv" -o -name "*.rtf" -o -name "*.odt" -o -name "*.ods" -o -name "*.odp" ` +
        `\\) -exec ls -t {} + 2>/dev/null | head -1000`;
      const { stdout: fallbackFiles } = await execAsync(fallbackCommand);
      newFilePaths = fallbackFiles.trim().split('\n').filter(f => f.length > 0);
      console.log(`⚡ Fallback found ${newFilePaths.length} recent files`);
    }

    // Debug: Show some of the files found
    if (newFilePaths.length > 0) {
      console.log(`🔍 DEBUG: First 5 files found:`, newFilePaths.slice(0, 5));
      console.log(`🔍 DEBUG: Last 5 files found:`, newFilePaths.slice(-5));
    }

    if (newFilePaths.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No new files found',
        refreshResults: {
          newAssets: 0,
          updatedAssets: 0,
          deletedAssets: 0,
          foldersChecked: 0,
          duration: Date.now() - startTime,
          method: 'terminal-fast'
        }
      });
    }
    
    // Step 2: Quick database check to see which files are actually new (FAST - 1 second)
    console.log('⚡ Step 2: Checking which files are actually new...');
    
    const { db } = await import('@/lib/db/schema');
    const { assets } = await import('@/lib/db/schema');
    const { inArray } = await import('drizzle-orm');
    
    // Convert absolute paths to relative paths for database comparison
    const relativePaths = newFilePaths.map(filePath =>
      path.relative(storagePath, filePath)
    );

    console.log(`🔍 DEBUG: First 5 relative paths:`, relativePaths.slice(0, 5));

    // Check which files already exist in database
    const existingAssets = await db.select({ filePath: assets.filePath })
      .from(assets)
      .where(inArray(assets.filePath, relativePaths));

    console.log(`🔍 DEBUG: Found ${existingAssets.length} existing assets in database`);
    console.log(`🔍 DEBUG: First 5 existing paths:`, existingAssets.slice(0, 5).map(a => a.filePath));

    const existingPaths = new Set(existingAssets.map(a => a.filePath));
    const trulyNewFiles = newFilePaths.filter(filePath => {
      const relativePath = path.relative(storagePath, filePath);
      return !existingPaths.has(relativePath);
    });

    console.log(`⚡ Found ${trulyNewFiles.length} truly new files that need indexing`);
    if (trulyNewFiles.length > 0) {
      console.log(`🔍 DEBUG: First 5 truly new files:`, trulyNewFiles.slice(0, 5));
    }
    
    // Step 3: Index only the new files (FAST - 1-2 seconds)
    let newAssets = 0;
    if (trulyNewFiles.length > 0) {
      console.log('⚡ Step 3: Indexing only the new files...');

      for (const filePath of trulyNewFiles) {
        try {
          await indexingService.indexFile(filePath);
          newAssets++;
        } catch (error) {
          console.error(`Error indexing ${filePath}:`, error);
        }
      }
    }

    // Step 4: Cleanup deleted files (FAST - 1-2 seconds)
    console.log('🧹 Step 4: Cleaning up deleted files from database...');
    let deletedAssets = 0;
    try {
      const cleanupResults = await indexingService.forceCleanupDeletedFiles();
      deletedAssets = cleanupResults.deletedCount;
      console.log(`🧹 Cleanup completed: ${deletedAssets} deleted files removed from database`);
    } catch (error) {
      console.error('⚠️ Cleanup error (non-critical):', error);
      // Don't fail the entire refresh if cleanup fails
    }

    const duration = Date.now() - startTime;
    console.log(`✅ FAST REFRESH completed in ${duration}ms: ${newAssets} new assets indexed, ${deletedAssets} deleted assets cleaned up`);

    return NextResponse.json({
      success: true,
      message: `Fast refresh completed: ${newAssets} new assets found, ${deletedAssets} deleted files cleaned up`,
      refreshResults: {
        newAssets,
        updatedAssets: 0,
        deletedAssets,
        foldersChecked: 1,
        duration,
        method: 'terminal-fast',
        detectedFiles: trulyNewFiles.map(f => path.basename(f))
      }
    });
    
  } catch (error) {
    console.error('❌ Fast refresh error:', error);
    return NextResponse.json({
      success: false,
      error: 'Fast refresh failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// GET endpoint to check what files would be found
export async function GET() {
  try {
    const storagePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
    
    // Just show what files would be found without indexing them (optimized search)
    const currentYear = new Date().getFullYear();
    const yearPath = `"${storagePath}/${currentYear}"`;

    const findCommand = `find ${yearPath} -maxdepth 4 -type f \\( ` +
      // Images
      `-name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.svg" -o -name "*.webp" -o -name "*.bmp" -o -name "*.tiff" -o -name "*.tif" -o -name "*.raw" -o -name "*.cr2" -o -name "*.nef" -o -name "*.arw" -o -name "*.dng" -o -name "*.heic" -o -name "*.heif" ` +
      // Videos
      `-o -name "*.mp4" -o -name "*.webm" -o -name "*.mov" -o -name "*.avi" -o -name "*.wmv" -o -name "*.flv" -o -name "*.mkv" -o -name "*.m4v" -o -name "*.3gp" -o -name "*.ogv" ` +
      // Audio
      `-o -name "*.mp3" -o -name "*.wav" -o -name "*.ogg" -o -name "*.aac" -o -name "*.m4a" -o -name "*.flac" -o -name "*.wma" -o -name "*.opus" ` +
      // Documents
      `-o -name "*.pdf" -o -name "*.doc" -o -name "*.docx" -o -name "*.xls" -o -name "*.xlsx" -o -name "*.ppt" -o -name "*.pptx" -o -name "*.txt" -o -name "*.csv" -o -name "*.rtf" -o -name "*.odt" -o -name "*.ods" -o -name "*.odp" ` +
      `\\) 2>/dev/null | head -20`;
    
    const { stdout: recentFiles } = await execAsync(findCommand);
    const filePaths = recentFiles.trim().split('\n').filter(f => f.length > 0);
    
    return NextResponse.json({
      success: true,
      message: `Found ${filePaths.length} files modified in last 7 days`,
      files: filePaths.map(f => path.basename(f)),
      note: 'This is a preview - use POST to actually index new files'
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Preview failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
