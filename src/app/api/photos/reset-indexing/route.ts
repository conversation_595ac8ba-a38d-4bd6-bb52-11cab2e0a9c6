import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

const indexingService = IndexingService.getInstance();

export async function POST() {
  try {
    console.log('🔄 Force reset indexing requested');
    
    // Force reset the indexing states
    indexingService.forceResetIndexingState();
    
    // Trigger a fresh fast indexing
    await indexingService.fastIndexRecentFiles(30);
    
    // Get updated status
    const status = indexingService.getIndexingStatus();
    
    return NextResponse.json({
      success: true,
      message: 'Indexing state reset and fresh scan completed',
      status
    });
  } catch (error) {
    console.error('Error resetting indexing:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to reset indexing',
      errorDetails: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 