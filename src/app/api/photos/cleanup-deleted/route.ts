import { NextRequest, NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

export async function POST(request: NextRequest) {
  try {
    const indexingService = IndexingService.getInstance();

    console.log('🧹 API: Starting cleanup of deleted files...');

    // Force cleanup of deleted files
    const results = await indexingService.forceCleanupDeletedFiles();

    console.log(`✅ API: Cleanup completed - ${results.deletedCount} deleted, ${results.checkedCount} checked`);

    return NextResponse.json({
      success: true,
      message: 'Cleanup completed successfully',
      results: {
        deletedCount: results.deletedCount,
        checkedCount: results.checkedCount,
        summary: `Checked ${results.checkedCount} assets, removed ${results.deletedCount} deleted files`
      }
    });
  } catch (error) {
    console.error('❌ API: Error during cleanup:', error);
    return NextResponse.json(
      {
        error: 'Failed to cleanup deleted files',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
