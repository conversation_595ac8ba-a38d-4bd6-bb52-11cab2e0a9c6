import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

// Use the singleton instance
const indexingService = IndexingService.getInstance();

export async function GET() {
  try {
    console.log('Quick file scan requested via API');
    
    // Trigger a quick scan to count total files
    await indexingService.quickScan();
    
    // Get the latest status after the quick scan
    const status = await indexingService.getIndexingStatus();
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Quick file scan completed',
      status
    });
  } catch (error) {
    console.error('Error during quick file scan:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to perform quick file scan',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 