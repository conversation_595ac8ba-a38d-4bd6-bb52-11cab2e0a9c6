import { NextRequest, NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

export async function POST(request: NextRequest) {
  try {
    const indexingService = IndexingService.getInstance();
    
    console.log('🧹 API: Starting comprehensive cleanup of all assets...');
    
    // Force comprehensive cleanup of all deleted files
    const results = await indexingService.comprehensiveCleanupDeletedFiles();
    
    console.log(`✅ API: Comprehensive cleanup completed - ${results.deletedCount} deleted, ${results.checkedCount} checked`);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Comprehensive cleanup completed successfully',
      results: {
        deletedCount: results.deletedCount,
        checkedCount: results.checkedCount,
        summary: `Checked ${results.checkedCount} assets, removed ${results.deletedCount} deleted files`
      }
    });
  } catch (error) {
    console.error('❌ API: Error during comprehensive cleanup:', error);
    return NextResponse.json(
      { 
        error: 'Failed to perform comprehensive cleanup',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const indexingService = IndexingService.getInstance();
    
    // Get current indexing status
    const status = indexingService.getIndexingStatus();
    
    return NextResponse.json({
      success: true,
      message: 'Comprehensive cleanup status',
      status,
      note: 'Use POST to trigger comprehensive cleanup of all assets'
    });
  } catch (error) {
    console.error('❌ API: Error getting cleanup status:', error);
    return NextResponse.json(
      { error: 'Failed to get cleanup status' },
      { status: 500 }
    );
  }
}
