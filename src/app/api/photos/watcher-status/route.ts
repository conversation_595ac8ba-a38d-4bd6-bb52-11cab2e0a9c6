import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

const indexingService = IndexingService.getInstance();

export async function GET() {
  try {
    const watcherStatus = indexingService.getWatcherStatus();
    const indexingStatus = indexingService.getIndexingStatus();
    
    return NextResponse.json({
      success: true,
      watcher: watcherStatus,
      indexing: indexingStatus
    });
  } catch (error) {
    console.error('Error getting watcher status:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to get watcher status',
      errorDetails: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 