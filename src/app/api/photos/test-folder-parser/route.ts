import { NextResponse } from 'next/server';
import { testFolderDateParser } from '@/lib/utils/folderDateParser';

export async function GET() {
  try {
    console.log('🧪 Testing folder date parser...');
    
    // Capture console output
    const originalLog = console.log;
    const logs: string[] = [];
    console.log = (...args) => {
      logs.push(args.join(' '));
      originalLog(...args);
    };
    
    // Run the test
    testFolderDateParser();
    
    // Restore console.log
    console.log = originalLog;
    
    return NextResponse.json({
      success: true,
      message: 'Folder date parser test completed',
      logs
    });
  } catch (error) {
    console.error('Error testing folder date parser:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to test folder date parser',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
