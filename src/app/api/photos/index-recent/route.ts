import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

const indexingService = IndexingService.getInstance();

export async function POST(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '7');
    
    console.log(`🚀 Manual trigger: Fast indexing recent files (last ${days} days)`);
    
    // Trigger fast indexing
    await indexingService.fastIndexRecentFiles(days);
    
    // Get updated status
    const status = indexingService.getIndexingStatus();
    
    return NextResponse.json({
      success: true,
      message: `Fast indexing completed for files from last ${days} days`,
      processed: status.current,
      isIndexing: status.isIndexing
    });
  } catch (error) {
    console.error('Error in fast indexing:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to perform fast indexing',
      errorDetails: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 