import { NextResponse } from 'next/server';
// import { storageService } from '@/lib/storage/storageService';
// import path from 'path';
import fs from 'fs';
import sharp from 'sharp';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const thumbnail = searchParams.get('thumbnail') === 'true';
    
    // Decode the base64 ID back to the file path
    const filePath = Buffer.from(params.id, 'base64').toString();
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error('File not found:', filePath);
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }
    
    // Read the file
    const fileBuffer = fs.readFileSync(filePath);
    
    // If thumbnail is requested, resize the image
    if (thumbnail) {
      try {
        const resizedBuffer = await sharp(fileBuffer)
          .resize(200, 200, {
            fit: 'cover',
            position: 'center'
          })
          .jpeg({ quality: 80 })
          .toBuffer();
        
        return new NextResponse(resizedBuffer, {
          headers: {
            'Content-Type': 'image/jpeg',
            'Cache-Control': 'public, max-age=31536000'
          }
        });
      } catch (error) {
        console.error('Error generating thumbnail:', error);
        // If thumbnail generation fails, return the original image
        return new NextResponse(fileBuffer, {
          headers: {
            'Content-Type': 'image/jpeg',
            'Cache-Control': 'public, max-age=31536000'
          }
        });
      }
    }
    
    // Return the original file
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000'
      }
    });
  } catch (error) {
    console.error('Error serving photo:', error);
    return NextResponse.json(
      { error: 'Failed to serve photo' },
      { status: 500 }
    );
  }
} 