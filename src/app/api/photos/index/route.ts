import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

// Use the singleton instance
const indexingService = IndexingService.getInstance();

export async function POST() {
  try {
    console.log('Manual indexing requested via API');
    
    // Force start the indexing process
    indexingService.startIndexing(true).catch(error => {
      console.error('Error during manual indexing:', error);
    });
    
    // Return immediate success response since indexing runs in the background
    return NextResponse.json({
      success: true,
      message: 'Indexing started in background',
      status: indexingService.getIndexingStatus()
    });
  } catch (error) {
    console.error('Error initiating indexing:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to start indexing',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 