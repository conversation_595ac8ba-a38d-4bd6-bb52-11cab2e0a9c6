import { NextResponse } from 'next/server';
import { OptimizedIndexingService } from '@/lib/storage/optimizedIndexingService';

// Use the singleton instance
const optimizedService = OptimizedIndexingService.getInstance();

export async function GET(request: Request) {
  const startTime = Date.now();
  
  try {
    console.log('🚀 Optimized Photos API route called');
    const { searchParams } = new URL(request.url);
    
    // Parse parameters
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 200); // Cap at 200
    const offset = parseInt(searchParams.get('offset') || '0');
    const type = searchParams.get('type') || undefined;
    
    // Date filters
    const year = searchParams.get('year') || undefined;
    const month = searchParams.get('month') || undefined;
    const day = searchParams.get('day') || undefined;
    
    const filters = { year, month, day, type };
    
    console.log('📊 Query parameters:', { limit, offset, filters });
    
    // Get assets using optimized service
    const assets = await optimizedService.getAssetsOptimized(limit, offset, filters);
    
    // Transform for API response
    const processedAssets = assets.map(asset => ({
      id: asset.id,
      title: asset.filename,
      description: '',
      filename: asset.filename,
      fileUrl: asset.fileUrl,
      thumbnailUrl: asset.thumbnailUrl,
      filePath: asset.filePath,
      type: asset.type,
      size: asset.size,
      lastModified: asset.lastModified.toISOString(),
      createdAt: asset.createdAt.toISOString(),
      updatedAt: asset.updatedAt.toISOString(),
      metadata: asset.metadata || {},
      tags: [],
      collectionId: undefined
    }));
    
    const queryTime = Date.now() - startTime;
    
    // Get performance metrics
    const metrics = optimizedService.getMetrics();
    
    console.log(`✅ Optimized API response: ${processedAssets.length} assets in ${queryTime}ms`);
    
    return NextResponse.json({
      success: true,
      assets: processedAssets,
      total: offset + assets.length + (assets.length === limit ? limit : 0), // Estimated
      hasMore: assets.length === limit,
      performance: {
        queryTime,
        assetsReturned: processedAssets.length,
        metrics: {
          filesProcessed: metrics.filesProcessed,
          averageProcessingTime: Math.round(metrics.averageProcessingTime),
          errorsCount: metrics.errorsCount
        }
      },
      note: 'Optimized endpoint with performance improvements'
    });
    
  } catch (error) {
    const errorTime = Date.now() - startTime;
    console.error('❌ Error in optimized photos API:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error fetching photos from optimized service',
      errorDetails: error instanceof Error ? error.message : String(error),
      assets: [],
      total: 0,
      hasMore: false,
      performance: {
        queryTime: errorTime,
        assetsReturned: 0
      }
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const { action, filePaths } = await request.json();
    
    if (action === 'batch-process' && Array.isArray(filePaths)) {
      console.log(`🚀 Batch processing ${filePaths.length} files`);
      
      const result = await optimizedService.batchProcessFiles(filePaths);
      
      return NextResponse.json({
        success: true,
        message: 'Batch processing completed',
        result: {
          processed: result.processed,
          errors: result.errors,
          total: filePaths.length
        },
        metrics: optimizedService.getMetrics()
      });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action or parameters'
    }, { status: 400 });
    
  } catch (error) {
    console.error('❌ Error in optimized photos POST:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error processing batch request',
      errorDetails: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
