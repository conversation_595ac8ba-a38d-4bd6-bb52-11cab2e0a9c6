import { NextRequest, NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

export async function POST(request: NextRequest) {
  try {
    const indexingService = IndexingService.getInstance();

    console.log('🚀 API: Starting smart recent refresh (last 7 days)...');

    // Trigger smart refresh for last 7 days to catch newly added folders
    await indexingService.fastIndexRecentFiles(7, true); // Check last 7 days for new folders

    // Also cleanup deleted files to keep database synchronized
    console.log('🧹 API: Cleaning up deleted files from database...');
    let deletedCount = 0;
    try {
      const cleanupResults = await indexingService.forceCleanupDeletedFiles();
      deletedCount = cleanupResults.deletedCount;
      console.log(`🧹 API: Cleanup completed - ${deletedCount} deleted files removed`);
    } catch (error) {
      console.error('⚠️ API: Cleanup error (non-critical):', error);
      // Don't fail the entire refresh if cleanup fails
    }

    console.log('✅ API: Smart recent refresh completed successfully');

    return NextResponse.json({
      success: true,
      message: `Smart recent refresh completed successfully${deletedCount > 0 ? ` - ${deletedCount} deleted files cleaned up` : ''}`,
      note: 'Checked folders and files modified in the last 7 days to detect newly added content and cleaned up deleted files'
    });
  } catch (error) {
    console.error('❌ API: Error during smart refresh:', error);
    return NextResponse.json(
      {
        error: 'Failed to perform smart refresh',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const indexingService = IndexingService.getInstance();
    
    // Get current indexing status
    const status = indexingService.getIndexingStatus();
    
    return NextResponse.json({
      success: true,
      message: 'Smart refresh status',
      status,
      note: 'Use POST to trigger smart folder-based refresh (much faster than comprehensive cleanup)'
    });
  } catch (error) {
    console.error('❌ API: Error getting smart refresh status:', error);
    return NextResponse.json(
      { error: 'Failed to get smart refresh status' },
      { status: 500 }
    );
  }
}
