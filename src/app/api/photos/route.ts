import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

// Use the singleton instance instead of creating a new one each time
const indexingService = IndexingService.getInstance();

export async function GET(request: Request) {
  try {
    console.log('Photos API route called');
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const refresh = searchParams.get('refresh') === 'true';
    
    // Get date-based filter parameters
    const year = searchParams.get('year') || undefined;
    const month = searchParams.get('month') || undefined;
    const day = searchParams.get('day') || undefined;
    
    // Only create filters object if at least one filter is present
    const filters = year || month || day 
      ? { year, month, day } 
      : undefined;
    
    console.log('Getting assets with pagination:', { limit, offset, filters, refresh });
    
    // Handle refresh parameter - wait for indexing to complete when refresh=true
    let refreshResults = null;
    if (refresh) {
      console.log('🚀 Refresh requested - checking for files added today...');
      try {
        // Wait for smart refresh to complete - check last 7 days to catch newly added folders
        refreshResults = await indexingService.fastIndexRecentFiles(7, true); // Check last 7 days for new folders
        console.log('✅ Smart refresh completed, now fetching fresh assets...');

        // Ensure we always have refresh results, even if no changes were detected
        if (!refreshResults) {
          refreshResults = {
            newAssets: 0,
            updatedAssets: 0,
            deletedAssets: 0,
            foldersChecked: 0
          };
          console.log('📊 No changes detected during refresh');
        } else {
          console.log(`📊 Refresh results: ${refreshResults.newAssets} new, ${refreshResults.updatedAssets} updated, ${refreshResults.deletedAssets} deleted`);
        }
      } catch (error) {
        console.error('❌ Error during refresh indexing:', error);
        // Provide default refresh results even if refresh fails
        refreshResults = {
          newAssets: 0,
          updatedAssets: 0,
          deletedAssets: 0,
          foldersChecked: 0
        };
      }
    }

    // PERFORMANCE: Use getLatestAssets for better performance when no filters are applied
    const assets = filters
      ? await indexingService.getAssets(limit, offset, filters)
      : await indexingService.getLatestAssets(limit, offset);

    console.log(`Found ${assets.length} assets with filters:`, filters);

    // Get actual total count when filters are applied (important for filter feedback)
    let total = 0;
    if (filters) {
      console.log('🔢 Getting exact total count for filtered results...');
      total = await indexingService.getTotalCount(filters);
      console.log(`🔢 Exact filtered total: ${total}`);
    } else {
      // For unfiltered results, get the actual database count
      console.log('🔢 Getting actual total count from database...');
      total = await indexingService.getTotalCount();
      console.log(`🔢 Actual total (no filters): ${total}`);
    }

    // Convert dates to ISO strings for JSON serialization
    const processedAssets = assets.map(asset => ({
      id: asset.id,
      title: asset.filename, // Use filename as title
      description: '', // Default empty description
      filename: asset.filename,
      fileUrl: asset.fileUrl,
      thumbnailUrl: asset.thumbnailUrl,
      filePath: asset.filePath,
      type: asset.type,
      size: asset.size,
      lastModified: asset.lastModified.toISOString(),
      createdAt: asset.createdAt.toISOString(),
      updatedAt: asset.updatedAt.toISOString(),
      metadata: asset.metadata || {},
      tags: [], // Default empty tags array
      collectionId: undefined // Default undefined collection
    }));

    console.log('📦 API Response structure:', {
      success: true,
      assetsCount: processedAssets.length,
      firstAsset: processedAssets[0] ? { id: processedAssets[0].id, filename: processedAssets[0].filename } : null,
      total: total,
      hasMore: assets.length === limit,
      filters: filters
    });

    const response: any = {
      success: true,
      assets: processedAssets,
      total: total,
      hasMore: assets.length === limit,
      filters: filters, // Include applied filters in response
      note: 'Actual total count from database'
    };

    // Include refresh results if this was a refresh request
    if (refreshResults) {
      response.refreshResults = refreshResults;
      response.refreshNote = `Refresh completed: ${refreshResults.newAssets} new, ${refreshResults.updatedAssets} updated, ${refreshResults.deletedAssets} deleted from ${refreshResults.foldersChecked} folders`;
    }

    const jsonResponse = NextResponse.json(response);

    // Add smart caching headers - cache for 30 seconds for better performance
    jsonResponse.headers.set('Cache-Control', 'public, max-age=30, stale-while-revalidate=60');
    jsonResponse.headers.set('ETag', `"${Date.now()}-${response.assets?.length || 0}"`);
    jsonResponse.headers.set('Vary', 'Accept-Encoding');

    return jsonResponse;
  } catch (error) {
    console.error('Error fetching photos:', error);
    
    // Return error response with cache-busting headers
    const errorResponse = NextResponse.json({
      success: false,
      error: 'Error fetching photos from database',
      errorDetails: error instanceof Error ? error.message : String(error),
      assets: [],
      total: 0,
      hasMore: false
    }, { status: 500 });

    // Add cache-busting headers
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    errorResponse.headers.set('Pragma', 'no-cache');
    errorResponse.headers.set('Expires', '0');

    return errorResponse;
  }
} 