import { NextResponse } from 'next/server';
import { db } from '@/lib/db/schema';
import { assets } from '@/lib/db/schema';
import { like, or } from 'drizzle-orm';

export async function POST() {
  try {
    console.log('🧹 Starting cleanup of test files...');
    
    // Find test files in the database
    const testFiles = await db.select()
      .from(assets)
      .where(
        or(
          like(assets.filename, '%test%'),
          like(assets.filename, '%cleanup%'),
          like(assets.filePath, '%test%'),
          like(assets.filePath, '%cleanup%'),
          like(assets.filePath, '%2025/06JUN2025folder%') // Specific test folder
        )
      );
    
    console.log(`🔍 Found ${testFiles.length} test files to remove:`);
    testFiles.forEach(file => {
      console.log(`  - ${file.filename} (${file.filePath})`);
    });
    
    if (testFiles.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No test files found to cleanup',
        removedCount: 0
      });
    }
    
    // Remove test files from database
    const result = await db.delete(assets)
      .where(
        or(
          like(assets.filename, '%test%'),
          like(assets.filename, '%cleanup%'),
          like(assets.filePath, '%test%'),
          like(assets.filePath, '%cleanup%'),
          like(assets.filePath, '%2025/06JUN2025folder%') // Specific test folder
        )
      );
    
    console.log(`✅ Removed ${testFiles.length} test files from database`);
    
    return NextResponse.json({
      success: true,
      message: `Successfully removed ${testFiles.length} test files`,
      removedCount: testFiles.length,
      removedFiles: testFiles.map(f => ({ filename: f.filename, filePath: f.filePath }))
    });
    
  } catch (error) {
    console.error('❌ Error cleaning up test files:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to cleanup test files',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
