import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';
import { db } from '@/lib/db/schema';
import { assets } from '@/lib/db/schema';

const indexingService = IndexingService.getInstance();

export async function POST() {
  try {
    console.log('🚀 Starting full reindex with folder-based dates...');
    
    // Get current asset count
    const currentAssets = await db.select().from(assets).limit(1);
    const hasAssets = currentAssets.length > 0;
    
    if (hasAssets) {
      console.log('📊 Database has existing assets - will update dates based on folder structure');
    }
    
    // Force a complete reindex
    console.log('🔄 Starting comprehensive indexing...');
    await indexingService.startIndexing(true);
    
    // Get updated status
    const status = indexingService.getIndexingStatus();
    
    return NextResponse.json({
      success: true,
      message: 'Full reindex started - dates will be updated based on folder structure',
      status,
      note: 'This process will scan all folders and update asset dates based on folder naming patterns. Check /api/photos/status for progress.'
    });
  } catch (error) {
    console.error('❌ Error starting full reindex:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to start full reindex',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const status = indexingService.getIndexingStatus();
    
    return NextResponse.json({
      success: true,
      message: 'Full reindex status',
      status,
      note: 'Use POST to start full reindex with folder-based date extraction'
    });
  } catch (error) {
    console.error('❌ Error getting reindex status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get reindex status'
    }, { status: 500 });
  }
}
