import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

const indexingService = IndexingService.getInstance();

export async function POST(request: Request) {
  try {
    const { action } = await request.json();
    
    switch (action) {
      case 'start':
        indexingService.startFileWatcher();
        return NextResponse.json({
          success: true,
          message: 'File watcher started',
          status: indexingService.getWatcherStatus()
        });
        
      case 'stop':
        indexingService.stopFileWatcher();
        return NextResponse.json({
          success: true,
          message: 'File watcher stopped',
          status: indexingService.getWatcherStatus()
        });
        
      case 'restart':
        indexingService.stopFileWatcher();
        // Wait a moment before restarting
        setTimeout(() => {
          indexingService.startFileWatcher();
        }, 2000);
        return NextResponse.json({
          success: true,
          message: 'File watcher restarting...',
          status: indexingService.getWatcherStatus()
        });
        
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: start, stop, or restart'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Error controlling file watcher:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to control file watcher',
      errorDetails: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const status = indexingService.getWatcherStatus();
    return NextResponse.json({
      success: true,
      status
    });
  } catch (error) {
    console.error('Error getting watcher status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get watcher status',
      errorDetails: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 