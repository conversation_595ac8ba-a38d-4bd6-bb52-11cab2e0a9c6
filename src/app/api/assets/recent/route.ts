import { NextResponse } from 'next/server';
import { db, assets } from '@/lib/db/schema';
import { desc, gte } from 'drizzle-orm';

// GET /api/assets/recent - Get recently imported assets (last 30 days)
export async function GET() {
  try {
    console.log('📋 Fetching recent assets...');

    // Get assets from the last 30 days based on createdAt (when they were indexed)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentAssets = await db
      .select()
      .from(assets)
      .where(gte(assets.createdAt, thirtyDaysAgo))
      .orderBy(desc(assets.createdAt))
      .limit(200); // Limit to 200 most recent

    console.log(`✅ Found ${recentAssets.length} recent assets (last 30 days)`);

    return NextResponse.json({
      success: true,
      assets: recentAssets,
      total: recentAssets.length,
      period: '30 days'
    });

  } catch (error) {
    console.error('❌ Error fetching recent assets:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch recent assets',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
