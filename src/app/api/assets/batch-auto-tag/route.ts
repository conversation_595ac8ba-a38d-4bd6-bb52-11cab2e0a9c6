import { NextRequest, NextResponse } from 'next/server';
import { db, assets } from '@/lib/db/schema';
import { eq, inArray } from 'drizzle-orm';
import { AITaggingService } from '@/lib/services/aiTaggingService';

// POST /api/assets/batch-auto-tag - Auto-tag multiple assets using AI
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetIds, limit = 10 } = body;

    if (!Array.isArray(assetIds) || assetIds.length === 0) {
      return NextResponse.json(
        { error: 'assetIds must be a non-empty array' },
        { status: 400 }
      );
    }

    // Limit the number of assets to process at once to avoid overwhelming the AI service
    const limitedAssetIds = assetIds.slice(0, Math.min(limit, 20));

    console.log(`🚀 Batch auto-tagging request for ${limitedAssetIds.length} assets`);

    // Get all requested assets that are images
    const assetsToProcess = await db
      .select({
        id: assets.id,
        filename: assets.filename,
        fileUrl: assets.fileUrl,
        type: assets.type,
      })
      .from(assets)
      .where(inArray(assets.id, limitedAssetIds));

    // Filter to only images
    const imageAssets = assetsToProcess.filter(asset => asset.type === 'image');

    if (imageAssets.length === 0) {
      return NextResponse.json(
        { error: 'No image assets found to process' },
        { status: 400 }
      );
    }

    console.log(`📸 Processing ${imageAssets.length} image assets`);

    // Perform batch auto-tagging
    const results = await AITaggingService.batchAutoTag(
      imageAssets.map(asset => ({
        id: asset.id,
        imageUrl: asset.fileUrl,
      }))
    );

    // Calculate summary statistics
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const totalTagsApplied = successful.reduce((sum, r) => sum + r.appliedTags.length, 0);
    const totalSuggestions = successful.reduce((sum, r) => sum + r.suggestedTags.length, 0);

    console.log(`✅ Batch auto-tagging completed:`);
    console.log(`   - Successful: ${successful.length}/${results.length}`);
    console.log(`   - Total tags applied: ${totalTagsApplied}`);
    console.log(`   - Total suggestions: ${totalSuggestions}`);

    return NextResponse.json({
      success: true,
      summary: {
        totalProcessed: results.length,
        successful: successful.length,
        failed: failed.length,
        totalTagsApplied,
        totalSuggestions,
      },
      results: results.map(result => ({
        assetId: result.assetId,
        success: result.success,
        appliedTagsCount: result.appliedTags.length,
        suggestionsCount: result.suggestedTags.length,
        error: result.error,
        // Include detailed results for successful ones
        ...(result.success && {
          appliedTags: result.appliedTags,
          suggestions: result.suggestedTags,
        })
      })),
      // Include failed assets for debugging
      failedAssets: failed.map(f => ({
        assetId: f.assetId,
        error: f.error,
      })),
    });

  } catch (error) {
    console.error('❌ Batch auto-tagging API error:', error);
    return NextResponse.json(
      { 
        error: 'Batch auto-tagging request failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}
