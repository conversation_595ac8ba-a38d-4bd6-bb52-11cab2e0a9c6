import { NextResponse } from 'next/server';
import { db, assets } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

// GET /api/assets/starred - Get all starred assets
export async function GET() {
  try {
    console.log('📋 Fetching starred assets...');

    const starredAssets = await db
      .select()
      .from(assets)
      .where(eq(assets.isStarred, true))
      .orderBy(desc(assets.updatedAt), desc(assets.createdAt));

    console.log(`✅ Found ${starredAssets.length} starred assets`);

    return NextResponse.json({
      success: true,
      assets: starredAssets,
      total: starredAssets.length
    });

  } catch (error) {
    console.error('❌ Error fetching starred assets:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch starred assets',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
