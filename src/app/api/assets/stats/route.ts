import { NextResponse } from 'next/server';
import { db, assets } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

// GET /api/assets/stats - Get asset statistics including counts by type
export async function GET() {
  try {
    console.log('📊 ===== ASSET STATS API CALLED =====');
    console.log('📊 Time:', new Date().toISOString());
    console.log('📊 Fetching asset statistics...');

    // Get total count
    const totalResult = await db.select({
      count: sql<string>`count(*)::text`
    }).from(assets);
    const total = parseInt(totalResult[0]?.count || '0', 10);

    // Get counts by type
    const typeCountsResult = await db.select({
      type: assets.type,
      count: sql<string>`count(*)::text`
    })
    .from(assets)
    .groupBy(assets.type)
    .orderBy(assets.type);

    // Convert to a more usable format and ensure numbers
    const typeCounts = typeCountsResult.reduce((acc, row) => {
      acc[row.type] = parseInt(row.count || '0', 10);
      return acc;
    }, {} as Record<string, number>);

    // Categorize types into broader categories
    const stats = {
      total,
      images: (typeCounts.image || 0) + (typeCounts.jpg || 0) + (typeCounts.jpeg || 0) + (typeCounts.png || 0) + (typeCounts.gif || 0) + (typeCounts.webp || 0) + (typeCounts.bmp || 0) + (typeCounts.tiff || 0),
      videos: (typeCounts.video || 0) + (typeCounts.mp4 || 0) + (typeCounts.avi || 0) + (typeCounts.mov || 0) + (typeCounts.mkv || 0) + (typeCounts.webm || 0) + (typeCounts.flv || 0),
      audio: (typeCounts.audio || 0) + (typeCounts.mp3 || 0) + (typeCounts.wav || 0) + (typeCounts.flac || 0) + (typeCounts.aac || 0) + (typeCounts.ogg || 0),
      documents: (typeCounts.document || 0) + (typeCounts.pdf || 0) + (typeCounts.doc || 0) + (typeCounts.docx || 0) + (typeCounts.txt || 0) + (typeCounts.rtf || 0),
      byType: typeCounts
    };

    console.log('📊 Asset statistics:', stats);
    console.log('📊 ===== END ASSET STATS API =====');

    const response = NextResponse.json({
      success: true,
      stats
    });

    // Add cache-busting headers
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;

  } catch (error) {
    console.error('❌ Error fetching asset statistics:', error);
    
    const errorResponse = NextResponse.json({
      success: false,
      error: 'Failed to fetch asset statistics',
      errorDetails: error instanceof Error ? error.message : String(error)
    }, { status: 500 });

    // Add cache-busting headers
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    errorResponse.headers.set('Pragma', 'no-cache');
    errorResponse.headers.set('Expires', '0');

    return errorResponse;
  }
}
