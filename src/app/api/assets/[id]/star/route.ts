import { NextRequest, NextResponse } from 'next/server';
import { db, assets } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// POST /api/assets/[id]/star - Toggle starred status
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: assetId } = await params;
    const body = await request.json();
    const { isStarred } = body;

    console.log(`⭐ Toggling star status for asset ${assetId} to ${isStarred}`);

    // Check if asset exists
    const asset = await db
      .select()
      .from(assets)
      .where(eq(assets.id, assetId))
      .limit(1);

    if (asset.length === 0) {
      return NextResponse.json(
        { error: 'Asset not found' },
        { status: 404 }
      );
    }

    // Update starred status
    const [updatedAsset] = await db
      .update(assets)
      .set({ 
        isStarred: <PERSON><PERSON>an(isStarred),
        updatedAt: new Date()
      })
      .where(eq(assets.id, assetId))
      .returning();

    console.log(`✅ Asset ${assetId} star status updated to ${updatedAsset.isStarred}`);

    return NextResponse.json({
      success: true,
      asset: updatedAsset,
      message: `Asset ${isStarred ? 'starred' : 'unstarred'} successfully`
    });

  } catch (error) {
    console.error('❌ Error updating star status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update star status',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
