import { NextRequest, NextResponse } from 'next/server';
import { db, assets } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { AITaggingService } from '@/lib/services/aiTaggingService';

// POST /api/assets/[id]/auto-tag - Auto-tag a specific asset using AI
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: assetId } = await params;
    
    console.log(`🤖 Auto-tagging request for asset: ${assetId}`);

    // Check if asset exists and is an image
    const asset = await db
      .select()
      .from(assets)
      .where(eq(assets.id, assetId))
      .limit(1);

    if (asset.length === 0) {
      return NextResponse.json(
        { error: 'Asset not found' },
        { status: 404 }
      );
    }

    const assetData = asset[0];

    if (assetData.type !== 'image') {
      return NextResponse.json(
        { error: 'Auto-tagging is only supported for images' },
        { status: 400 }
      );
    }

    // Get the request body for any options
    const body = await request.json().catch(() => ({}));
    const { forceRetag = false } = body;

    console.log(`📸 Processing image: ${assetData.filename}`);
    console.log(`🔗 Image URL: ${assetData.fileUrl}`);

    // Perform auto-tagging
    const result = await AITaggingService.autoTagAsset(assetId, assetData.fileUrl);

    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Auto-tagging failed', 
          details: result.error,
          assetId,
          filename: assetData.filename
        },
        { status: 500 }
      );
    }

    console.log(`✅ Auto-tagging completed for ${assetData.filename}`);
    console.log(`🏷️ Applied ${result.appliedTags.length} tags, suggested ${result.suggestedTags.length} total`);

    return NextResponse.json({
      success: true,
      assetId,
      filename: assetData.filename,
      result: {
        appliedTagsCount: result.appliedTags.length,
        totalSuggestionsCount: result.suggestedTags.length,
        appliedTags: result.appliedTags,
        allSuggestions: result.suggestedTags,
        analysis: result.analysis,
      }
    });

  } catch (error) {
    console.error('❌ Auto-tagging API error:', error);
    return NextResponse.json(
      { 
        error: 'Auto-tagging request failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}
