import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { imageUrl, prompt } = await request.json();
    
    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }
    
    const defaultPrompt = prompt || "Analyze this image in detail. Include information about: 1) What's in the image 2) Any notable objects, people, or scenes 3) The composition, lighting, and style 4) Any text visible in the image 5) The overall mood or theme. Format your response in clear paragraphs.";
    
    // Convert relative URL to absolute if needed
    let processedUrl = imageUrl;
    if (imageUrl.startsWith('/')) {
      const origin = request.headers.get('origin') || 
                    request.headers.get('host') ? 
                    `${request.headers.get('x-forwarded-proto') || 'http'}://${request.headers.get('host')}` :
                    'http://localhost:3000';
      processedUrl = `${origin}${imageUrl}`;
    }
    
    console.log('Direct Vision API Test - Processing:', {
      originalUrl: imageUrl,
      processedUrl,
      prompt: defaultPrompt
    });
    
    // Try different request formats to see what works

    // Format 1: Standard OpenAI vision format
    const requestBody1 = {
      model: 'azure-gpt-4o',
      webId: '682085ef6916da00137ad972',
      integrationId: '67e52e6e8e1618ee9ba4117f',
      messages: [
        {
          role: 'user',
          content: [
            { type: 'text', text: defaultPrompt },
            {
              type: 'image_url',
              image_url: {
                url: processedUrl
              }
            }
          ]
        }
      ],
      stream: false
    };

    // Format 2: Simple prompt + image format
    const requestBody2 = {
      model: 'azure-gpt-4o',
      webId: '682085ef6916da00137ad972',
      integrationId: '67e52e6e8e1618ee9ba4117f',
      prompt: defaultPrompt,
      image_url: processedUrl,
      stream: false
    };

    // Format 3: Text-only test to see if the basic API works
    const requestBody3 = {
      model: 'azure-gpt-4o',
      webId: '682085ef6916da00137ad972',
      integrationId: '67e52e6e8e1618ee9ba4117f',
      messages: [
        {
          role: 'user',
          content: [
            { type: 'text', text: 'Hello, this is a test message. Please respond with "API is working".' }
          ]
        }
      ],
      stream: false
    };

    // Try format 1 first (standard vision format)
    let requestBody = requestBody1;
    let formatUsed = 'Standard OpenAI Vision Format';
    
    console.log(`Direct Vision API Request Body (${formatUsed}):`, JSON.stringify(requestBody, null, 2));

    let response = await fetch('https://proxy.getcreatr.com/v1/proxy/llm', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody),
    });

    // If format 1 fails, try format 3 (text-only test)
    if (!response.ok && formatUsed === 'Standard OpenAI Vision Format') {
      console.log('Format 1 failed, trying text-only test...');
      requestBody = requestBody3;
      formatUsed = 'Text-Only Test';

      response = await fetch('https://proxy.getcreatr.com/v1/proxy/llm', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      });
    }
    
    console.log(`Direct Vision API Response (${formatUsed}):`, {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      bodyUsed: response.bodyUsed
    });
    
    if (!response.ok) {
      let errorText = '';
      try {
        errorText = await response.text();
      } catch (e) {
        errorText = 'Could not read error response';
      }
      
      return NextResponse.json({
        error: 'Vision API error',
        status: response.status,
        statusText: response.statusText,
        details: errorText,
        formatUsed,
        requestBody: JSON.stringify(requestBody, null, 2)
      }, { status: response.status });
    }
    
    const data = await response.json();
    console.log('Direct Vision API Success:', data);
    
    return NextResponse.json({
      success: true,
      text: data.data?.choices?.[0]?.message?.content || data.choices?.[0]?.message?.content || 'No content in response',
      usage: data.data?.usage || data.usage || {},
      formatUsed,
      rawResponse: data
    });
    
  } catch (error) {
    console.error('Direct vision API test error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to analyze image', 
        details: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
