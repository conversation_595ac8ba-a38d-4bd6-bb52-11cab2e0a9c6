import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('Testing basic text API...');
    
    const requestBody = {
      model: 'azure-gpt-4o',
      webId: '682085ef6916da00137ad972',
      integrationId: '67e52e6e8e1618ee9ba4117f',
      prompt: 'Hello, this is a test message. Please respond with "API is working" and nothing else.',
      stream: false
    };
    
    console.log('Text API Request Body:', JSON.stringify(requestBody, null, 2));
    
    const response = await fetch('https://proxy.getcreatr.com/v1/proxy/llm', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody),
    });
    
    console.log('Text API Response:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      bodyUsed: response.bodyUsed
    });
    
    if (!response.ok) {
      let errorText = '';
      try {
        errorText = await response.text();
      } catch (e) {
        errorText = 'Could not read error response';
      }
      
      return NextResponse.json({
        error: 'Text API error',
        status: response.status,
        statusText: response.statusText,
        details: errorText
      }, { status: response.status });
    }
    
    const data = await response.json();
    console.log('Text API Success:', data);
    
    return NextResponse.json({
      success: true,
      text: data.data?.choices?.[0]?.message?.content || data.choices?.[0]?.message?.content || 'No content in response',
      usage: data.data?.usage || data.usage || {},
      rawResponse: data
    });
    
  } catch (error) {
    console.error('Text API test error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to test text API', 
        details: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
