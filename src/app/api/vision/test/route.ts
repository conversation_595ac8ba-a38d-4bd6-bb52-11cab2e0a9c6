import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { imageUrl } = await request.json();
    
    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }
    
    // Convert relative URL to absolute if needed
    let processedUrl = imageUrl;
    if (imageUrl.startsWith('/')) {
      const origin = request.headers.get('origin') || 
                    request.headers.get('host') ? 
                    `${request.headers.get('x-forwarded-proto') || 'http'}://${request.headers.get('host')}` :
                    'http://localhost:3000';
      processedUrl = `${origin}${imageUrl}`;
    }
    
    console.log('Testing image URL accessibility:', processedUrl);
    
    // Test if the image URL is accessible
    try {
      const imageResponse = await fetch(processedUrl, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Asset-Hub-Vision-Test/1.0'
        }
      });
      
      const isAccessible = imageResponse.ok;
      const contentType = imageResponse.headers.get('content-type');
      const contentLength = imageResponse.headers.get('content-length');
      
      return NextResponse.json({
        success: true,
        originalUrl: imageUrl,
        processedUrl,
        accessible: isAccessible,
        status: imageResponse.status,
        statusText: imageResponse.statusText,
        contentType,
        contentLength,
        headers: Object.fromEntries(imageResponse.headers.entries())
      });
      
    } catch (fetchError) {
      return NextResponse.json({
        success: false,
        originalUrl: imageUrl,
        processedUrl,
        accessible: false,
        error: fetchError instanceof Error ? fetchError.message : String(fetchError)
      });
    }
    
  } catch (error) {
    console.error('Vision test error:', error);
    return NextResponse.json(
      { error: 'Failed to test image URL', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
