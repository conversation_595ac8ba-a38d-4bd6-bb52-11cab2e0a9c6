import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

export async function GET(request: NextRequest) {
  try {
    console.log('🔐 Auth Verify: Checking authentication...');
    
    // Get token from cookie
    const token = request.cookies.get('auth-token')?.value;
    
    if (!token) {
      console.log('❌ Auth Verify: No auth token found');
      return NextResponse.json(
        { success: false, error: 'No authentication token' },
        { status: 401 }
      );
    }

    // Verify JWT token
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any;
      console.log('✅ Auth Verify: Token valid for user:', decoded.email);
      
      const userData = {
        id: decoded.userId,
        email: decoded.email,
        name: decoded.name,
        role: decoded.role
      };

      return NextResponse.json({
        success: true,
        user: userData
      });

    } catch (jwtError) {
      console.log('❌ Auth Verify: Invalid token:', jwtError);
      
      // Clear invalid cookies
      const response = NextResponse.json(
        { success: false, error: 'Invalid authentication token' },
        { status: 401 }
      );
      
      response.cookies.delete('auth-token');
      response.cookies.delete('user-session');
      
      return response;
    }

  } catch (error) {
    console.error('❌ Auth Verify: Server error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}
