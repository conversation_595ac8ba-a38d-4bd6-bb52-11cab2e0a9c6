import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/services/authService';

// GET /api/auth/users - Get all users (admin only)
export async function GET(request: NextRequest) {
  try {
    console.log('👥 Fetching all users...');

    const authService = AuthService.getInstance();
    const users = await authService.getAllUsers();

    console.log(`✅ Found ${users.length} users`);

    return NextResponse.json({
      success: true,
      users,
      total: users.length
    });

  } catch (error) {
    console.error('❌ Error fetching users:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch users', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

// POST /api/auth/users - Create new user (admin only)
export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();

    if (!userData.email || !userData.password || !userData.name) {
      return NextResponse.json(
        { error: 'Email, password, and name are required' },
        { status: 400 }
      );
    }

    console.log('👤 Creating new user:', userData.email);

    const authService = AuthService.getInstance();
    const user = await authService.createUser({
      email: userData.email,
      password: userData.password,
      name: userData.name,
      role: userData.role || 'editor',
      avatar: userData.avatar,
    });

    console.log('✅ User created successfully:', user.email);

    return NextResponse.json({
      success: true,
      user,
      message: 'User created successfully'
    });

  } catch (error) {
    console.error('❌ Error creating user:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create user', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}
