import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/services/authService';

// GET /api/auth/users/[id] - Get user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('👤 Fetching user:', id);

    const authService = AuthService.getInstance();
    const user = await authService.getUserById(id);

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      user
    });

  } catch (error) {
    console.error('❌ Error fetching user:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch user', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

// PUT /api/auth/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const updates = await request.json();

    console.log('👤 Updating user:', id);

    const authService = AuthService.getInstance();
    const user = await authService.updateUser(id, updates);

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    console.log('✅ User updated successfully:', user.email);

    return NextResponse.json({
      success: true,
      user,
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating user:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update user', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

// DELETE /api/auth/users/[id] - Delete user (deactivate)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('👤 Deleting user:', id);

    const authService = AuthService.getInstance();
    const success = await authService.deleteUser(id);

    if (!success) {
      return NextResponse.json(
        { error: 'User not found or could not be deleted' },
        { status: 404 }
      );
    }

    console.log('✅ User deleted successfully');

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('❌ Error deleting user:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete user', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}
