import { NextRequest, NextResponse } from 'next/server';
import { DocumentPreviewService } from '@/lib/services/documentPreviewService';
import fs from 'fs';
import path from 'path';

// GET /api/storage/preview/[hash] - Serve document preview
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ hash: string }> }
) {
  try {
    const { hash } = await params;
    
    // Decode the hash back to relative path
    const relativePath = Buffer.from(hash.replace(/_/g, '/'), 'base64').toString();
    
    console.log(`🖼️ Serving document preview for: ${relativePath}`);
    
    const previewService = DocumentPreviewService.getInstance();
    
    // Get or generate preview
    const previewPath = await previewService.getDocumentPreview(relativePath);
    
    if (!previewPath || !fs.existsSync(previewPath)) {
      console.warn(`⚠️ Preview not found for: ${relativePath}`);
      return new NextResponse('Preview not found', { status: 404 });
    }
    
    // Read and serve the preview image
    const imageBuffer = fs.readFileSync(previewPath);
    
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
        'Content-Length': imageBuffer.length.toString(),
      },
    });
    
  } catch (error) {
    console.error('❌ Error serving document preview:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
