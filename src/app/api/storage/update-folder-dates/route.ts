import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';

const indexingService = IndexingService.getInstance();

export async function POST() {
  try {
    console.log('🔄 API: Starting folder-based date update...');
    
    // Update existing assets with folder-based dates
    await indexingService.updateExistingAssetsWithFolderDates(500); // Process in smaller batches for API
    
    // Get some sample assets to show the results
    const latestAssets = await indexingService.getLatestAssets(10, 0);
    
    const sampleResults = latestAssets.map(asset => ({
      filename: asset.filename,
      lastModified: {
        iso: asset.lastModified.toISOString(),
        year: asset.lastModified.getFullYear(),
        readable: asset.lastModified.toLocaleString()
      },
      filePath: asset.filePath
    }));
    
    console.log('📊 Sample results after folder date update:');
    sampleResults.forEach(result => {
      console.log(`📁 ${result.filename} → ${result.lastModified.readable} (${result.lastModified.year})`);
    });
    
    return NextResponse.json({
      success: true,
      message: 'Folder-based date update completed successfully',
      sampleResults,
      note: 'Assets are now sorted by folder structure dates instead of file modification times'
    });
  } catch (error) {
    console.error('Error in folder date update API:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Just return current status and sample of latest assets
    const latestAssets = await indexingService.getLatestAssets(10, 0);
    
    const currentResults = latestAssets.map(asset => ({
      filename: asset.filename,
      lastModified: {
        iso: asset.lastModified.toISOString(),
        year: asset.lastModified.getFullYear(),
        readable: asset.lastModified.toLocaleString()
      },
      filePath: asset.filePath
    }));
    
    return NextResponse.json({
      success: true,
      message: 'Current latest assets (sorted by lastModified)',
      currentResults,
      note: 'Use POST to trigger folder-based date update'
    });
  } catch (error) {
    console.error('Error getting current assets:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
