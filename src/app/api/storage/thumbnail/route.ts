import { NextRequest, NextResponse } from 'next/server';
import { ThumbnailService } from '@/lib/storage/thumbnailService';
import fs from 'fs';

const thumbnailService = ThumbnailService.getInstance();

export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    const { searchParams } = new URL(request.url);
    const filePath = searchParams.get('path');
    const size = searchParams.get('size') as 'small' | 'medium' | 'large' || 'medium';

    if (!filePath) {
      return NextResponse.json({ error: 'Path parameter is required' }, { status: 400 });
    }

    console.log(`🖼️ Thumbnail request: ${filePath} (${size})`);

    // Get or generate thumbnail
    const thumbnailPath = await thumbnailService.getThumbnail(filePath, size);

    if (!thumbnailPath || !fs.existsSync(thumbnailPath)) {
      console.warn(`⚠️ Thumbnail not available for: ${filePath}`);
      return NextResponse.json({ error: 'Thumbnail not available' }, { status: 404 });
    }

    // Read and serve thumbnail
    const thumbnailBuffer = await fs.promises.readFile(thumbnailPath);
    const processingTime = Date.now() - startTime;

    console.log(`✅ Thumbnail served in ${processingTime}ms: ${filePath}`);

    return new NextResponse(thumbnailBuffer, {
      headers: {
        'Content-Type': 'image/webp',
        'Cache-Control': 'public, max-age=31536000, immutable', // Cache for 1 year
        'X-Processing-Time': processingTime.toString(),
        'X-Thumbnail-Size': size,
      },
    });
  } catch (error) {
    const errorTime = Date.now() - startTime;
    console.error('❌ Error serving thumbnail:', error);

    return NextResponse.json({
      error: 'Failed to generate thumbnail',
      processingTime: errorTime,
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// POST endpoint for batch thumbnail generation
export async function POST(request: NextRequest) {
  try {
    const { filePaths, sizes } = await request.json();

    if (!Array.isArray(filePaths)) {
      return NextResponse.json({ error: 'filePaths must be an array' }, { status: 400 });
    }

    console.log(`🖼️ Batch thumbnail generation for ${filePaths.length} files`);

    const result = await thumbnailService.batchGenerateThumbnails(
      filePaths,
      sizes || ['small', 'medium']
    );

    const stats = await thumbnailService.getStats();

    return NextResponse.json({
      success: true,
      message: 'Batch thumbnail generation completed',
      result,
      stats
    });

  } catch (error) {
    console.error('❌ Error in batch thumbnail generation:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to generate thumbnails',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}