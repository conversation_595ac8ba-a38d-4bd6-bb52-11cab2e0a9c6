import { NextResponse } from 'next/server';
import { storageService } from '@/lib/storage/storageService';
import path from 'path';

export async function GET() {
  try {
    console.log('Scanning storage directory...');
    const assets = await storageService.scanDirectory();
    console.log('Found assets:', assets.length);
    
    // Assets from IndexingService have different interface, use fileUrl for path
    const processedAssets = assets.map(asset => ({
      ...asset,
      path: asset.fileUrl // Use the fileUrl property for path
    }));

    return NextResponse.json({ assets: processedAssets });
  } catch (error) {
    console.error('Error fetching assets:', error);
    return NextResponse.json(
      { error: 'Failed to fetch assets' },
      { status: 500 }
    );
  }
}

// WebSocket endpoint for real-time updates
export async function POST(request: Request) {
  try {
    const { action } = await request.json();
    
    if (action === 'start-watching') {
      // Start watching for changes
      storageService.startWatching((event, path) => {
        // Handle file changes
        console.log(`File ${event}: ${path}`);
      });
      
      return NextResponse.json({ status: 'watching' });
    }
    
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error handling storage action:', error);
    return NextResponse.json(
      { error: 'Failed to handle storage action' },
      { status: 500 }
    );
  }
} 