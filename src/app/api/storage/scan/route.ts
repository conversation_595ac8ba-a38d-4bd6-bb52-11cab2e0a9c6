import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';
import exifr from 'exifr';
import { IndexingService } from '@/lib/storage/indexingService';

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

const supportedImageTypes = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.tiff'];

export async function GET() {
  console.log('Scan API endpoint called');
  
  try {
    // Use the singleton instance
    const indexingService = IndexingService.getInstance();
    console.log('Using singleton indexing service instance');
    
    // Force start the indexing process - this will override the time check
    console.log('Starting forced indexing process...');
    await indexingService.startIndexing(true);
    console.log('Indexing process completed or running in background');
    
    // Get current status after starting
    const status = indexingService.getIndexingStatus();
    console.log('Current indexing status:', status);
    
    // Query the database for assets to return
    const assets = await indexingService.getAssets(100, 0);
    console.log(`Returning ${assets.length} assets from the database`);
    
    return NextResponse.json({
      success: true,
      assets,
      status
    });
  } catch (error) {
    console.error('Error during scan operation:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to scan directory',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// tracker: { assets: [], total: 0, offset, limit }
async function findImagesRecursively(basePath, dirPath, tracker) {
  if (tracker.assets.length >= tracker.limit) return;
  let files;
  try {
    files = await readdir(dirPath);
  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error);
    return;
  }
  // Sort files/folders for consistent order
  files.sort();
  for (const file of files) {
    if (tracker.assets.length >= tracker.limit) break;
    const fullPath = path.join(dirPath, file);
    let fileStat;
    try {
      fileStat = await fs.promises.stat(fullPath);
    } catch (error) {
      console.error(`Error processing ${fullPath}:`, error);
      continue;
    }
    if (fileStat.isDirectory()) {
      await findImagesRecursively(basePath, fullPath, tracker);
    } else if (isImageFile(file)) {
      tracker.total++;
      if (tracker.total <= tracker.offset) continue;
      if (tracker.assets.length < tracker.limit) {
        try {
          const metadata = await getImageMetadata(fullPath);
          const relativePath = path.relative(basePath, fullPath);
          tracker.assets.push({
            id: uuidv4(),
            filename: file,
            fileUrl: `/api/storage/file?path=${encodeURIComponent(relativePath)}`,
            thumbnailUrl: `/api/storage/thumbnail?path=${encodeURIComponent(relativePath)}`,
            type: 'image',
            size: fileStat.size,
            lastModified: fileStat.mtime,
            metadata
          });
        } catch (error) {
          console.error(`Error processing image ${fullPath}:`, error);
        }
      }
    }
  }
}

function isImageFile(filename) {
  const ext = path.extname(filename).toLowerCase();
  return supportedImageTypes.includes(ext);
}

async function getImageMetadata(filePath) {
  try {
    const metadata = await exifr.parse(filePath);
    return metadata || {};
  } catch (error) {
    console.error('Error reading image metadata:', error);
    return {};
  }
} 