export type AssetType = "image" | "video" | "audio" | "document" | "other";

export interface Asset {
  id: string;
  title: string;
  description?: string;
  filename: string;
  fileUrl: string;
  thumbnailUrl: string;
  filePath?: string;
  type: AssetType;
  size: number; // in bytes
  createdAt: string;
  updatedAt: string;
  metadata: Record<string, any>;
  tags: string[];
  collectionId?: string;
  isStarred?: boolean;
}

export interface Collection {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  parentId?: string;
  assetCount: number;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: "admin" | "editor" | "viewer";
  avatar?: string;
}

export interface Tag {
  id: string;
  name: string;
  color?: string;
}
