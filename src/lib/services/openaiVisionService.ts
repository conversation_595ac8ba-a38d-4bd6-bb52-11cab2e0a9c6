import OpenAI from 'openai';

export interface VisionAnalysisResult {
  tags: Array<{
    name: string;
    confidence: number;
    reason: string;
  }>;
  analysis: string;
  success: boolean;
  error?: string;
}

export class OpenAIVisionService {
  private static client: OpenAI | null = null;

  private static getClient(): OpenAI {
    if (!this.client) {
      const apiKey = process.env.OPENAI_API_KEY;
      if (!apiKey || apiKey === 'your_openai_api_key_here') {
        throw new Error('OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.');
      }
      this.client = new OpenAI({ apiKey });
    }
    return this.client;
  }

  /**
   * Analyze an image using OpenAI GPT-4 Vision and extract tags
   */
  static async analyzeImageForTags(imageUrl: string): Promise<VisionAnalysisResult> {
    try {
      console.log('🔍 Starting OpenAI Vision analysis for:', imageUrl);

      const client = this.getClient();

      const prompt = `Analyze this image and provide detailed tags for a digital asset management system.

Focus on identifying:
1. MAIN SUBJECTS: People, animals, objects, vehicles
2. SETTING: Indoor/outdoor, specific locations, environments  
3. ACTIVITIES: What's happening in the image
4. STYLE: Photography type, artistic style, composition
5. MOOD: Emotional tone, atmosphere, lighting
6. COLORS: Dominant colors in the image
7. TECHNICAL: Portrait/landscape, close-up/wide shot

Provide your response in this EXACT JSON format:
{
  "tags": [
    {
      "name": "descriptive_tag",
      "confidence": 0.85,
      "reason": "Brief explanation why this tag applies"
    }
  ],
  "analysis": "Brief overall description of the image content"
}

Rules:
- Use descriptive, searchable tags (not generic like "photo" or "image")
- Confidence should be realistic (0.0 to 1.0)
- Focus on content that would help someone find this image
- Maximum 8-10 tags per image
- Use lowercase with underscores for multi-word tags
- Be specific about what you actually see`;

      const response = await client.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: prompt },
              {
                type: "image_url",
                image_url: {
                  url: imageUrl,
                  detail: "high"
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.3, // Lower temperature for more consistent results
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response content from OpenAI');
      }

      console.log('🤖 OpenAI Response:', content);

      // Parse the JSON response
      const parsedResponse = this.parseResponse(content);
      
      if (!parsedResponse.tags || !Array.isArray(parsedResponse.tags)) {
        throw new Error('Invalid response format: missing tags array');
      }

      // Validate and clean up the tags
      const validatedTags = parsedResponse.tags
        .filter(tag => tag.name && typeof tag.confidence === 'number')
        .map(tag => ({
          name: tag.name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, ''),
          confidence: Math.max(0, Math.min(1, tag.confidence)),
          reason: tag.reason || 'AI identified content'
        }))
        .filter(tag => tag.name.length > 0)
        .slice(0, 10);

      console.log('✅ Validated tags:', validatedTags);

      return {
        tags: validatedTags,
        analysis: parsedResponse.analysis || 'Image analyzed successfully',
        success: true
      };

    } catch (error) {
      console.error('❌ OpenAI Vision analysis failed:', error);
      
      return {
        tags: [],
        analysis: '',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Parse the AI response, handling potential JSON formatting issues
   */
  private static parseResponse(content: string): any {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // If no JSON found, try parsing the whole content
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse AI response as JSON:', content);
      
      // Fallback: try to extract tags from text
      return this.extractTagsFromText(content);
    }
  }

  /**
   * Fallback method to extract tags from unstructured text
   */
  private static extractTagsFromText(text: string): any {
    console.log('🔄 Attempting to extract tags from unstructured text');
    
    const tags = [];
    const lines = text.split('\n');
    
    for (const line of lines) {
      // Look for patterns like "- tag_name" or "tag_name:" or numbered lists
      const tagMatch = line.match(/(?:[-*•]\s*|^\d+\.\s*|^)([a-zA-Z_][a-zA-Z0-9_\s]+)(?:\s*[:,-]|\s*$)/);
      if (tagMatch) {
        const tagName = tagMatch[1].trim().toLowerCase().replace(/\s+/g, '_');
        if (tagName.length > 2 && tagName.length < 30) {
          tags.push({
            name: tagName,
            confidence: 0.7,
            reason: 'Extracted from AI description'
          });
        }
      }
    }
    
    return {
      tags: tags.slice(0, 8),
      analysis: text.substring(0, 200) + '...'
    };
  }

  /**
   * Test the OpenAI connection
   */
  static async testConnection(): Promise<boolean> {
    try {
      const client = this.getClient();
      
      const response = await client.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: "Hello, please respond with 'Connection successful'" }],
        max_tokens: 10
      });

      const content = response.choices[0]?.message?.content;
      console.log('🔗 OpenAI connection test:', content);
      
      return content?.includes('successful') || false;
    } catch (error) {
      console.error('❌ OpenAI connection test failed:', error);
      return false;
    }
  }
}
