import path from 'path';
import fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export class DocumentPreviewService {
  private static instance: DocumentPreviewService;
  private previewDir = path.join(process.cwd(), 'storage', 'previews');
  private basePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';

  static getInstance(): DocumentPreviewService {
    if (!DocumentPreviewService.instance) {
      DocumentPreviewService.instance = new DocumentPreviewService();
    }
    return DocumentPreviewService.instance;
  }

  constructor() {
    this.ensurePreviewDirectory();
  }

  private ensurePreviewDirectory() {
    if (!fs.existsSync(this.previewDir)) {
      fs.mkdirSync(this.previewDir, { recursive: true });
      console.log(`📁 Created preview directory: ${this.previewDir}`);
    }
  }

  /**
   * Check if file type supports preview generation
   */
  isPreviewSupported(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    const supportedTypes = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.rtf', '.odt', '.ods', '.odp'
    ];
    return supportedTypes.includes(ext);
  }

  /**
   * Get preview path for a document
   */
  private getPreviewPath(relativePath: string): string {
    const hash = Buffer.from(relativePath).toString('base64').replace(/[/+=]/g, '_');
    return path.join(this.previewDir, `${hash}.jpg`);
  }

  /**
   * Generate preview for PDF documents
   */
  private async generatePdfPreview(sourcePath: string, previewPath: string): Promise<void> {
    try {
      // Use ImageMagick to convert first page of PDF to image
      const command = `convert "${sourcePath}[0]" -thumbnail 300x400 -background white -alpha remove "${previewPath}"`;
      await execAsync(command);
      console.log(`✅ Generated PDF preview: ${path.basename(previewPath)}`);
    } catch (error) {
      console.error(`❌ Error generating PDF preview:`, error);
      throw error;
    }
  }

  /**
   * Generate preview for Office documents
   */
  private async generateOfficePreview(sourcePath: string, previewPath: string): Promise<void> {
    try {
      // Use LibreOffice to convert to PDF first, then to image
      const tempPdfPath = previewPath.replace('.jpg', '.pdf');
      
      // Convert to PDF
      const convertCommand = `libreoffice --headless --convert-to pdf --outdir "${path.dirname(tempPdfPath)}" "${sourcePath}"`;
      await execAsync(convertCommand);
      
      // Convert PDF to image
      const imageCommand = `convert "${tempPdfPath}[0]" -thumbnail 300x400 -background white -alpha remove "${previewPath}"`;
      await execAsync(imageCommand);
      
      // Clean up temporary PDF
      if (fs.existsSync(tempPdfPath)) {
        fs.unlinkSync(tempPdfPath);
      }
      
      console.log(`✅ Generated Office preview: ${path.basename(previewPath)}`);
    } catch (error) {
      console.error(`❌ Error generating Office preview:`, error);
      throw error;
    }
  }

  /**
   * Generate preview for text documents
   */
  private async generateTextPreview(sourcePath: string, previewPath: string): Promise<void> {
    try {
      // Read first 1000 characters of text file
      const content = fs.readFileSync(sourcePath, 'utf-8').substring(0, 1000);
      
      // Create a simple HTML representation
      const html = `
        <html>
          <head>
            <style>
              body { font-family: monospace; padding: 20px; background: white; }
              pre { white-space: pre-wrap; word-wrap: break-word; }
            </style>
          </head>
          <body>
            <pre>${content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
          </body>
        </html>
      `;
      
      const tempHtmlPath = previewPath.replace('.jpg', '.html');
      fs.writeFileSync(tempHtmlPath, html);
      
      // Convert HTML to image using wkhtmltoimage or similar
      const command = `wkhtmltoimage --width 300 --height 400 "${tempHtmlPath}" "${previewPath}"`;
      await execAsync(command);
      
      // Clean up temporary HTML
      if (fs.existsSync(tempHtmlPath)) {
        fs.unlinkSync(tempHtmlPath);
      }
      
      console.log(`✅ Generated text preview: ${path.basename(previewPath)}`);
    } catch (error) {
      console.error(`❌ Error generating text preview:`, error);
      // Fallback: create a simple placeholder
      await this.generatePlaceholderPreview(sourcePath, previewPath);
    }
  }

  /**
   * Generate a placeholder preview for unsupported formats
   */
  private async generatePlaceholderPreview(sourcePath: string, previewPath: string): Promise<void> {
    try {
      const ext = path.extname(sourcePath).toUpperCase().substring(1);
      const filename = path.basename(sourcePath);
      
      // Create a simple SVG placeholder
      const svg = `
        <svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
          <rect width="300" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
          <text x="150" y="180" text-anchor="middle" font-family="Arial" font-size="24" fill="#6c757d">${ext}</text>
          <text x="150" y="220" text-anchor="middle" font-family="Arial" font-size="12" fill="#6c757d">Document</text>
          <text x="150" y="250" text-anchor="middle" font-family="Arial" font-size="10" fill="#6c757d" text-overflow="ellipsis">${filename.length > 30 ? filename.substring(0, 27) + '...' : filename}</text>
        </svg>
      `;
      
      const tempSvgPath = previewPath.replace('.jpg', '.svg');
      fs.writeFileSync(tempSvgPath, svg);
      
      // Convert SVG to JPG
      const command = `convert "${tempSvgPath}" "${previewPath}"`;
      await execAsync(command);
      
      // Clean up temporary SVG
      if (fs.existsSync(tempSvgPath)) {
        fs.unlinkSync(tempSvgPath);
      }
      
      console.log(`✅ Generated placeholder preview: ${path.basename(previewPath)}`);
    } catch (error) {
      console.error(`❌ Error generating placeholder preview:`, error);
      throw error;
    }
  }

  /**
   * Get or generate document preview
   */
  async getDocumentPreview(relativePath: string): Promise<string | null> {
    const sourcePath = path.join(this.basePath, relativePath);
    const previewPath = this.getPreviewPath(relativePath);
    
    try {
      // Check if source file exists
      if (!fs.existsSync(sourcePath)) {
        console.warn(`⚠️ Source file not found: ${sourcePath}`);
        return null;
      }
      
      // Check if preview already exists and is newer than source
      if (fs.existsSync(previewPath)) {
        const sourceStats = fs.statSync(sourcePath);
        const previewStats = fs.statSync(previewPath);
        
        if (previewStats.mtime >= sourceStats.mtime) {
          return previewPath;
        }
      }
      
      // Generate preview based on file type
      const ext = path.extname(sourcePath).toLowerCase();
      
      if (ext === '.pdf') {
        await this.generatePdfPreview(sourcePath, previewPath);
      } else if (['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.odt', '.ods', '.odp'].includes(ext)) {
        await this.generateOfficePreview(sourcePath, previewPath);
      } else if (['.txt', '.rtf'].includes(ext)) {
        await this.generateTextPreview(sourcePath, previewPath);
      } else {
        await this.generatePlaceholderPreview(sourcePath, previewPath);
      }
      
      return previewPath;
      
    } catch (error) {
      console.error(`❌ Error generating preview for ${relativePath}:`, error);
      
      // Try to generate a placeholder as fallback
      try {
        await this.generatePlaceholderPreview(sourcePath, previewPath);
        return previewPath;
      } catch (fallbackError) {
        console.error(`❌ Error generating fallback preview:`, fallbackError);
        return null;
      }
    }
  }

  /**
   * Check if preview exists
   */
  hasPreview(relativePath: string): boolean {
    const previewPath = this.getPreviewPath(relativePath);
    return fs.existsSync(previewPath);
  }

  /**
   * Get preview URL for serving
   */
  getPreviewUrl(relativePath: string): string {
    const hash = Buffer.from(relativePath).toString('base64').replace(/[/+=]/g, '_');
    return `/api/storage/preview/${hash}`;
  }
}
