/**
 * Performance Monitoring Service for Asset Hub
 * Tracks and analyzes application performance metrics
 */

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: Date;
  category: 'database' | 'api' | 'file_system' | 'thumbnail' | 'search';
  metadata?: Record<string, any>;
}

export interface PerformanceReport {
  summary: {
    totalRequests: number;
    averageResponseTime: number;
    slowestEndpoint: string;
    fastestEndpoint: string;
    errorRate: number;
  };
  endpoints: Record<string, {
    count: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    errors: number;
  }>;
  categories: Record<string, {
    count: number;
    averageTime: number;
    totalTime: number;
  }>;
  recommendations: string[];
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 10000; // Keep last 10k metrics
  private activeTimers = new Map<string, number>();

  private constructor() {
    console.log('📊 PerformanceMonitor initialized');
    
    // Clean up old metrics every hour
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 60 * 60 * 1000);
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start timing an operation
   */
  startTimer(operationId: string): void {
    this.activeTimers.set(operationId, Date.now());
  }

  /**
   * End timing and record metric
   */
  endTimer(
    operationId: string, 
    name: string, 
    category: PerformanceMetric['category'],
    metadata?: Record<string, any>
  ): number {
    const startTime = this.activeTimers.get(operationId);
    if (!startTime) {
      console.warn(`⚠️ No start time found for operation: ${operationId}`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.activeTimers.delete(operationId);

    this.recordMetric({
      name,
      value: duration,
      timestamp: new Date(),
      category,
      metadata: {
        ...metadata,
        operationId
      }
    });

    return duration;
  }

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log slow operations
    if (metric.value > 5000) { // 5 seconds
      console.warn(`🐌 Slow operation detected: ${metric.name} took ${metric.value}ms`);
    }
  }

  /**
   * Record API request performance
   */
  recordApiRequest(
    endpoint: string, 
    method: string, 
    duration: number, 
    statusCode: number,
    metadata?: Record<string, any>
  ): void {
    this.recordMetric({
      name: `${method} ${endpoint}`,
      value: duration,
      timestamp: new Date(),
      category: 'api',
      metadata: {
        endpoint,
        method,
        statusCode,
        isError: statusCode >= 400,
        ...metadata
      }
    });
  }

  /**
   * Record database query performance
   */
  recordDatabaseQuery(
    query: string, 
    duration: number, 
    rowCount?: number,
    metadata?: Record<string, any>
  ): void {
    this.recordMetric({
      name: `DB: ${query}`,
      value: duration,
      timestamp: new Date(),
      category: 'database',
      metadata: {
        query,
        rowCount,
        ...metadata
      }
    });
  }

  /**
   * Record file system operation performance
   */
  recordFileSystemOperation(
    operation: string, 
    duration: number, 
    fileCount?: number,
    metadata?: Record<string, any>
  ): void {
    this.recordMetric({
      name: `FS: ${operation}`,
      value: duration,
      timestamp: new Date(),
      category: 'file_system',
      metadata: {
        operation,
        fileCount,
        ...metadata
      }
    });
  }

  /**
   * Get performance report
   */
  getReport(timeRange?: { start: Date; end: Date }): PerformanceReport {
    let filteredMetrics = this.metrics;
    
    if (timeRange) {
      filteredMetrics = this.metrics.filter(
        m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end
      );
    }

    const endpointStats = new Map<string, {
      times: number[];
      errors: number;
    }>();

    const categoryStats = new Map<string, number[]>();

    // Process metrics
    filteredMetrics.forEach(metric => {
      // Endpoint stats
      if (metric.category === 'api') {
        const key = metric.name;
        if (!endpointStats.has(key)) {
          endpointStats.set(key, { times: [], errors: 0 });
        }
        const stats = endpointStats.get(key)!;
        stats.times.push(metric.value);
        if (metric.metadata?.isError) {
          stats.errors++;
        }
      }

      // Category stats
      if (!categoryStats.has(metric.category)) {
        categoryStats.set(metric.category, []);
      }
      categoryStats.get(metric.category)!.push(metric.value);
    });

    // Calculate endpoint metrics
    const endpoints: PerformanceReport['endpoints'] = {};
    let slowestEndpoint = '';
    let fastestEndpoint = '';
    let slowestTime = 0;
    let fastestTime = Infinity;

    endpointStats.forEach((stats, endpoint) => {
      const times = stats.times;
      const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);

      endpoints[endpoint] = {
        count: times.length,
        averageTime: Math.round(averageTime),
        minTime,
        maxTime,
        errors: stats.errors
      };

      if (averageTime > slowestTime) {
        slowestTime = averageTime;
        slowestEndpoint = endpoint;
      }
      if (averageTime < fastestTime) {
        fastestTime = averageTime;
        fastestEndpoint = endpoint;
      }
    });

    // Calculate category metrics
    const categories: PerformanceReport['categories'] = {};
    categoryStats.forEach((times, category) => {
      const totalTime = times.reduce((a, b) => a + b, 0);
      const averageTime = totalTime / times.length;

      categories[category] = {
        count: times.length,
        averageTime: Math.round(averageTime),
        totalTime
      };
    });

    // Calculate summary
    const allApiMetrics = filteredMetrics.filter(m => m.category === 'api');
    const totalRequests = allApiMetrics.length;
    const averageResponseTime = totalRequests > 0 
      ? Math.round(allApiMetrics.reduce((sum, m) => sum + m.value, 0) / totalRequests)
      : 0;
    const errorCount = allApiMetrics.filter(m => m.metadata?.isError).length;
    const errorRate = totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0;

    // Generate recommendations
    const recommendations = this.generateRecommendations(categories, endpoints);

    return {
      summary: {
        totalRequests,
        averageResponseTime,
        slowestEndpoint,
        fastestEndpoint,
        errorRate: Math.round(errorRate * 100) / 100
      },
      endpoints,
      categories,
      recommendations
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    categories: PerformanceReport['categories'],
    endpoints: PerformanceReport['endpoints']
  ): string[] {
    const recommendations: string[] = [];

    // Database performance
    if (categories.database?.averageTime > 1000) {
      recommendations.push('Database queries are slow (>1s average). Consider adding indexes or optimizing queries.');
    }

    // API performance
    if (categories.api?.averageTime > 2000) {
      recommendations.push('API responses are slow (>2s average). Consider caching or background processing.');
    }

    // File system performance
    if (categories.file_system?.averageTime > 5000) {
      recommendations.push('File system operations are slow (>5s average). Consider batch processing or async operations.');
    }

    // Thumbnail performance
    if (categories.thumbnail?.averageTime > 3000) {
      recommendations.push('Thumbnail generation is slow (>3s average). Consider pre-generating thumbnails.');
    }

    // High error rates
    Object.entries(endpoints).forEach(([endpoint, stats]) => {
      const errorRate = (stats.errors / stats.count) * 100;
      if (errorRate > 5) {
        recommendations.push(`High error rate (${errorRate.toFixed(1)}%) for ${endpoint}. Investigate error causes.`);
      }
    });

    if (recommendations.length === 0) {
      recommendations.push('Performance looks good! No immediate optimizations needed.');
    }

    return recommendations;
  }

  /**
   * Clean up old metrics
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    const initialCount = this.metrics.length;
    
    this.metrics = this.metrics.filter(m => m.timestamp > cutoffTime);
    
    const removedCount = initialCount - this.metrics.length;
    if (removedCount > 0) {
      console.log(`🧹 Cleaned up ${removedCount} old performance metrics`);
    }
  }

  /**
   * Get current metrics count
   */
  getMetricsCount(): number {
    return this.metrics.length;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
    this.activeTimers.clear();
    console.log('🧹 All performance metrics cleared');
  }
}
