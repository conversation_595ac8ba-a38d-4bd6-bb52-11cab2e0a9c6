import { HfInference } from '@huggingface/inference';

export interface VisionAnalysisResult {
  tags: Array<{
    name: string;
    confidence: number;
    reason: string;
  }>;
  analysis: string;
  success: boolean;
  error?: string;
}

export class HuggingFaceVisionService {
  private static client: HfInference | null = null;

  private static getClient(): HfInference {
    if (!this.client) {
      // Hugging Face Inference API can work without API key for public models (with rate limits)
      // For better performance, you can add HF_TOKEN to environment variables
      const token = process.env.HF_TOKEN || process.env.HUGGINGFACE_API_TOKEN;
      this.client = new HfInference(token);
    }
    return this.client;
  }

  /**
   * Analyze an image using Hugging Face models and extract tags
   */
  static async analyzeImageForTags(imageUrl: string): Promise<VisionAnalysisResult> {
    try {
      console.log('🤗 Starting Hugging Face Vision analysis for:', imageUrl);

      // Fetch the image
      const imageResponse = await fetch(imageUrl);
      if (!imageResponse.ok) {
        throw new Error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
      }
      
      const imageBlob = await imageResponse.blob();
      console.log('📷 Image fetched, size:', imageBlob.size, 'bytes');

      const client = this.getClient();
      const tags: Array<{ name: string; confidence: number; reason: string }> = [];

      try {
        // Method 1: Image Classification for general content
        console.log('🔍 Running image classification...');
        const classificationResults = await client.imageClassification({
          data: imageBlob,
          model: 'google/vit-base-patch16-224' // Free, fast vision transformer
        });

        // Convert classification results to tags
        for (const result of classificationResults.slice(0, 5)) {
          if (result.score > 0.1) { // Only include confident predictions
            const tagName = result.label.toLowerCase()
              .replace(/[^a-z0-9\s]/g, '')
              .replace(/\s+/g, '_')
              .trim();
            
            if (tagName && tagName.length > 2) {
              tags.push({
                name: tagName,
                confidence: result.score,
                reason: `Image classification: ${result.label}`
              });
            }
          }
        }
        console.log('✅ Classification results:', classificationResults.slice(0, 3));

      } catch (classError) {
        console.warn('⚠️ Image classification failed:', classError);
      }

      try {
        // Method 2: Object Detection for specific objects
        console.log('🎯 Running object detection...');
        const objectResults = await client.objectDetection({
          data: imageBlob,
          model: 'facebook/detr-resnet-50' // Free object detection model
        });

        // Convert object detection results to tags
        const detectedObjects = new Set<string>();
        for (const obj of objectResults.slice(0, 8)) {
          if (obj.score > 0.3) { // Only confident detections
            const objName = obj.label.toLowerCase()
              .replace(/[^a-z0-9\s]/g, '')
              .replace(/\s+/g, '_')
              .trim();
            
            if (objName && objName.length > 2 && !detectedObjects.has(objName)) {
              detectedObjects.add(objName);
              tags.push({
                name: objName,
                confidence: obj.score,
                reason: `Object detected: ${obj.label}`
              });
            }
          }
        }
        console.log('✅ Object detection results:', Array.from(detectedObjects));

      } catch (objError) {
        console.warn('⚠️ Object detection failed:', objError);
      }

      // If we got some results, enhance with contextual tags
      if (tags.length > 0) {
        this.addContextualTags(tags);
      }

      // Remove duplicates and sort by confidence
      const uniqueTags = this.deduplicateTags(tags)
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 8);

      if (uniqueTags.length === 0) {
        throw new Error('No tags could be extracted from the image');
      }

      const analysis = this.generateAnalysis(uniqueTags);

      console.log('✅ Final tags:', uniqueTags);

      return {
        tags: uniqueTags,
        analysis,
        success: true
      };

    } catch (error) {
      console.error('❌ Hugging Face Vision analysis failed:', error);
      
      return {
        tags: [],
        analysis: '',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Add contextual tags based on detected content
   */
  private static addContextualTags(tags: Array<{ name: string; confidence: number; reason: string }>) {
    const tagNames = tags.map(t => t.name);
    
    // Add environment tags
    if (tagNames.some(name => ['tree', 'grass', 'sky', 'mountain', 'water', 'flower'].includes(name))) {
      tags.push({
        name: 'outdoor',
        confidence: 0.8,
        reason: 'Natural elements detected'
      });
      tags.push({
        name: 'nature',
        confidence: 0.7,
        reason: 'Natural scene identified'
      });
    }

    if (tagNames.some(name => ['building', 'wall', 'window', 'door', 'furniture'].includes(name))) {
      tags.push({
        name: 'indoor',
        confidence: 0.8,
        reason: 'Indoor elements detected'
      });
    }

    // Add activity tags
    if (tagNames.some(name => ['person', 'people', 'man', 'woman', 'child'].includes(name))) {
      tags.push({
        name: 'people',
        confidence: 0.9,
        reason: 'Human subjects detected'
      });
    }

    if (tagNames.some(name => ['car', 'truck', 'bus', 'motorcycle', 'bicycle'].includes(name))) {
      tags.push({
        name: 'vehicle',
        confidence: 0.8,
        reason: 'Transportation detected'
      });
    }

    if (tagNames.some(name => ['food', 'plate', 'bowl', 'cup', 'restaurant'].includes(name))) {
      tags.push({
        name: 'food',
        confidence: 0.8,
        reason: 'Food-related content detected'
      });
    }
  }

  /**
   * Remove duplicate tags and merge similar ones
   */
  private static deduplicateTags(tags: Array<{ name: string; confidence: number; reason: string }>) {
    const tagMap = new Map<string, { name: string; confidence: number; reason: string }>();
    
    for (const tag of tags) {
      const existing = tagMap.get(tag.name);
      if (!existing || tag.confidence > existing.confidence) {
        tagMap.set(tag.name, tag);
      }
    }
    
    return Array.from(tagMap.values());
  }

  /**
   * Generate a descriptive analysis based on the tags
   */
  private static generateAnalysis(tags: Array<{ name: string; confidence: number; reason: string }>): string {
    if (tags.length === 0) return 'No content could be identified in the image.';
    
    const topTags = tags.slice(0, 3).map(t => t.name);
    const confidence = tags.reduce((sum, t) => sum + t.confidence, 0) / tags.length;
    
    let analysis = `Image contains: ${topTags.join(', ')}`;
    
    if (confidence > 0.7) {
      analysis += '. High confidence analysis.';
    } else if (confidence > 0.5) {
      analysis += '. Moderate confidence analysis.';
    } else {
      analysis += '. Lower confidence analysis.';
    }
    
    return analysis;
  }

  /**
   * Test the Hugging Face connection
   */
  static async testConnection(): Promise<boolean> {
    try {
      const client = this.getClient();
      
      // Test with a simple text classification to verify connection
      const result = await client.textClassification({
        model: 'cardiffnlp/twitter-roberta-base-sentiment-latest',
        inputs: 'Hello world'
      });

      console.log('🔗 Hugging Face connection test successful:', result);
      return true;
    } catch (error) {
      console.error('❌ Hugging Face connection test failed:', error);
      return false;
    }
  }
}
