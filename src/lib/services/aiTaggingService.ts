import { generateTextWithImages } from '@/lib/api/util';
import { LocalVisionService } from './localVisionService';
import { db, tags, assetTags } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

export interface TagSuggestion {
  name: string;
  confidence: number;
  reason: string;
}

export interface AutoTagResult {
  assetId: string;
  suggestedTags: TagSuggestion[];
  appliedTags: string[];
  analysis: string;
  success: boolean;
  error?: string;
}

export class AITaggingService {
  private static readonly AI_MODEL = 'azure-gpt-4o';
  private static readonly MIN_CONFIDENCE = 0.6; // Minimum confidence to auto-apply tags
  
  // Comprehensive tagging prompt optimized for extracting structured tags
  private static readonly TAGGING_PROMPT = `
Analyze this image and suggest relevant tags. Focus on:

1. OBJECTS & SUBJECTS: What's the main subject? (people, animals, objects, food, etc.)
2. SETTING & LOCATION: Indoor/outdoor, specific locations, environments
3. ACTIVITIES & ACTIONS: What's happening in the image?
4. STYLE & COMPOSITION: Photography style, artistic elements
5. MOOD & ATMOSPHERE: Emotional tone, lighting, weather
6. TECHNICAL ASPECTS: Portrait/landscape orientation, close-up/wide shot

Provide your response in this EXACT JSON format:
{
  "tags": [
    {
      "name": "tag_name_lowercase",
      "confidence": 0.95,
      "reason": "Brief explanation why this tag applies"
    }
  ],
  "analysis": "Brief overall description of the image"
}

Rules:
- Use lowercase, single words or short phrases (max 2-3 words)
- Confidence should be 0.0 to 1.0 (be realistic, not everything is 0.95+)
- Only suggest tags you're confident about
- Avoid overly specific or rare tags
- Focus on searchable, useful categories
- Maximum 10 tags per image
`;

  /**
   * Analyze an image and suggest tags using AI
   */
  static async analyzeImageForTags(imageUrl: string): Promise<TagSuggestion[]> {
    try {
      console.log('🤖 Starting AI analysis for tagging:', imageUrl);

      // Try Local Vision service first (free and always available)
      try {
        console.log('🔍 Attempting Local Vision analysis...');
        const localResult = await LocalVisionService.analyzeImageForTags(imageUrl);

        if (localResult.success && localResult.tags.length > 0) {
          console.log('✅ Local Vision analysis successful');
          return localResult.tags;
        } else {
          throw new Error(localResult.error || 'Local Vision returned no tags');
        }
      } catch (localError) {
        console.warn('⚠️ Local Vision failed, trying proxy service:', localError);

        // Fallback to the existing proxy service
        try {
          const result = await generateTextWithImages(
            this.TAGGING_PROMPT,
            [imageUrl],
            this.AI_MODEL
          );

          console.log('🤖 Proxy AI Response:', result.text);

          // Parse the JSON response
          const parsedResponse = this.parseAIResponse(result.text);

          if (!parsedResponse.tags || !Array.isArray(parsedResponse.tags)) {
            throw new Error('Invalid AI response format: missing tags array');
          }

          // Validate and clean up the suggestions
          const validatedTags = parsedResponse.tags
            .filter(tag => tag.name && typeof tag.confidence === 'number')
            .map(tag => ({
              name: tag.name.toLowerCase().trim(),
              confidence: Math.max(0, Math.min(1, tag.confidence)), // Clamp between 0-1
              reason: tag.reason || 'AI suggested tag'
            }))
            .slice(0, 10); // Limit to 10 tags

          console.log('✅ Proxy service validated tags:', validatedTags);
          return validatedTags;

        } catch (proxyError) {
          console.warn('⚠️ Proxy AI service also failed, using fallback tagging:', proxyError);

          // Final fallback to filename-based tagging
          return this.generateFallbackTags(imageUrl);
        }
      }

    } catch (error) {
      console.error('❌ Error in AI tagging analysis:', error);
      throw new Error(`AI tagging failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate fallback tags based on filename and basic analysis
   */
  private static generateFallbackTags(imageUrl: string): TagSuggestion[] {
    console.log('🔄 Generating fallback tags for:', imageUrl);

    const filename = imageUrl.split('/').pop()?.toLowerCase() || '';
    const fallbackTags: TagSuggestion[] = [];

    // Basic filename analysis
    if (filename.includes('portrait') || filename.includes('people') || filename.includes('person')) {
      fallbackTags.push({ name: 'people', confidence: 0.8, reason: 'Filename suggests people content' });
      fallbackTags.push({ name: 'portrait', confidence: 0.7, reason: 'Filename suggests portrait' });
    }

    if (filename.includes('landscape') || filename.includes('nature') || filename.includes('outdoor')) {
      fallbackTags.push({ name: 'landscape', confidence: 0.8, reason: 'Filename suggests landscape' });
      fallbackTags.push({ name: 'outdoor', confidence: 0.7, reason: 'Filename suggests outdoor scene' });
    }

    if (filename.includes('architecture') || filename.includes('building') || filename.includes('city')) {
      fallbackTags.push({ name: 'architecture', confidence: 0.8, reason: 'Filename suggests architecture' });
      fallbackTags.push({ name: 'urban', confidence: 0.7, reason: 'Filename suggests urban scene' });
    }

    if (filename.includes('food') || filename.includes('restaurant') || filename.includes('dining')) {
      fallbackTags.push({ name: 'food', confidence: 0.8, reason: 'Filename suggests food content' });
    }

    if (filename.includes('travel') || filename.includes('vacation') || filename.includes('trip')) {
      fallbackTags.push({ name: 'travel', confidence: 0.8, reason: 'Filename suggests travel content' });
    }

    // Add some generic tags based on file patterns
    if (filename.includes('jpg') || filename.includes('jpeg')) {
      fallbackTags.push({ name: 'photo', confidence: 0.9, reason: 'JPEG image file' });
    }

    // If no specific tags found, add some generic ones
    if (fallbackTags.length === 0) {
      fallbackTags.push(
        { name: 'image', confidence: 0.9, reason: 'Generic image content' },
        { name: 'photo', confidence: 0.8, reason: 'Photographic content' }
      );
    }

    // Add a timestamp-based tag if filename has date patterns
    const dateMatch = filename.match(/(\d{4}[-_]\d{2}[-_]\d{2})/);
    if (dateMatch) {
      const year = dateMatch[1].substring(0, 4);
      fallbackTags.push({ name: year, confidence: 0.6, reason: `Image from year ${year}` });
    }

    console.log('🔄 Generated fallback tags:', fallbackTags);
    return fallbackTags.slice(0, 6); // Limit to 6 fallback tags
  }

  /**
   * Parse AI response, handling potential JSON formatting issues
   */
  private static parseAIResponse(text: string): any {
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // If no JSON found, try parsing the whole text
      return JSON.parse(text);
    } catch (error) {
      console.error('Failed to parse AI response as JSON:', text);
      throw new Error('AI response is not valid JSON');
    }
  }

  /**
   * Auto-tag an asset with AI-generated tags
   */
  static async autoTagAsset(assetId: string, imageUrl: string): Promise<AutoTagResult> {
    try {
      console.log(`🏷️ Starting auto-tagging for asset: ${assetId}`);

      // Get AI tag suggestions
      const suggestions = await this.analyzeImageForTags(imageUrl);
      
      // Filter suggestions by confidence threshold
      const highConfidenceTags = suggestions.filter(
        tag => tag.confidence >= this.MIN_CONFIDENCE
      );

      console.log(`📊 Found ${suggestions.length} suggestions, ${highConfidenceTags.length} above confidence threshold`);

      // Create or get existing tags
      const appliedTagIds: string[] = [];
      
      for (const suggestion of highConfidenceTags) {
        try {
          // Try to find existing tag
          let existingTag = await db
            .select()
            .from(tags)
            .where(eq(tags.name, suggestion.name))
            .limit(1);

          let tagId: string;

          if (existingTag.length === 0) {
            // Create new tag
            const newTag = {
              id: uuidv4(),
              name: suggestion.name,
              color: this.generateTagColor(suggestion.name),
              description: `Auto-generated tag: ${suggestion.reason}`,
              isAutoGenerated: true,
            };

            const [createdTag] = await db
              .insert(tags)
              .values(newTag)
              .returning();

            tagId = createdTag.id;
            console.log(`✨ Created new tag: ${suggestion.name} (${tagId})`);
          } else {
            tagId = existingTag[0].id;
            console.log(`♻️ Using existing tag: ${suggestion.name} (${tagId})`);
          }

          // Add tag to asset (ignore if already exists)
          try {
            await db
              .insert(assetTags)
              .values({
                assetId,
                tagId,
                confidence: suggestion.confidence,
                isAutoGenerated: true,
              });

            appliedTagIds.push(tagId);
            console.log(`🏷️ Applied tag "${suggestion.name}" to asset with confidence ${suggestion.confidence}`);
          } catch (error) {
            // Ignore duplicate key errors
            if (!(error as Error).message?.includes('duplicate key')) {
              throw error;
            }
            console.log(`⚠️ Tag "${suggestion.name}" already exists for this asset`);
          }

        } catch (error) {
          console.error(`❌ Error processing tag "${suggestion.name}":`, error);
        }
      }

      return {
        assetId,
        suggestedTags: suggestions,
        appliedTags: appliedTagIds,
        analysis: suggestions.length > 0 ? 'AI analysis completed successfully' : 'No high-confidence tags found',
        success: true,
      };

    } catch (error) {
      console.error('❌ Auto-tagging failed:', error);
      return {
        assetId,
        suggestedTags: [],
        appliedTags: [],
        analysis: '',
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Generate a color for a tag based on its name
   */
  private static generateTagColor(tagName: string): string {
    const colors = [
      '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
      '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9',
      '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
      '#ec4899', '#f43f5e'
    ];
    
    // Simple hash function to consistently assign colors
    let hash = 0;
    for (let i = 0; i < tagName.length; i++) {
      hash = tagName.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }

  /**
   * Batch auto-tag multiple assets
   */
  static async batchAutoTag(assets: Array<{ id: string; imageUrl: string }>): Promise<AutoTagResult[]> {
    const results: AutoTagResult[] = [];
    
    console.log(`🚀 Starting batch auto-tagging for ${assets.length} assets`);
    
    for (const asset of assets) {
      try {
        const result = await this.autoTagAsset(asset.id, asset.imageUrl);
        results.push(result);
        
        // Add a small delay to avoid overwhelming the AI service
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`❌ Batch tagging failed for asset ${asset.id}:`, error);
        results.push({
          assetId: asset.id,
          suggestedTags: [],
          appliedTags: [],
          analysis: '',
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
    
    console.log(`✅ Batch auto-tagging completed: ${results.filter(r => r.success).length}/${results.length} successful`);
    return results;
  }
}
