import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "@/lib/db/schema";

// Define Better-Auth schema tables
const betterAuthSchema = {
  user: {
    id: "text",
    name: "text",
    email: "text",
    emailVerified: "boolean",
    image: "text",
    createdAt: "timestamp",
    updatedAt: "timestamp",
    role: "text"
  },
  session: {
    id: "text",
    expiresAt: "timestamp",
    token: "text",
    createdAt: "timestamp",
    updatedAt: "timestamp",
    ipAddress: "text",
    userAgent: "text",
    userId: "text"
  },
  account: {
    id: "text",
    accountId: "text",
    providerId: "text",
    userId: "text",
    accessToken: "text",
    refreshToken: "text",
    idToken: "text",
    accessTokenExpiresAt: "timestamp",
    refreshTokenExpiresAt: "timestamp",
    scope: "text",
    password: "text",
    createdAt: "timestamp",
    updatedAt: "timestamp"
  },
  verification: {
    id: "text",
    identifier: "text",
    value: "text",
    expiresAt: "timestamp",
    createdAt: "timestamp",
    updatedAt: "timestamp"
  }
};

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
    schema: betterAuthSchema,
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 60 * 24 * 7, // 7 days
    },
  },
  user: {
    additionalFields: {
      role: {
        type: "string",
        required: false,
        defaultValue: "editor",
      },
    },
  },
  // Network compatibility settings
  trustedOrigins: [
    "http://localhost:3000",
    "http://************:3000", // Network IP from server logs
    process.env.NEXT_PUBLIC_APP_URL,
  ].filter(Boolean),
  cookies: {
    sameSite: "lax", // Allow cross-site requests within same network
    secure: process.env.NODE_ENV === "production",
    httpOnly: true,
  },
});

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.User;
