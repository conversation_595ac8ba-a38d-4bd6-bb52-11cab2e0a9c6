import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import crypto from 'crypto';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Optimized Thumbnail Service with:
 * 1. Pre-generation of thumbnails
 * 2. Multiple thumbnail sizes
 * 3. Efficient caching
 * 4. Background processing
 * 5. WebP format for better compression
 */
export class ThumbnailService {
  private static instance: ThumbnailService;
  private thumbnailDir = path.join(process.cwd(), 'storage', 'thumbnails');
  private basePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
  
  // Thumbnail configurations
  private thumbnailSizes = {
    small: { width: 150, height: 150, quality: 80 },
    medium: { width: 300, height: 300, quality: 85 },
    large: { width: 600, height: 600, quality: 90 }
  };

  private processingQueue: Set<string> = new Set();
  private cache = new Map<string, string>();
  
  private constructor() {
    this.ensureThumbnailDirectory();
    console.log('🖼️ ThumbnailService initialized');
    console.log(`📁 Thumbnail directory: ${this.thumbnailDir}`);
  }

  static getInstance(): ThumbnailService {
    if (!ThumbnailService.instance) {
      ThumbnailService.instance = new ThumbnailService();
    }
    return ThumbnailService.instance;
  }

  /**
   * Ensure thumbnail directory exists
   */
  private ensureThumbnailDirectory(): void {
    try {
      if (!fs.existsSync(this.thumbnailDir)) {
        fs.mkdirSync(this.thumbnailDir, { recursive: true });
        console.log(`📁 Created thumbnail directory: ${this.thumbnailDir}`);
      }
      
      // Create size-specific subdirectories
      Object.keys(this.thumbnailSizes).forEach(size => {
        const sizeDir = path.join(this.thumbnailDir, size);
        if (!fs.existsSync(sizeDir)) {
          fs.mkdirSync(sizeDir, { recursive: true });
        }
      });
    } catch (error) {
      console.error('❌ Error creating thumbnail directory:', error);
    }
  }

  /**
   * Check if file is a video
   */
  private isVideoFile(filePath: string): boolean {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.wmv', '.flv', '.m4v'];
    const ext = path.extname(filePath).toLowerCase();
    return videoExtensions.includes(ext);
  }

  /**
   * Generate video thumbnail using ffmpeg
   */
  private async generateVideoThumbnail(inputPath: string, outputPath: string, size: string): Promise<void> {
    const [width, height] = size.split('x').map(Number);

    // Use ffmpeg to extract a frame from the video at 1 second
    const ffmpegCommand = `ffmpeg -i "${inputPath}" -ss 00:00:01.000 -vframes 1 -vf "scale=${width}:${height}:force_original_aspect_ratio=decrease,pad=${width}:${height}:(ow-iw)/2:(oh-ih)/2:black" -y "${outputPath}"`;

    try {
      await execAsync(ffmpegCommand);
      console.log(`✅ Video thumbnail generated: ${outputPath}`);
    } catch (error) {
      console.error(`❌ Failed to generate video thumbnail for ${inputPath}:`, error);
      throw new Error(`Failed to generate video thumbnail: ${error}`);
    }
  }

  /**
   * Generate thumbnail hash for caching
   */
  private generateThumbnailHash(filePath: string, size: string): string {
    return crypto
      .createHash('md5')
      .update(`${filePath}-${size}`)
      .digest('hex');
  }

  /**
   * Get thumbnail path for a given file and size
   */
  private getThumbnailPath(filePath: string, size: string = 'medium'): string {
    const hash = this.generateThumbnailHash(filePath, size);
    return path.join(this.thumbnailDir, size, `${hash}.webp`);
  }

  /**
   * Check if thumbnail exists and is newer than source file
   */
  private async isThumbnailValid(sourcePath: string, thumbnailPath: string): Promise<boolean> {
    try {
      const [sourceStats, thumbnailStats] = await Promise.all([
        fs.promises.stat(sourcePath),
        fs.promises.stat(thumbnailPath)
      ]);
      
      return thumbnailStats.mtime >= sourceStats.mtime;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a single thumbnail (supports both images and videos)
   */
  private async generateThumbnail(
    sourcePath: string,
    thumbnailPath: string,
    size: keyof typeof this.thumbnailSizes
  ): Promise<void> {
    const config = this.thumbnailSizes[size];

    // Ensure thumbnail directory exists
    const thumbnailDir = path.dirname(thumbnailPath);
    if (!fs.existsSync(thumbnailDir)) {
      fs.mkdirSync(thumbnailDir, { recursive: true });
    }

    try {
      if (this.isVideoFile(sourcePath)) {
        // Generate video thumbnail using ffmpeg
        const sizeString = `${config.width}x${config.height}`;
        await this.generateVideoThumbnail(sourcePath, thumbnailPath, sizeString);
      } else {
        // Generate image thumbnail using sharp
        await sharp(sourcePath)
          .resize(config.width, config.height, {
            fit: 'cover',
            position: 'center'
          })
          .webp({ quality: config.quality })
          .toFile(thumbnailPath);
      }

      console.log(`✅ Generated ${size} thumbnail: ${path.basename(thumbnailPath)}`);
    } catch (error) {
      console.error(`❌ Error generating thumbnail for ${sourcePath}:`, error);
      throw error;
    }
  }

  /**
   * Get or generate thumbnail
   */
  async getThumbnail(
    relativePath: string, 
    size: keyof typeof this.thumbnailSizes = 'medium'
  ): Promise<string | null> {
    const sourcePath = path.join(this.basePath, relativePath);
    const thumbnailPath = this.getThumbnailPath(relativePath, size);
    const cacheKey = `${relativePath}-${size}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }
    
    try {
      // Check if source file exists
      if (!fs.existsSync(sourcePath)) {
        console.warn(`⚠️ Source file not found: ${sourcePath}`);
        return null;
      }
      
      // Check if valid thumbnail exists
      if (fs.existsSync(thumbnailPath)) {
        const isValid = await this.isThumbnailValid(sourcePath, thumbnailPath);
        if (isValid) {
          this.cache.set(cacheKey, thumbnailPath);
          return thumbnailPath;
        }
      }
      
      // Generate thumbnail if not in processing queue
      if (!this.processingQueue.has(cacheKey)) {
        this.processingQueue.add(cacheKey);
        
        try {
          await this.generateThumbnail(sourcePath, thumbnailPath, size);
          this.cache.set(cacheKey, thumbnailPath);
          return thumbnailPath;
        } finally {
          this.processingQueue.delete(cacheKey);
        }
      }
      
      return null;
    } catch (error) {
      console.error(`❌ Error getting thumbnail for ${relativePath}:`, error);
      this.processingQueue.delete(cacheKey);
      return null;
    }
  }

  /**
   * Batch generate thumbnails for multiple files
   */
  async batchGenerateThumbnails(
    filePaths: string[], 
    sizes: (keyof typeof this.thumbnailSizes)[] = ['small', 'medium']
  ): Promise<{ generated: number; errors: number }> {
    let generated = 0;
    let errors = 0;
    
    console.log(`🖼️ Batch generating thumbnails for ${filePaths.length} files`);
    
    // Process in parallel batches
    const batchSize = 10;
    for (let i = 0; i < filePaths.length; i += batchSize) {
      const batch = filePaths.slice(i, i + batchSize);
      
      const promises = batch.map(async (filePath) => {
        try {
          for (const size of sizes) {
            await this.getThumbnail(filePath, size);
          }
          generated++;
        } catch (error) {
          console.error(`❌ Error generating thumbnails for ${filePath}:`, error);
          errors++;
        }
      });
      
      await Promise.all(promises);
      
      // Progress logging
      if ((i + batch.length) % 50 === 0) {
        const progress = ((i + batch.length) / filePaths.length * 100).toFixed(1);
        console.log(`🖼️ Thumbnail progress: ${progress}% (${generated} generated, ${errors} errors)`);
      }
    }
    
    console.log(`✅ Batch thumbnail generation completed: ${generated} generated, ${errors} errors`);
    return { generated, errors };
  }

  /**
   * Clean up old thumbnails
   */
  async cleanupOldThumbnails(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<number> {
    let cleaned = 0;
    const cutoffTime = Date.now() - maxAge;
    
    try {
      for (const size of Object.keys(this.thumbnailSizes)) {
        const sizeDir = path.join(this.thumbnailDir, size);
        
        if (fs.existsSync(sizeDir)) {
          const files = await fs.promises.readdir(sizeDir);
          
          for (const file of files) {
            const filePath = path.join(sizeDir, file);
            const stats = await fs.promises.stat(filePath);
            
            if (stats.mtime.getTime() < cutoffTime) {
              await fs.promises.unlink(filePath);
              cleaned++;
            }
          }
        }
      }
      
      console.log(`🧹 Cleaned up ${cleaned} old thumbnails`);
    } catch (error) {
      console.error('❌ Error cleaning up thumbnails:', error);
    }
    
    return cleaned;
  }

  /**
   * Get thumbnail statistics
   */
  async getStats(): Promise<{
    totalThumbnails: number;
    sizeBreakdown: Record<string, number>;
    cacheSize: number;
    processingQueueSize: number;
  }> {
    const stats = {
      totalThumbnails: 0,
      sizeBreakdown: {} as Record<string, number>,
      cacheSize: this.cache.size,
      processingQueueSize: this.processingQueue.size
    };
    
    try {
      for (const size of Object.keys(this.thumbnailSizes)) {
        const sizeDir = path.join(this.thumbnailDir, size);
        
        if (fs.existsSync(sizeDir)) {
          const files = await fs.promises.readdir(sizeDir);
          stats.sizeBreakdown[size] = files.length;
          stats.totalThumbnails += files.length;
        } else {
          stats.sizeBreakdown[size] = 0;
        }
      }
    } catch (error) {
      console.error('❌ Error getting thumbnail stats:', error);
    }
    
    return stats;
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 Thumbnail cache cleared');
  }
}
