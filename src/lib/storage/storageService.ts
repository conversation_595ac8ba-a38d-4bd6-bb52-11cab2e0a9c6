import { v4 as uuidv4 } from 'uuid';
import exifr from 'exifr';
import { IndexingService } from './indexingService';

export interface Asset {
  id: string;
  filename: string;
  fileUrl: string;
  thumbnailUrl: string;
  type: string;
  size: number;
  lastModified: Date;
  metadata: any;
}

export interface AssetsResponse {
  assets: Asset[];
  total: number;
  hasMore: boolean;
}

export class StorageService {
  private cachedAssets: Asset[] = [];
  private isScanning: boolean = false;
  private scanProgress: { current: number; total: number } = { current: 0, total: 0 };
  private scanCallbacks: ((progress: { current: number; total: number }) => void)[] = [];

  async getInitialAssets(limit: number = 50, offset: number = 0): Promise<AssetsResponse> {
    try {
      const response = await fetch(`/api/storage/scan?limit=${limit}&offset=${offset}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to scan storage');
      }
      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to scan storage');
      }
      this.cachedAssets = data.assets;
      return {
        assets: data.assets,
        total: data.total,
        hasMore: data.hasMore
      };
    } catch (error) {
      console.error('Error getting initial assets:', error);
      throw error; // Re-throw to let the UI handle the error
    }
  }

  onScanProgress(callback: (progress: { current: number; total: number }) => void) {
    this.scanCallbacks.push(callback);
    return () => {
      this.scanCallbacks = this.scanCallbacks.filter(cb => cb !== callback);
    };
  }

  private notifyScanProgress() {
    this.scanCallbacks.forEach(callback => callback(this.scanProgress));
  }

  async scanDirectory(): Promise<Asset[]> {
    try {
      const indexingService = IndexingService.getInstance();
      // Don't trigger indexing here - just return existing assets for fast response
      // File watcher will handle new files automatically
      console.log('📁 Scanning directory - returning cached database results for fast response');
      return await indexingService.getAssets(100, 0);
    } catch (error) {
      console.error('Error scanning directory:', error);
      return [];
    }
  }

  startWatching(callback: (event: string, path: string) => void) {
    const indexingService = IndexingService.getInstance();
    // The IndexingService already starts watching automatically, 
    // but we can provide a callback mechanism if needed
    console.log('Storage service watching started - IndexingService handles file monitoring');
    
    // Since chokidar events are already handled in IndexingService,
    // we just inform the caller that watching is active
    callback('watching', 'File watching is active via IndexingService');
  }
}

const storageService = new StorageService();
export { storageService }; 