/**
 * Utility to extract dates from folder structure patterns
 * Handles various folder naming conventions used across years
 */

export interface ParsedFolderDate {
  year: number;
  month: number;
  day?: number;
  confidence: 'high' | 'medium' | 'low';
  source: string;
}

/**
 * Extract date information from folder path
 * @param folderPath - Full path to the folder containing the asset
 * @returns ParsedFolderDate or null if no date can be extracted
 */
export function extractDateFromFolderPath(folderPath: string): ParsedFolderDate | null {
  // Remove base path and normalize
  const relativePath = folderPath.replace('/mnt/nas/photos/MASTER-PHOTOS/', '');
  const pathParts = relativePath.split('/').filter(part => part.length > 0);
  
  if (pathParts.length === 0) return null;

  // Extract year from first part
  const yearMatch = pathParts[0].match(/^(\d{4})$/);
  if (!yearMatch) return null;
  
  const year = parseInt(yearMatch[1]);
  
  // If only year folder, return year-based date
  if (pathParts.length === 1) {
    return {
      year,
      month: 12, // Default to December for year-only
      day: 31,
      confidence: 'low',
      source: 'year-only'
    };
  }

  const monthFolder = pathParts[1];
  
  // Parse different month folder patterns
  const monthDate = parseMonthFolder(monthFolder, year);
  if (!monthDate) {
    return {
      year,
      month: 12,
      day: 31,
      confidence: 'low',
      source: 'year-fallback'
    };
  }

  // Check for daily folder (third level)
  if (pathParts.length >= 3) {
    const dayDate = parseDayFolder(pathParts[2], year, monthDate.month);
    if (dayDate) {
      return {
        year,
        month: monthDate.month,
        day: dayDate.day,
        confidence: 'high',
        source: 'daily-folder'
      };
    }
  }

  return monthDate;
}

/**
 * Parse month folder patterns
 */
function parseMonthFolder(folderName: string, year: number): ParsedFolderDate | null {
  // Pattern: "01-JAN2022", "02-FEB2023", etc.
  let match = folderName.match(/^(\d{1,2})-([A-Z]{3})(\d{4})/);
  if (match) {
    const month = parseInt(match[1]);
    const monthName = match[2];
    const folderYear = parseInt(match[3]);
    
    if (folderYear === year && month >= 1 && month <= 12) {
      return {
        year,
        month,
        day: getLastDayOfMonth(year, month),
        confidence: 'high',
        source: 'month-folder-numbered'
      };
    }
  }

  // Pattern: "01JAN2025", "06JUN2025", etc.
  match = folderName.match(/^(\d{1,2})([A-Z]{3})(\d{4})/);
  if (match) {
    const month = parseInt(match[1]);
    const monthName = match[2];
    const folderYear = parseInt(match[3]);
    
    if (folderYear === year && month >= 1 && month <= 12) {
      return {
        year,
        month,
        day: getLastDayOfMonth(year, month),
        confidence: 'high',
        source: 'month-folder-compact'
      };
    }
  }

  // Pattern: "001-JAN2020", "002-FEB2021", etc.
  match = folderName.match(/^(\d{3})-([A-Z]{3})(\d{4})/);
  if (match) {
    const month = parseInt(match[1]);
    const monthName = match[2];
    const folderYear = parseInt(match[3]);
    
    if (folderYear === year && month >= 1 && month <= 12) {
      return {
        year,
        month,
        day: getLastDayOfMonth(year, month),
        confidence: 'high',
        source: 'month-folder-padded'
      };
    }
  }

  // Pattern: "01-January 2018 folder", etc.
  match = folderName.match(/^(\d{1,2})-([A-Za-z]+)\s+(\d{4})/);
  if (match) {
    const month = parseInt(match[1]);
    const folderYear = parseInt(match[3]);
    
    if (folderYear === year && month >= 1 && month <= 12) {
      return {
        year,
        month,
        day: getLastDayOfMonth(year, month),
        confidence: 'high',
        source: 'month-folder-full-name'
      };
    }
  }

  // Pattern: just numbers "07", "08", etc.
  match = folderName.match(/^(\d{1,2})$/);
  if (match) {
    const month = parseInt(match[1]);
    if (month >= 1 && month <= 12) {
      return {
        year,
        month,
        day: getLastDayOfMonth(year, month),
        confidence: 'medium',
        source: 'month-number-only'
      };
    }
  }

  return null;
}

/**
 * Parse daily folder patterns
 */
function parseDayFolder(folderName: string, year: number, month: number): { day: number } | null {
  // Pattern: "01-01-2025 folder", "02-01-2025 folder", etc.
  const match = folderName.match(/^(\d{1,2})-(\d{1,2})-(\d{4})/);
  if (match) {
    const day = parseInt(match[1]);
    const folderMonth = parseInt(match[2]);
    const folderYear = parseInt(match[3]);
    
    if (folderYear === year && folderMonth === month && day >= 1 && day <= 31) {
      return { day };
    }
  }

  return null;
}

/**
 * Get the last day of a given month/year
 */
function getLastDayOfMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

/**
 * Convert ParsedFolderDate to JavaScript Date object
 */
export function folderDateToDate(folderDate: ParsedFolderDate): Date {
  return new Date(folderDate.year, folderDate.month - 1, folderDate.day || 1);
}

/**
 * Test the parser with sample paths
 */
export function testFolderDateParser() {
  const testPaths = [
    '/mnt/nas/photos/MASTER-PHOTOS/2025/01JAN2025 folder/01-01-2025 folder',
    '/mnt/nas/photos/MASTER-PHOTOS/2025/06JUN2025folder',
    '/mnt/nas/photos/MASTER-PHOTOS/2024/01-JAN2024 folder/01-01-2024 folder',
    '/mnt/nas/photos/MASTER-PHOTOS/2024/06JUN2024 folder',
    '/mnt/nas/photos/MASTER-PHOTOS/2023/01-JAN2023/05-01-2023 folder',
    '/mnt/nas/photos/MASTER-PHOTOS/2023/02-FEB2023 folder',
    '/mnt/nas/photos/MASTER-PHOTOS/2022/12-DEC2022',
    '/mnt/nas/photos/MASTER-PHOTOS/2021/001-JAN2021/01JAN2021',
    '/mnt/nas/photos/MASTER-PHOTOS/2021/002-FEB2021',
    '/mnt/nas/photos/MASTER-PHOTOS/2020/012-DEC2020',
    '/mnt/nas/photos/MASTER-PHOTOS/2019/07',
    '/mnt/nas/photos/MASTER-PHOTOS/2018/01-January 2018 folder'
  ];

  console.log('🧪 Testing folder date parser:');
  testPaths.forEach(path => {
    const result = extractDateFromFolderPath(path);
    if (result) {
      const date = folderDateToDate(result);
      console.log(`📁 ${path}`);
      console.log(`   → ${date.toISOString()} (${result.confidence} confidence, ${result.source})`);
    } else {
      console.log(`📁 ${path} → No date extracted`);
    }
  });
}
