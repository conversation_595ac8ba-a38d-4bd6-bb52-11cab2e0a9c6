/**
 * Search query parser for advanced search functionality
 * Supports AND/OR operators and multiple search terms
 */

export interface SearchTerm {
  word: string;
  operator?: 'AND' | 'OR';
  exclude?: boolean; // true if this term should be excluded (prefixed with -)
  isPhrase?: boolean; // true if this is a quoted phrase that should be searched as exact match
}

export interface ParsedSearchQuery {
  terms: SearchTerm[];
  hasOperators: boolean;
  hasExclusions: boolean;
  hasPhrases: boolean;
  originalQuery: string;
}

/**
 * Parse a search query string into structured terms with operators, exclusions, and phrases
 * Examples:
 * - "king spain" -> OR operation (default)
 * - "king AND spain" -> AND operation
 * - "king OR queen" -> OR operation
 * - "king AND spain OR france" -> Mixed operations
 * - "king -spain" -> Include king, exclude spain
 * - "travel AND nature -indoor" -> Include travel AND nature, exclude indoor
 * - '"King Salaman"' -> Exact phrase search for "King Salaman"
 * - '"Real Madrid" AND football -barcelona' -> Phrase "Real Madrid" AND football, exclude barcelona
 */
export function parseSearchQuery(query: string): ParsedSearchQuery {
  if (!query || !query.trim()) {
    return {
      terms: [],
      hasOperators: false,
      hasExclusions: false,
      hasPhrases: false,
      originalQuery: query
    };
  }

  const normalizedQuery = query.trim();
  const terms: SearchTerm[] = [];
  let hasOperators = false;
  let hasExclusions = false;
  let hasPhrases = false;

  // Helper function to process a word/phrase and check for exclusion prefix and quotes
  const processWord = (word: string): { word: string; exclude: boolean; isPhrase: boolean } => {
    let isPhrase = false;
    let processedWord = word;
    let exclude = false;

    // Check for exclusion prefix first
    if (processedWord.startsWith('-') && processedWord.length > 1) {
      hasExclusions = true;
      exclude = true;
      processedWord = processedWord.substring(1);
    }

    // Check for quoted phrases
    if (processedWord.startsWith('"') && processedWord.endsWith('"') && processedWord.length > 2) {
      hasPhrases = true;
      isPhrase = true;
      processedWord = processedWord.substring(1, processedWord.length - 1);
    }

    return {
      word: processedWord.toLowerCase(),
      exclude,
      isPhrase
    };
  };

  // First, extract quoted phrases and replace them with placeholders
  const phrases: string[] = [];
  let queryWithPlaceholders = normalizedQuery;

  // Find all quoted phrases (including those with exclusion prefix)
  const quoteRegex = /(-?"[^"]+"|"[^"]+")/g;
  let match;
  let placeholderIndex = 0;

  while ((match = quoteRegex.exec(normalizedQuery)) !== null) {
    const placeholder = `__PHRASE_${placeholderIndex}__`;
    phrases.push(match[1]);
    queryWithPlaceholders = queryWithPlaceholders.replace(match[1], placeholder);
    placeholderIndex++;
  }

  // Split by AND/OR operators while preserving the operators
  const parts = queryWithPlaceholders.split(/\s+(AND|OR)\s+/i);

  if (parts.length === 1) {
    // No explicit operators, split by spaces and treat as OR
    const words = queryWithPlaceholders.split(/\s+/).filter(word => word.length > 0);
    words.forEach((word, index) => {
      // Check if this is a phrase placeholder
      if (word.startsWith('__PHRASE_') && word.endsWith('__')) {
        const phraseIndex = parseInt(word.replace('__PHRASE_', '').replace('__', ''));
        const originalPhrase = phrases[phraseIndex];
        const { word: processedWord, exclude, isPhrase } = processWord(originalPhrase);
        terms.push({
          word: processedWord,
          operator: index === 0 ? undefined : 'OR',
          exclude,
          isPhrase
        });
      } else {
        const { word: processedWord, exclude, isPhrase } = processWord(word);
        terms.push({
          word: processedWord,
          operator: index === 0 ? undefined : 'OR',
          exclude,
          isPhrase
        });
      }
    });
  } else {
    // Has explicit operators
    hasOperators = true;

    for (let i = 0; i < parts.length; i++) {
      if (i % 2 === 0) {
        // This is a search term (not an operator)
        const words = parts[i].trim().split(/\s+/).filter(word => word.length > 0);
        words.forEach((word, wordIndex) => {
          const isFirstTerm = terms.length === 0 && wordIndex === 0;

          // Check if this is a phrase placeholder
          if (word.startsWith('__PHRASE_') && word.endsWith('__')) {
            const phraseIndex = parseInt(word.replace('__PHRASE_', '').replace('__', ''));
            const originalPhrase = phrases[phraseIndex];
            const { word: processedWord, exclude, isPhrase } = processWord(originalPhrase);
            terms.push({
              word: processedWord,
              operator: isFirstTerm ? undefined : 'OR',
              exclude,
              isPhrase
            });
          } else {
            const { word: processedWord, exclude, isPhrase } = processWord(word);
            terms.push({
              word: processedWord,
              operator: isFirstTerm ? undefined : 'OR', // Multiple words within a term are OR'd
              exclude,
              isPhrase
            });
          }
        });
      } else {
        // This is an operator (AND/OR)
        const operator = parts[i].toUpperCase() as 'AND' | 'OR';
        if (terms.length > 0) {
          // Apply the operator to the next term
          const nextTermIndex = i + 1;
          if (nextTermIndex < parts.length) {
            const nextWords = parts[nextTermIndex].trim().split(/\s+/).filter(word => word.length > 0);
            nextWords.forEach((word, wordIndex) => {
              // Check if this is a phrase placeholder
              if (word.startsWith('__PHRASE_') && word.endsWith('__')) {
                const phraseIndex = parseInt(word.replace('__PHRASE_', '').replace('__', ''));
                const originalPhrase = phrases[phraseIndex];
                const { word: processedWord, exclude, isPhrase } = processWord(originalPhrase);
                terms.push({
                  word: processedWord,
                  operator: wordIndex === 0 ? operator : 'OR',
                  exclude,
                  isPhrase
                });
              } else {
                const { word: processedWord, exclude, isPhrase } = processWord(word);
                terms.push({
                  word: processedWord,
                  operator: wordIndex === 0 ? operator : 'OR',
                  exclude,
                  isPhrase
                });
              }
            });
            i++; // Skip the next part since we processed it
          }
        }
      }
    }
  }

  return {
    terms,
    hasOperators,
    hasExclusions,
    hasPhrases,
    originalQuery: normalizedQuery
  };
}

/**
 * Build search terms for database query
 * Returns an array of search patterns to be used with Drizzle ORM
 */
export function buildSearchTerms(parsedQuery: ParsedSearchQuery): string[] {
  return parsedQuery.terms.map(term => `%${term.word}%`);
}

/**
 * Determine if the search should use AND logic between terms
 * Returns true if any term has AND operator
 */
export function shouldUseAndLogic(parsedQuery: ParsedSearchQuery): boolean {
  return parsedQuery.terms.some(term => term.operator === 'AND');
}
