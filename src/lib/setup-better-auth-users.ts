import { db } from "@/lib/db/schema";
import bcrypt from "bcryptjs";

// Better-Auth user table structure
const betterAuthUsers = {
  id: "text",
  name: "text", 
  email: "text",
  emailVerified: "boolean",
  image: "text",
  createdAt: "timestamp",
  updatedAt: "timestamp",
  role: "text"
};

const betterAuthAccounts = {
  id: "text",
  accountId: "text",
  providerId: "text", 
  userId: "text",
  accessToken: "text",
  refreshToken: "text",
  idToken: "text",
  accessTokenExpiresAt: "timestamp",
  refreshTokenExpiresAt: "timestamp",
  scope: "text",
  password: "text",
  createdAt: "timestamp",
  updatedAt: "timestamp"
};

export async function setupBetterAuthUsers() {
  try {
    console.log('🔧 Setting up Better-Auth users...');

    // Check if tables exist, if not create them
    await db.execute(`
      CREATE TABLE IF NOT EXISTS "user" (
        "id" text PRIMARY KEY,
        "name" text,
        "email" text UNIQUE NOT NULL,
        "emailVerified" boolean DEFAULT false,
        "image" text,
        "createdAt" timestamp DEFAULT NOW(),
        "updatedAt" timestamp DEFAULT NOW(),
        "role" text DEFAULT 'editor'
      );
    `);

    await db.execute(`
      CREATE TABLE IF NOT EXISTS "account" (
        "id" text PRIMARY KEY,
        "accountId" text NOT NULL,
        "providerId" text NOT NULL,
        "userId" text NOT NULL,
        "accessToken" text,
        "refreshToken" text,
        "idToken" text,
        "accessTokenExpiresAt" timestamp,
        "refreshTokenExpiresAt" timestamp,
        "scope" text,
        "password" text,
        "createdAt" timestamp DEFAULT NOW(),
        "updatedAt" timestamp DEFAULT NOW(),
        FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE
      );
    `);

    await db.execute(`
      CREATE TABLE IF NOT EXISTS "session" (
        "id" text PRIMARY KEY,
        "expiresAt" timestamp NOT NULL,
        "token" text UNIQUE NOT NULL,
        "createdAt" timestamp DEFAULT NOW(),
        "updatedAt" timestamp DEFAULT NOW(),
        "ipAddress" text,
        "userAgent" text,
        "userId" text NOT NULL,
        FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE
      );
    `);

    await db.execute(`
      CREATE TABLE IF NOT EXISTS "verification" (
        "id" text PRIMARY KEY,
        "identifier" text NOT NULL,
        "value" text NOT NULL,
        "expiresAt" timestamp NOT NULL,
        "createdAt" timestamp DEFAULT NOW(),
        "updatedAt" timestamp DEFAULT NOW()
      );
    `);

    // Hash passwords
    const adminPasswordHash = await bcrypt.hash("Asharq!@2025", 12);
    const editorPasswordHash = await bcrypt.hash("Dam2025", 12);

    const now = new Date().toISOString();

    // Insert admin user
    const adminUserId = "admin-user-id-" + Date.now();
    const adminAccountId = "admin-account-id-" + Date.now();

    await db.execute(`
      INSERT INTO "user" (id, name, email, "emailVerified", role, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (email) DO UPDATE SET
        name = EXCLUDED.name,
        role = EXCLUDED.role,
        "updatedAt" = EXCLUDED."updatedAt"
    `, [adminUserId, "Admin User", "<EMAIL>", true, "admin", now, now]);

    await db.execute(`
      INSERT INTO "account" (id, "accountId", "providerId", "userId", password, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (id) DO UPDATE SET
        password = EXCLUDED.password,
        "updatedAt" = EXCLUDED."updatedAt"
    `, [adminAccountId, "<EMAIL>", "credential", adminUserId, adminPasswordHash, now, now]);

    // Insert editor user
    const editorUserId = "editor-user-id-" + Date.now();
    const editorAccountId = "editor-account-id-" + Date.now();

    await db.execute(`
      INSERT INTO "user" (id, name, email, "emailVerified", role, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (email) DO UPDATE SET
        name = EXCLUDED.name,
        role = EXCLUDED.role,
        "updatedAt" = EXCLUDED."updatedAt"
    `, [editorUserId, "Editor User", "<EMAIL>", true, "editor", now, now]);

    await db.execute(`
      INSERT INTO "account" (id, "accountId", "providerId", "userId", password, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (id) DO UPDATE SET
        password = EXCLUDED.password,
        "updatedAt" = EXCLUDED."updatedAt"
    `, [editorAccountId, "<EMAIL>", "credential", editorUserId, editorPasswordHash, now, now]);

    console.log('✅ Better-Auth users setup complete');
    console.log('👤 Admin: <EMAIL> / Asharq!@2025');
    console.log('👤 Editor: <EMAIL> / Dam2025');

  } catch (error) {
    console.error('❌ Error setting up Better-Auth users:', error);
    throw error;
  }
}
