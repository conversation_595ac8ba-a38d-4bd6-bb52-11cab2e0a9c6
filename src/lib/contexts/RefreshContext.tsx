'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface RefreshContextType {
  isRefreshing: boolean;
  setIsRefreshing: (refreshing: boolean) => void;
  refreshFunction: (() => void) | null;
  setRefreshFunction: (fn: (() => void) | null) => void;
  triggerRefresh: () => void;
}

const RefreshContext = createContext<RefreshContextType | undefined>(undefined);

export function RefreshProvider({ children }: { children: ReactNode }) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshFunction, setRefreshFunction] = useState<(() => void) | null>(null);

  const triggerRefresh = () => {
    if (refreshFunction) {
      refreshFunction();
    }
  };

  return (
    <RefreshContext.Provider
      value={{
        isRefreshing,
        setIsRefreshing,
        refreshFunction,
        setRefreshFunction,
        triggerRefresh,
      }}
    >
      {children}
    </RefreshContext.Provider>
  );
}

export function useRefresh() {
  const context = useContext(RefreshContext);
  if (context === undefined) {
    throw new Error('useRefresh must be used within a RefreshProvider');
  }
  return context;
}
