import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import { pgTable, text, timestamp, integer, jsonb, boolean, real, primaryKey, varchar } from 'drizzle-orm/pg-core';

// Define the assets table
export const assets = pgTable('assets', {
  id: text('id').primaryKey(),
  filename: text('filename').notNull(),
  filePath: text('file_path').notNull(),
  fileUrl: text('file_url').notNull(),
  thumbnailUrl: text('thumbnail_url').notNull(),
  type: text('type').notNull(),
  size: integer('size').notNull(),
  lastModified: timestamp('last_modified').notNull(),
  metadata: jsonb('metadata'),
  isStarred: boolean('is_starred').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Define the tags table
export const tags = pgTable('tags', {
  id: text('id').primaryKey(),
  name: text('name').notNull().unique(),
  color: text('color'), // Optional hex color for UI
  description: text('description'),
  isAutoGenerated: boolean('is_auto_generated').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Define the asset_tags junction table (many-to-many relationship)
export const assetTags = pgTable('asset_tags', {
  assetId: text('asset_id').notNull().references(() => assets.id, { onDelete: 'cascade' }),
  tagId: text('tag_id').notNull().references(() => tags.id, { onDelete: 'cascade' }),
  confidence: real('confidence').default(1.0), // AI confidence score (0.0 to 1.0)
  isAutoGenerated: boolean('is_auto_generated').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  pk: primaryKey({ columns: [table.assetId, table.tagId] }),
}));

// Define the folders table for tracking folder metadata and statistics
export const folders = pgTable('folders', {
  id: text('id').primaryKey(),
  path: text('path').notNull().unique(),
  parentPath: text('parent_path'),
  name: text('name').notNull(),
  fileCount: integer('file_count').default(0).notNull(),
  imageCount: integer('image_count').default(0).notNull(),
  totalSize: text('total_size').default('0').notNull(), // Using text to handle large numbers
  lastModified: timestamp('last_modified').notNull(),
  lastScanned: timestamp('last_scanned').defaultNow().notNull(),
  contentHash: text('content_hash'), // Hash of folder contents for change detection
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Define folder statistics table for historical tracking
export const folderStats = pgTable('folder_stats', {
  id: text('id').primaryKey(),
  folderId: text('folder_id').notNull().references(() => folders.id, { onDelete: 'cascade' }),
  statDate: timestamp('stat_date').defaultNow().notNull(),
  fileCount: integer('file_count').default(0).notNull(),
  imageCount: integer('image_count').default(0).notNull(),
  totalSize: text('total_size').default('0').notNull(), // Using text to handle large numbers
  newFiles: integer('new_files').default(0).notNull(),
  deletedFiles: integer('deleted_files').default(0).notNull(),
  modifiedFiles: integer('modified_files').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Define the users table for authentication and user management
export const users = pgTable('users', {
  id: text('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull().default('editor'), // 'admin', 'editor', 'viewer'
  isActive: boolean('is_active').default(true).notNull(),
  lastLogin: timestamp('last_login'),
  avatar: text('avatar'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Determine if running in Docker environment
const isDocker = process.env.DOCKER || process.env.CONTAINER || false;
const dbHost = isDocker ? 'db' : 'localhost';

// Create the database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || `postgresql://postgres:postgres@${dbHost}:5432/asset_hub`
});

// Create the database instance
export const db = drizzle(pool); 