import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';

// Use localhost if running on host, db if running in Docker
const isDocker = process.env.DOCKER || process.env.CONTAINER || false;
const dbHost = isDocker ? 'db' : 'localhost';
// Use port 5433 for localhost (as per docker-compose.yml), 5432 for Docker internal
const dbPort = isDocker ? '5432' : '5433';
const connectionString = process.env.DATABASE_URL || `postgresql://postgres:postgres@${dbHost}:${dbPort}/asset_hub`;

async function migrate() {
  const pool = new Pool({
    connectionString
  });

  try {
    console.log('Starting database migration...');
    console.log(`Connecting to database at ${dbHost}:${dbPort}...`);
    console.log(`Connection string: ${connectionString}`);

    // Get all migration files
    const migrationsDir = path.join(process.cwd(), 'src/lib/db/migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Ensure they run in order

    console.log(`Found ${migrationFiles.length} migration files:`, migrationFiles);

    // Execute each migration
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      const migrationPath = path.join(migrationsDir, file);
      const migration = fs.readFileSync(migrationPath, 'utf8');

      await pool.query(migration);
      console.log(`✅ Migration ${file} completed successfully`);
    }

    console.log('🎉 All migrations completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the migration
migrate(); 