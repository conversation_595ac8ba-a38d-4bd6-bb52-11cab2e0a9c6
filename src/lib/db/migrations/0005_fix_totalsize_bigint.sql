-- Fix totalSize field to handle large folder sizes
-- This migration changes the total_size column from INTEGER to TEXT to handle large values

-- First, alter the folders table
ALTER TABLE folders 
ALTER COLUMN total_size TYPE TEXT USING total_size::TEXT;

-- Update default value for new records
ALTER TABLE folders 
ALTER COLUMN total_size SET DEFAULT '0';

-- Also fix the folder_stats table
ALTER TABLE folder_stats 
ALTER COLUMN total_size TYPE TEXT USING total_size::TEXT;

-- Update default value for new records
ALTER TABLE folder_stats 
ALTER COLUMN total_size SET DEFAULT '0';

-- Update any existing records that might have been stored as integers
-- Convert them to text format
UPDATE folders 
SET total_size = total_size::TEXT 
WHERE total_size IS NOT NULL;

UPDATE folder_stats 
SET total_size = total_size::TEXT 
WHERE total_size IS NOT NULL;

-- Update table statistics for better query planning
ANALYZE folders;
ANALYZE folder_stats;
