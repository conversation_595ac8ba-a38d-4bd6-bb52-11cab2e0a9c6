-- Performance optimization indexes for Asset Hub
-- Migration: 0006_performance_indexes.sql

-- 1. Primary performance indexes for assets table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assets_last_modified_desc 
ON assets (last_modified DESC, created_at DESC, id DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assets_type 
ON assets (type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assets_file_path 
ON assets (file_path);

-- 2. Search optimization indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assets_filename_gin 
ON assets USING gin (to_tsvector('english', filename));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assets_filename_trigram 
ON assets USING gin (filename gin_trgm_ops);

-- Enable trigram extension if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 3. Folder tracking indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_folders_path 
ON folders (path);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_folders_last_modified 
ON folders (last_modified DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_folders_last_scanned 
ON folders (last_scanned);

-- 4. Asset tags indexes for AI features
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_asset_tags_asset_id 
ON asset_tags (asset_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_asset_tags_tag_id 
ON asset_tags (tag_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tags_name 
ON tags (name);

-- 5. Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assets_type_last_modified 
ON assets (type, last_modified DESC);

-- 6. Partial indexes for active assets (performance optimization)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assets_images_recent 
ON assets (last_modified DESC, created_at DESC) 
WHERE type = 'image';

-- 7. Statistics for query planner
ANALYZE assets;
ANALYZE folders;
ANALYZE tags;
ANALYZE asset_tags;
