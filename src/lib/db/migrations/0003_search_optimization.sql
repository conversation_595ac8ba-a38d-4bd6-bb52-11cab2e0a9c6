-- Search optimization indexes for advanced search functionality
-- This migration adds indexes to improve search performance across filenames, tags, and metadata

-- Add GIN index for metadata JSONB search (PostgreSQL specific)
CREATE INDEX IF NOT EXISTS idx_assets_metadata_gin ON assets USING GIN (metadata);

-- Add functional index for case-insensitive filename search
CREATE INDEX IF NOT EXISTS idx_assets_filename_lower ON assets (LOWER(filename));

-- Add functional index for case-insensitive tag name search
CREATE INDEX IF NOT EXISTS idx_tags_name_lower ON tags (LOWER(name));

-- Add composite index for asset_tags joins (already exists but ensuring it's optimal)
CREATE INDEX IF NOT EXISTS idx_asset_tags_composite ON asset_tags (asset_id, tag_id);

-- Add index for metadata text search (for when metadata is cast to text)
CREATE INDEX IF NOT EXISTS idx_assets_metadata_text ON assets USING GIN (to_tsvector('english', COALESCE(metadata::text, '')));

-- Update table statistics for better query planning
ANALYZE assets;
ANALYZE tags;
ANALYZE asset_tags;
