-- Folder tracking system migration
-- This migration adds folder metadata tracking for efficient change detection

-- Create folders table for tracking folder metadata and statistics
CREATE TABLE IF NOT EXISTS folders (
  id TEXT PRIMARY KEY,
  path TEXT NOT NULL UNIQUE,
  parent_path TEXT,
  name TEXT NOT NULL,
  file_count INTEGER DEFAULT 0 NOT NULL,
  image_count INTEGER DEFAULT 0 NOT NULL,
  total_size TEXT DEFAULT '0' NOT NULL,
  last_modified TIMESTAMP NOT NULL,
  last_scanned TIMESTAMP NOT NULL DEFAULT NOW(),
  content_hash TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create folder statistics table for historical tracking
CREATE TABLE IF NOT EXISTS folder_stats (
  id TEXT PRIMARY KEY,
  folder_id TEXT NOT NULL REFERENCES folders(id) ON DELETE CASCADE,
  stat_date TIMESTAMP NOT NULL DEFAULT NOW(),
  file_count INTEGER DEFAULT 0 NOT NULL,
  image_count INTEGER DEFAULT 0 NOT NULL,
  total_size TEXT DEFAULT '0' NOT NULL,
  new_files INTEGER DEFAULT 0 NOT NULL,
  deleted_files INTEGER DEFAULT 0 NOT NULL,
  modified_files INTEGER DEFAULT 0 NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_folders_path ON folders(path);
CREATE INDEX IF NOT EXISTS idx_folders_parent_path ON folders(parent_path);
CREATE INDEX IF NOT EXISTS idx_folders_last_modified ON folders(last_modified);
CREATE INDEX IF NOT EXISTS idx_folders_last_scanned ON folders(last_scanned);
CREATE INDEX IF NOT EXISTS idx_folders_content_hash ON folders(content_hash);

-- Indexes for folder stats
CREATE INDEX IF NOT EXISTS idx_folder_stats_folder_id ON folder_stats(folder_id);
CREATE INDEX IF NOT EXISTS idx_folder_stats_stat_date ON folder_stats(stat_date);

-- Update table statistics for better query planning
ANALYZE folders;
ANALYZE folder_stats;
