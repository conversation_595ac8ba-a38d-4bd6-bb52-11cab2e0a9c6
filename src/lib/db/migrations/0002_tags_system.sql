-- Create tags table
CREATE TABLE IF NOT EXISTS tags (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  color TEXT,
  description TEXT,
  is_auto_generated BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create asset_tags junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS asset_tags (
  asset_id TEXT NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  tag_id TEXT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
  confidence REAL DEFAULT 1.0,
  is_auto_generated BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  PRIMARY KEY (asset_id, tag_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
CREATE INDEX IF NOT EXISTS idx_tags_auto_generated ON tags(is_auto_generated);
CREATE INDEX IF NOT EXISTS idx_asset_tags_asset_id ON asset_tags(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_tags_tag_id ON asset_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_asset_tags_confidence ON asset_tags(confidence);
CREATE INDEX IF NOT EXISTS idx_asset_tags_auto_generated ON asset_tags(is_auto_generated);

-- Insert some common default tags for testing
INSERT INTO tags (id, name, color, description, is_auto_generated) VALUES
  ('tag_nature', 'nature', '#22c55e', 'Natural landscapes, plants, animals', false),
  ('tag_people', 'people', '#3b82f6', 'Photos containing people', false),
  ('tag_architecture', 'architecture', '#8b5cf6', 'Buildings, structures, urban scenes', false),
  ('tag_food', 'food', '#f59e0b', 'Food and dining related images', false),
  ('tag_travel', 'travel', '#ef4444', 'Travel and vacation photos', false),
  ('tag_portrait', 'portrait', '#ec4899', 'Portrait photography', false),
  ('tag_landscape', 'landscape', '#10b981', 'Landscape photography', false),
  ('tag_indoor', 'indoor', '#6b7280', 'Indoor scenes', false),
  ('tag_outdoor', 'outdoor', '#059669', 'Outdoor scenes', false),
  ('tag_vehicle', 'vehicle', '#dc2626', 'Cars, bikes, transportation', false)
ON CONFLICT (name) DO NOTHING;
