'use client'

import React, { useState, useEffect } from 'react';
import { Zap, Wifi, WifiOff, Database } from 'lucide-react';
import { cacheUtils } from './ServiceWorkerRegistration';

interface PerformanceStats {
  cacheSize: number;
  serviceWorkerActive: boolean;
  networkStatus: 'online' | 'offline';
  virtualScrollingActive: boolean;
  optimizedImagesLoaded: number;
}

export function PerformanceIndicator() {
  const [stats, setStats] = useState<PerformanceStats>({
    cacheSize: 0,
    serviceWorkerActive: false,
    networkStatus: 'online',
    virtualScrollingActive: false,
    optimizedImagesLoaded: 0
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check service worker status
    const checkServiceWorker = () => {
      try {
        if (typeof navigator !== 'undefined' && 'serviceWorker' in navigator) {
          setStats(prev => ({
            ...prev,
            serviceWorkerActive: !!navigator.serviceWorker.controller
          }));
        }
      } catch (error) {
        console.error('Error checking service worker:', error);
      }
    };

    // Check network status
    const updateNetworkStatus = () => {
      try {
        if (typeof navigator !== 'undefined') {
          setStats(prev => ({
            ...prev,
            networkStatus: navigator.onLine ? 'online' : 'offline'
          }));
        }
      } catch (error) {
        console.error('Error checking network status:', error);
      }
    };

    // Get cache size
    const updateCacheSize = async () => {
      try {
        const size = await cacheUtils.getCacheSize();
        setStats(prev => ({ ...prev, cacheSize: size }));
      } catch (error) {
        console.error('Failed to get cache size:', error);
      }
    };

    // Check for virtual scrolling
    const checkVirtualScrolling = () => {
      const virtualGrids = document.querySelectorAll('[data-virtual-grid]');
      setStats(prev => ({
        ...prev,
        virtualScrollingActive: virtualGrids.length > 0
      }));
    };

    // Count optimized images
    const countOptimizedImages = () => {
      const optimizedImages = document.querySelectorAll('img[data-optimized]');
      setStats(prev => ({
        ...prev,
        optimizedImagesLoaded: optimizedImages.length
      }));
    };

    // Initial checks
    checkServiceWorker();
    updateNetworkStatus();
    updateCacheSize();
    checkVirtualScrolling();
    countOptimizedImages();

    // Set up event listeners
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // Periodic updates
    const interval = setInterval(() => {
      updateCacheSize();
      checkVirtualScrolling();
      countOptimizedImages();
    }, 5000);

    return () => {
      window.removeEventListener('online', updateNetworkStatus);
      window.removeEventListener('offline', updateNetworkStatus);
      clearInterval(interval);
    };
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getPerformanceScore = () => {
    let score = 0;
    if (stats.serviceWorkerActive) score += 25;
    if (stats.networkStatus === 'online') score += 25;
    if (stats.virtualScrollingActive) score += 25;
    if (stats.optimizedImagesLoaded > 0) score += 25;
    return score;
  };

  const performanceScore = getPerformanceScore();
  const scoreColor = performanceScore >= 75 ? 'text-green-500' : 
                    performanceScore >= 50 ? 'text-yellow-500' : 'text-red-500';

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-primary text-primary-foreground p-2 rounded-full shadow-lg hover:bg-primary/90 transition-colors z-50"
        title="Show performance stats"
      >
        <Zap className="h-4 w-4" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-card border border-border rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Zap className={`h-4 w-4 ${scoreColor}`} />
          <span className="font-medium text-sm">Performance</span>
          <span className={`text-sm font-bold ${scoreColor}`}>
            {performanceScore}%
          </span>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-muted-foreground hover:text-foreground text-sm"
        >
          ×
        </button>
      </div>

      <div className="space-y-2 text-xs">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Database className="h-3 w-3" />
            <span>Service Worker</span>
          </div>
          <span className={stats.serviceWorkerActive ? 'text-green-500' : 'text-red-500'}>
            {stats.serviceWorkerActive ? 'Active' : 'Inactive'}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {stats.networkStatus === 'online' ? 
              <Wifi className="h-3 w-3" /> : 
              <WifiOff className="h-3 w-3" />
            }
            <span>Network</span>
          </div>
          <span className={stats.networkStatus === 'online' ? 'text-green-500' : 'text-red-500'}>
            {stats.networkStatus}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span>Cache Size</span>
          <span className="text-muted-foreground">
            {formatBytes(stats.cacheSize)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span>Virtual Scroll</span>
          <span className={stats.virtualScrollingActive ? 'text-green-500' : 'text-muted-foreground'}>
            {stats.virtualScrollingActive ? 'Active' : 'Inactive'}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span>Optimized Images</span>
          <span className="text-muted-foreground">
            {stats.optimizedImagesLoaded}
          </span>
        </div>
      </div>

      <div className="mt-3 pt-2 border-t border-border">
        <button
          onClick={async () => {
            await cacheUtils.clearAllCaches();
            setStats(prev => ({ ...prev, cacheSize: 0 }));
          }}
          className="text-xs text-destructive hover:underline"
        >
          Clear All Caches
        </button>
      </div>
    </div>
  );
}
