'use client'

import { useEffect } from 'react';

export function ServiceWorkerRegistration() {
  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      // Register service worker
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('🔧 Service Worker registered successfully:', registration);
          
          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  console.log('🔄 New service worker available, consider refreshing');
                  // Optionally show a notification to the user about the update
                }
              });
            }
          });
        })
        .catch((error) => {
          console.error('❌ Service Worker registration failed:', error);
        });

      // Listen for service worker messages
      navigator.serviceWorker.addEventListener('message', (event) => {
        const { type, payload } = event.data;
        
        switch (type) {
          case 'CACHE_SIZE':
            console.log('📊 Total cache size:', formatBytes(payload.size));
            break;
        }
      });

      // Cleanup function
      return () => {
        // Remove event listeners if needed
      };
    }
  }, []);

  return null; // This component doesn't render anything
}

// Utility function to format bytes
function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Export utility functions for cache management
export const cacheUtils = {
  clearCache: async (cacheName: string) => {
    try {
      if (typeof navigator !== 'undefined' && 'serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'CLEAR_CACHE',
          payload: { cacheName }
        });
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  },

  clearAllCaches: async () => {
    try {
      if (typeof navigator !== 'undefined' && 'serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'CLEAR_ALL_CACHES'
        });
      }
    } catch (error) {
      console.error('Error clearing all caches:', error);
    }
  },

  getCacheSize: async (): Promise<number> => {
    return new Promise((resolve) => {
      try {
        if (typeof navigator !== 'undefined' && 'serviceWorker' in navigator && navigator.serviceWorker.controller) {
          const channel = new MessageChannel();

          channel.port1.onmessage = (event) => {
            if (event.data.type === 'CACHE_SIZE') {
              resolve(event.data.size);
            }
          };

          navigator.serviceWorker.controller.postMessage({
            type: 'GET_CACHE_SIZE'
          }, [channel.port2]);
        } else {
          resolve(0);
        }
      } catch (error) {
        console.error('Error getting cache size:', error);
        resolve(0);
      }
    });
  }
};
