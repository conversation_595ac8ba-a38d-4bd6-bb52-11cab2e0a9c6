'use client'

import React from "react";
import { Asset } from "@/lib/types";
import { AssetCard } from "./AssetCard";
import { AssetGridSkeleton } from "./SkeletonLoader";

interface AssetGridProps {
  assets: Asset[];
  onAssetClick: (asset: Asset) => void;
  loading?: boolean;
  showSkeleton?: boolean;
  onAssetUpdate?: (asset: Asset) => void;
}

export function AssetGrid({ assets, onAssetClick, loading = false, showSkeleton = false, onAssetUpdate }: AssetGridProps) {
  // Show skeleton when explicitly requested or when loading with no assets
  if (showSkeleton || (loading && assets.length === 0)) {
    return <AssetGridSkeleton count={18} />;
  }

  if (assets.length === 0) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">No assets found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
        {assets.map((asset) => (
          <AssetCard
            key={asset.id}
            asset={asset}
            onClick={() => onAssetClick(asset)}
            onAssetUpdate={onAssetUpdate}
          />
        ))}
      </div>
    </div>
  );
}
