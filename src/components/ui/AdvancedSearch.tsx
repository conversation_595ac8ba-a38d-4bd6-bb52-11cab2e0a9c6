'use client'

import React, { useState } from "react";
import { Filter, CalendarIcon, X, Info } from "lucide-react";
import { useSearch, useAssets } from "@/lib/store";

// Hardcoded data for simplicity
const YEARS = ['2025', '2024', '2023', '2022', '2021'];
const MONTHS = [
  {name: 'January', value: '1'},
  {name: 'February', value: '2'},
  {name: 'March', value: '3'},
  {name: 'April', value: '4'},
  {name: 'May', value: '5'},
  {name: 'June', value: '6'},
  {name: 'July', value: '7'},
  {name: 'August', value: '8'},
  {name: 'September', value: '9'},
  {name: 'October', value: '10'},
  {name: 'November', value: '11'},
  {name: 'December', value: '12'}
];
const DAYS = Array.from({ length: 31 }, (_, i) => String(i + 1));

export function AdvancedSearch() {
  const { searchQuery } = useSearch();
  const { dateFilters, setFilters } = useAssets();
  
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [selectedYear, setSelectedYear] = useState<string | undefined>(dateFilters.year);
  const [selectedMonth, setSelectedMonth] = useState<string | undefined>(dateFilters.month);
  const [selectedDay, setSelectedDay] = useState<string | undefined>(dateFilters.day);
  const [selectedMonthName, setSelectedMonthName] = useState<string | undefined>(
    dateFilters.month ? MONTHS.find(m => m.value === dateFilters.month)?.name : undefined
  );

  // Handle selecting a year
  const handleYearClick = (year: string) => {
    const newYear = selectedYear === year ? undefined : year;
    setSelectedYear(newYear);
    
    // If we're changing or clearing the year, reset month and day
    if (selectedYear !== newYear) {
      setSelectedMonth(undefined);
      setSelectedMonthName(undefined);
      setSelectedDay(undefined);
    }
    
    // Apply the date filter
    setFilters({ 
      year: newYear,
      month: undefined,
      day: undefined
    });
  };

  // Handle selecting a month
  const handleMonthClick = (month: {name: string, value: string}) => {
    const newMonth = selectedMonth === month.value ? undefined : month.value;
    const newMonthName = newMonth ? month.name : undefined;
    setSelectedMonth(newMonth);
    setSelectedMonthName(newMonthName);
    
    // If we're changing or clearing the month, reset day
    if (selectedMonth !== newMonth) {
      setSelectedDay(undefined);
    }
    
    // Apply the date filter
    setFilters({ 
      year: selectedYear, 
      month: newMonth,
      day: undefined
    });
  };

  // Handle selecting a day
  const handleDayClick = (day: string) => {
    const newDay = selectedDay === day ? undefined : day;
    setSelectedDay(newDay);
    
    // Apply the date filter
    setFilters({ 
      year: selectedYear, 
      month: selectedMonth,
      day: newDay
    });
  };

  // Clear all filters
  const clearFilters = () => {
    setSelectedYear(undefined);
    setSelectedMonth(undefined);
    setSelectedMonthName(undefined);
    setSelectedDay(undefined);
    setFilters({});
  };

  return (
    <div>
      <button 
        onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
        className={`flex items-center gap-1 rounded-md ${isAdvancedOpen ? 'bg-secondary' : 'hover:bg-secondary'} px-3 py-1.5 text-sm`}
        title="Advanced filters"
      >
        <Filter className="h-4 w-4" />
        <span className="hidden md:inline">Filters</span>
        {(selectedYear || selectedMonth || selectedDay) && (
          <span className="flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
            {[selectedYear, selectedMonth, selectedDay].filter(Boolean).length}
          </span>
        )}
      </button>
      
      {/* Advanced options */}
      {isAdvancedOpen && (
        <div className="absolute mt-2 right-0 z-50 w-72 bg-background border rounded-md p-4 space-y-4 shadow-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <h3 className="text-sm font-medium">Search Tips</h3>
            </div>
            <div className="bg-secondary/30 p-2 rounded-md text-xs space-y-1">
              <p>• <span className="font-medium">Single words:</span> <code>spain</code> - finds assets with "spain" in filename, tags, or metadata</p>
              <p>• <span className="font-medium">Multiple words (OR):</span> <code>king queen</code> - finds assets with either "king" OR "queen"</p>
              <p>• <span className="font-medium">Exact phrases:</span> <code>"King Salaman"</code> - finds assets with the exact phrase "King Salaman"</p>
              <p>• <span className="font-medium">AND operator:</span> <code>king AND spain</code> - finds assets with both "king" AND "spain"</p>
              <p>• <span className="font-medium">OR operator:</span> <code>king OR queen</code> - finds assets with either "king" OR "queen"</p>
              <p>• <span className="font-medium">Exclusion:</span> <code>travel -indoor</code> - finds assets with "travel" but NOT "indoor"</p>
              <p>• <span className="font-medium">Phrase exclusion:</span> <code>"Real Madrid" -barcelona</code> - finds "Real Madrid" but excludes "barcelona"</p>
              <p>• <span className="font-medium">Complex searches:</span> <code>nature AND forest -urban -city</code> - nature AND forest, excluding urban and city</p>
              <p>• <span className="font-medium">Mixed operators:</span> <code>king AND spain OR france</code> - complex searches with multiple operators</p>
              <p className="text-muted-foreground mt-2">Search looks in: filenames, manual tags, AI-generated tags, and metadata</p>
              <p className="text-muted-foreground">💡 Use quotes for exact phrases, - for exclusions, AND/OR for logic</p>
            </div>
          </div>
          
          <div className="border-t pt-4">
            <div className="flex items-center mb-3">
              <CalendarIcon className="h-4 w-4 mr-2" />
              <h3 className="text-sm font-medium">Date Filters</h3>
              
              {(selectedYear || selectedMonth || selectedDay) && (
                <button 
                  className="ml-auto text-xs text-primary hover:underline flex items-center"
                  onClick={clearFilters}
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear
                </button>
              )}
            </div>
            
            {/* Date filter summary */}
            {(selectedYear || selectedMonth || selectedDay) && (
              <div className="bg-muted rounded-md p-2 mb-3 text-sm">
                <p>
                  Filtering by: 
                  {selectedYear && ` Year: ${selectedYear}`}
                  {selectedMonthName && ` / Month: ${selectedMonthName}`}
                  {selectedDay && ` / Day: ${selectedDay}`}
                </p>
              </div>
            )}
            
            {/* Year selection */}
            <div className="space-y-2 mb-3">
              <label className="text-xs font-medium block">Year</label>
              <div className="grid grid-cols-5 gap-1">
                {YEARS.map(year => (
                  <button
                    key={year}
                    className={`text-xs py-1 px-2 rounded ${
                      selectedYear === year 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-secondary/50 hover:bg-secondary'
                    }`}
                    onClick={() => handleYearClick(year)}
                  >
                    {year}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Month selection (only show if year is selected) */}
            {selectedYear && (
              <div className="space-y-2 mb-3">
                <label className="text-xs font-medium block">Month</label>
                <div className="grid grid-cols-4 gap-1">
                  {MONTHS.map(month => (
                    <button
                      key={month.value}
                      className={`text-xs py-1 px-1 rounded truncate ${
                        selectedMonth === month.value 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-secondary/50 hover:bg-secondary'
                      }`}
                      onClick={() => handleMonthClick(month)}
                    >
                      {month.name}
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            {/* Day selection (only show if month is selected) */}
            {selectedYear && selectedMonth && (
              <div className="space-y-2">
                <label className="text-xs font-medium block">Day</label>
                <div className="grid grid-cols-7 gap-1">
                  {DAYS.map(day => (
                    <button
                      key={day}
                      className={`text-xs py-1 rounded ${
                        selectedDay === day
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-secondary/50 hover:bg-secondary'
                      }`}
                      onClick={() => handleDayClick(day)}
                    >
                      {day}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 