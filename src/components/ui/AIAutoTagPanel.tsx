'use client'

import React, { useState, useEffect } from "react";
import { Asset } from "@/lib/types";
import { useAssetTags, useTags } from "@/lib/store";
import { Sparkles, Loader2, Tag, CheckCircle, AlertCircle, RefreshCw } from "lucide-react";
import { motion } from "framer-motion";

interface AIAutoTagPanelProps {
  asset: Asset;
  onTagsUpdated?: () => void;
}

export function AIAutoTagPanel({ asset, onTagsUpdated }: AIAutoTagPanelProps) {
  const { autoTagAsset, loading: autoTagLoading, error: autoTagError } = useAssetTags(asset.id);
  const { fetchAssetTags, assetTags, loading: tagsLoading } = useAssetTags(asset.id);
  const { tags: allTags } = useTags();
  
  const [autoTagResult, setAutoTagResult] = useState<any>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Fetch asset tags when component mounts
  useEffect(() => {
    if (asset.id) {
      fetchAssetTags(asset.id);
    }
  }, [asset.id]);

  const handleAutoTag = async () => {
    try {
      console.log('🤖 Starting auto-tag for asset:', asset.id);
      const result = await autoTagAsset(asset.id);
      setAutoTagResult(result);
      setShowDetails(true);
      
      // Notify parent component that tags were updated
      if (onTagsUpdated) {
        onTagsUpdated();
      }
      
      console.log('✅ Auto-tagging completed:', result);
    } catch (error) {
      console.error('❌ Auto-tagging failed:', error);
    }
  };

  const getTagName = (tagId: string) => {
    const tag = allTags.find(t => t.id === tagId);
    return tag?.name || tagId;
  };

  const getTagColor = (tagId: string) => {
    const tag = allTags.find(t => t.id === tagId);
    return tag?.color || '#6b7280';
  };

  if (asset.type !== 'image') {
    return null;
  }

  return (
    <div className="border border-border rounded-lg p-4 bg-card">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium flex items-center">
          <Sparkles className="h-4 w-4 mr-2 text-purple-500" />
          AI Auto-Tagging
        </h4>
        <button
          onClick={handleAutoTag}
          disabled={autoTagLoading}
          className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md bg-purple-600 text-white hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {autoTagLoading ? (
            <>
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <Sparkles className="h-3 w-3 mr-1" />
              Auto-Tag
            </>
          )}
        </button>
      </div>

      {/* Current Tags */}
      {assetTags.length > 0 && (
        <div className="mb-3">
          <div className="text-xs font-medium text-muted-foreground mb-1">Current Tags:</div>
          <div className="flex flex-wrap gap-1">
            {assetTags.map((assetTag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs"
                style={{ 
                  backgroundColor: `${getTagColor(assetTag.tagId)}20`,
                  color: getTagColor(assetTag.tagId),
                  border: `1px solid ${getTagColor(assetTag.tagId)}40`
                }}
              >
                <Tag className="h-3 w-3 mr-1" />
                {assetTag.tagName}
                {assetTag.isAutoGenerated && (
                  <Sparkles className="h-2 w-2 ml-1 opacity-60" />
                )}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Loading State */}
      {tagsLoading && (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm text-muted-foreground">Loading tags...</span>
        </div>
      )}

      {/* Error State */}
      {autoTagError && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center p-3 bg-red-50 border border-red-200 rounded-md mb-3"
        >
          <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
          <div className="text-sm text-red-700">
            <div className="font-medium">Auto-tagging failed</div>
            <div className="text-xs opacity-80">{autoTagError}</div>
          </div>
        </motion.div>
      )}

      {/* Success Result */}
      {autoTagResult && autoTagResult.success && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 border border-green-200 rounded-md p-3 mb-3"
        >
          <div className="flex items-center mb-2">
            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            <div className="text-sm font-medium text-green-800">
              Auto-tagging completed!
            </div>
          </div>
          
          <div className="text-xs text-green-700 space-y-1">
            <div>Applied {autoTagResult.result.appliedTagsCount} new tags</div>
            <div>Found {autoTagResult.result.totalSuggestionsCount} total suggestions</div>
          </div>

          {showDetails && autoTagResult.result.allSuggestions && (
            <div className="mt-3 pt-3 border-t border-green-200">
              <div className="text-xs font-medium text-green-800 mb-2">AI Suggestions:</div>
              <div className="space-y-1">
                {autoTagResult.result.allSuggestions.map((suggestion: any, index: number) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <span className="text-green-700">{suggestion.name}</span>
                    <div className="flex items-center">
                      <span className="text-green-600 mr-2">
                        {Math.round(suggestion.confidence * 100)}%
                      </span>
                      {suggestion.confidence >= 0.6 && (
                        <CheckCircle className="h-3 w-3 text-green-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <button
            onClick={() => setShowDetails(!showDetails)}
            className="mt-2 text-xs text-green-600 hover:text-green-700 underline"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
        </motion.div>
      )}

      {/* Info Text */}
      <div className="text-xs text-muted-foreground">
        <div className="flex items-center">
          <Sparkles className="h-3 w-3 mr-1 opacity-60" />
          AI will analyze the image and suggest relevant tags automatically
        </div>
      </div>
    </div>
  );
}
