'use client'

import React, { useState, useEffect } from "react";
import { ChevronDown, ChevronRight, Folder, FolderOpen } from "lucide-react";

type FolderNode = {
  name: string;
  path: string;
  type: 'year' | 'month' | 'day';
  children: FolderNode[];
  expanded?: boolean;
};

type FolderTreeFilterProps = {
  onFilterChange: (filters: {year?: string; month?: string; day?: string; path?: string}) => void;
};

export function FolderTreeFilter({ onFilterChange }: FolderTreeFilterProps) {
  const [folderStructure, setFolderStructure] = useState<FolderNode[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPath, setSelectedPath] = useState<string | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());

  // Fetch folder structure from API
  useEffect(() => {
    const fetchFolderStructure = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/folders/structure');
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setFolderStructure(data.folders);
          }
        }
      } catch (error) {
        console.error('Error fetching folder structure:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFolderStructure();
  }, []);

  // Handle folder toggle
  const toggleFolder = (path: string) => {
    const newExpandedFolders = new Set(expandedFolders);
    if (newExpandedFolders.has(path)) {
      newExpandedFolders.delete(path);
    } else {
      newExpandedFolders.add(path);
    }
    setExpandedFolders(newExpandedFolders);
  };

  // Handle folder selection
  const selectFolder = (folder: FolderNode) => {
    const isDeselecting = selectedPath === folder.path;
    const newPath = isDeselecting ? null : folder.path;
    setSelectedPath(newPath);
    
    // Determine the filter type based on the folder type
    const filters: {year?: string; month?: string; day?: string; path?: string} = {};
    
    if (newPath) {
      // Always include the full path for API filtering
      filters.path = newPath;
      
      // Add specific filters based on folder type
      if (folder.type === 'year') {
        filters.year = folder.name;
      } else if (folder.type === 'month') {
        // Extract year from parent path
        const yearMatch = folder.path.match(/\/(\d{4})\//);
        if (yearMatch) {
          filters.year = yearMatch[1];
        }
        filters.month = folder.name;
      } else if (folder.type === 'day') {
        // Extract year and month from parent path
        const pathParts = folder.path.split('/');
        // This is an approximation - would need to adjust based on actual path format
        if (pathParts.length >= 3) {
          const yearMatch = pathParts[pathParts.length - 3].match(/(\d{4})/);
          if (yearMatch) {
            filters.year = yearMatch[1];
          }
          filters.month = pathParts[pathParts.length - 2];
        }
        filters.day = folder.name;
      }
    }
    
    onFilterChange(filters);
  };

  const renderFolderNode = (folder: FolderNode) => {
    const isExpanded = expandedFolders.has(folder.path);
    const isSelected = selectedPath === folder.path;
    
    return (
      <div key={folder.path} className="select-none">
        <div 
          className={`flex items-center py-1 px-2 cursor-pointer hover:bg-secondary/50 ${isSelected ? 'bg-secondary text-primary-foreground' : ''}`}
        >
          <span 
            className="mr-1 cursor-pointer" 
            onClick={() => toggleFolder(folder.path)}
          >
            {folder.children.length > 0 ? (
              isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
            ) : <span className="w-4"></span>}
          </span>
          
          <span 
            className="flex items-center gap-1" 
            onClick={() => selectFolder(folder)}
          >
            {isExpanded ? 
              <FolderOpen className="h-4 w-4 text-primary" /> : 
              <Folder className="h-4 w-4 text-primary" />}
            <span>{folder.name}</span>
          </span>
        </div>
        
        {isExpanded && folder.children.length > 0 && (
          <div className="pl-4">
            {folder.children.map(child => renderFolderNode(child))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return <div className="p-4">Loading folder structure...</div>;
  }

  if (folderStructure.length === 0) {
    return <div className="p-4 text-muted-foreground">No folders found</div>;
  }

  return (
    <div className="border rounded-md p-2 max-h-96 overflow-auto">
      <h3 className="font-medium mb-2">Folder Filters</h3>
      <div>
        {folderStructure.map(folder => renderFolderNode(folder))}
      </div>
      {selectedPath && (
        <div className="mt-4">
          <button 
            className="text-xs text-primary hover:underline"
            onClick={() => {
              setSelectedPath(null);
              onFilterChange({});
            }}
          >
            Clear Filter
          </button>
        </div>
      )}
    </div>
  );
} 