'use client'

import React from "react";
import { cn } from "@/lib/utils";

interface SkeletonProps {
  className?: string;
}

export function Skeleton({ className }: SkeletonProps) {
  return (
    <div
      className={cn(
        "rounded-md bg-gradient-to-r from-muted via-muted/30 to-muted animate-shimmer",
        className
      )}
      style={{
        backgroundSize: "200% 100%"
      }}
    />
  );
}

interface AssetCardSkeletonProps {
  className?: string;
}

export function AssetCardSkeleton({ className }: AssetCardSkeletonProps) {
  return (
    <div className={cn("group relative rounded-md border border-border overflow-hidden bg-card", className)}>
      <div className="aspect-square">
        <Skeleton className="w-full h-full" />
      </div>
      <div className="p-2 space-y-2">
        <Skeleton className="h-3 w-3/4" />
        <Skeleton className="h-2 w-1/2" />
      </div>
    </div>
  );
}

interface AssetGridSkeletonProps {
  count?: number;
  className?: string;
}

export function AssetGridSkeleton({ count = 12, className }: AssetGridSkeletonProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
        {Array.from({ length: count }).map((_, i) => (
          <AssetCardSkeleton key={i} />
        ))}
      </div>
    </div>
  );
}

interface DashboardSkeletonProps {
  className?: string;
}

export function DashboardSkeleton({ className }: DashboardSkeletonProps) {
  return (
    <div className={cn("space-y-6 px-1", className)}>
      {/* Header skeleton */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-3">
          <Skeleton className="h-8 w-24" />
        </div>
        <div className="flex items-center space-x-2">
          <Skeleton className="h-9 w-64" />
          <Skeleton className="h-9 w-9" />
          <div className="flex rounded-md border border-border">
            <Skeleton className="h-9 w-9" />
            <Skeleton className="h-9 w-9" />
          </div>
        </div>
      </div>

      {/* Asset grid skeleton */}
      <AssetGridSkeleton count={18} />
    </div>
  );
}

interface LoadingStateProps {
  title?: string;
  description?: string;
  showSpinner?: boolean;
  className?: string;
}

export function LoadingState({ 
  title = "Loading assets...", 
  description = "Fetching your digital assets from the database",
  showSpinner = true,
  className 
}: LoadingStateProps) {
  return (
    <div className={cn("flex flex-col h-full items-center justify-center p-8", className)}>
      {showSpinner && (
        <div className="relative mb-4">
          <div className="w-12 h-12 border-4 border-muted rounded-full animate-pulse"></div>
          <div className="absolute inset-0 w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      <h2 className="text-xl font-medium mb-2 text-center">{title}</h2>
      <p className="text-muted-foreground text-center max-w-md">
        {description}
      </p>
    </div>
  );
}

interface EmptyStateProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
  className?: string;
}

export function EmptyState({
  title = "No assets found",
  description = "Upload your first assets to get started with your digital asset management",
  icon,
  action,
  className
}: EmptyStateProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center text-center bg-secondary/50 rounded-lg p-12", className)}>
      {icon && (
        <div className="rounded-full bg-secondary p-5 mb-4">
          {icon}
        </div>
      )}
      <h2 className="text-2xl font-semibold mb-2">{title}</h2>
      <p className="text-muted-foreground mb-6 max-w-md">
        {description}
      </p>
      {action}
    </div>
  );
}

interface FilterLoadingProps {
  className?: string;
}

export function FilterLoading({ className }: FilterLoadingProps) {
  return (
    <div className={cn("flex items-center gap-2 text-muted-foreground", className)}>
      <div className="w-4 h-4 border-2 border-muted border-t-primary rounded-full animate-spin"></div>
      <span className="text-sm">Applying filters...</span>
    </div>
  );
}
