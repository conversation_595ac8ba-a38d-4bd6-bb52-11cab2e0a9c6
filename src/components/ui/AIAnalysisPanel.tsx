'use client'

import React, { useState } from 'react';
import { Asset } from '@/lib/types';
import { generateTextWithImages } from '@/lib/api/util';
import { Loader2, <PERSON>rk<PERSON>, RefreshCw, Bug } from 'lucide-react';

interface AIAnalysisPanelProps {
  asset: Asset;
}

export function AIAnalysisPanel({ asset }: AIAnalysisPanelProps) {
  const [analysis, setAnalysis] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<string>('azure-gpt-4o');
  const [debugInfo, setDebugInfo] = useState<string>('');

  const analyzeImage = async () => {
    if (asset.type !== 'image') return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('Starting image analysis for:', asset.fileUrl);
      console.log('Selected model:', selectedModel);
      console.log('Full asset object:', asset);

      const result = await generateTextWithImages(
        "Analyze this image in detail. Include information about: 1) What's in the image 2) Any notable objects, people, or scenes 3) The composition, lighting, and style 4) Any text visible in the image 5) The overall mood or theme. Format your response in clear paragraphs.",
        [asset.fileUrl],
        selectedModel
      );

      console.log('Raw result from generateTextWithImages:', result);
      setAnalysis(result.text);
      console.log('Image analysis completed successfully');
    } catch (err) {
      console.error('AI analysis error:', err);

      // Provide more specific error messages based on the error type
      let errorMessage = 'Unknown error occurred';

      if (err instanceof Error) {
        if (err.message.includes('Vision proxy error: 400')) {
          errorMessage = 'Bad request - The image might not be accessible or the request format is invalid. Please try again or contact support.';
        } else if (err.message.includes('Vision proxy error: 401')) {
          errorMessage = 'Authentication failed - Please check your API credentials.';
        } else if (err.message.includes('Vision proxy error: 403')) {
          errorMessage = 'Access forbidden - You may not have permission to use this feature.';
        } else if (err.message.includes('Vision proxy error: 429')) {
          errorMessage = 'Rate limit exceeded - Please wait a moment and try again.';
        } else if (err.message.includes('Vision proxy error: 500')) {
          errorMessage = 'Server error - The AI service is temporarily unavailable. Please try again later.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(`Error analyzing image: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testImageUrl = async () => {
    try {
      setDebugInfo('Testing image URL accessibility...');

      const response = await fetch('/api/vision/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imageUrl: asset.fileUrl })
      });

      const result = await response.json();
      setDebugInfo(JSON.stringify(result, null, 2));
    } catch (err) {
      setDebugInfo(`Debug test failed: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  const testDirectVisionAPI = async () => {
    try {
      setDebugInfo('Testing direct vision API call...');
      setIsLoading(true);

      const response = await fetch('/api/vision/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          imageUrl: asset.fileUrl,
          prompt: "Analyze this image in detail. Include information about: 1) What's in the image 2) Any notable objects, people, or scenes 3) The composition, lighting, and style 4) Any text visible in the image 5) The overall mood or theme. Format your response in clear paragraphs."
        })
      });

      const result = await response.json();

      if (result.success) {
        setAnalysis(result.text);
        setDebugInfo(`Direct API test successful!\n\nFormat used: ${result.formatUsed}\nUsage: ${JSON.stringify(result.usage, null, 2)}`);
      } else {
        setDebugInfo(`Direct API test failed:\n${JSON.stringify(result, null, 2)}`);
      }
    } catch (err) {
      setDebugInfo(`Direct API test failed: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testTextAPI = async () => {
    try {
      setDebugInfo('Testing basic text API (no vision)...');
      setIsLoading(true);

      const response = await fetch('/api/vision/test-text', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });

      const result = await response.json();

      if (result.success) {
        setDebugInfo(`Text API test successful!\n\nResponse: ${result.text}\nUsage: ${JSON.stringify(result.usage, null, 2)}`);
      } else {
        setDebugInfo(`Text API test failed:\n${JSON.stringify(result, null, 2)}`);
      }
    } catch (err) {
      setDebugInfo(`Text API test failed: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (asset.type !== 'image') {
    return null;
  }

  return (
    <div className="border border-border rounded-lg overflow-hidden">
      <div className="bg-muted/20 px-4 py-2 border-b border-border flex justify-between items-center">
        <h3 className="font-medium flex items-center">
          <Sparkles className="h-4 w-4 mr-2 text-primary" />
          AI Image Analysis
        </h3>
        <div className="flex items-center space-x-2">
          <select
            className="text-xs px-2 py-1 rounded border border-border bg-background"
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
          >
            <option value="azure-gpt-4o">GPT-4o</option>
          </select>
          <button
            onClick={testImageUrl}
            className="text-xs px-2 py-1 rounded border border-border bg-background text-foreground flex items-center"
            title="Test image URL accessibility"
          >
            <Bug className="h-3 w-3 mr-1" />
            Debug
          </button>
          <button
            onClick={testTextAPI}
            disabled={isLoading}
            className="text-xs px-2 py-1 rounded border border-border bg-secondary text-secondary-foreground flex items-center"
            title="Test basic text API (no vision)"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Testing...
              </>
            ) : (
              'Text Test'
            )}
          </button>
          <button
            onClick={testDirectVisionAPI}
            disabled={isLoading}
            className="text-xs px-2 py-1 rounded border border-border bg-secondary text-secondary-foreground flex items-center"
            title="Test direct vision API call"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Testing...
              </>
            ) : (
              'Vision Test'
            )}
          </button>
          <button
            onClick={analyzeImage}
            disabled={isLoading}
            className="text-xs px-2 py-1 rounded bg-primary text-primary-foreground flex items-center"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Analyzing...
              </>
            ) : analysis ? (
              <>
                <RefreshCw className="h-3 w-3 mr-1" />
                Refresh
              </>
            ) : (
              'Analyze'
            )}
          </button>
        </div>
      </div>
      
      <div className="p-4">
        {error ? (
          <div className="text-sm text-destructive">{error}</div>
        ) : isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
            <span>Analyzing image...</span>
          </div>
        ) : analysis ? (
          <div className="text-sm space-y-2 whitespace-pre-wrap">{analysis}</div>
        ) : (
          <div className="text-sm text-muted-foreground text-center py-6">
            Click &quot;Analyze&quot; to get AI insights about this image
          </div>
        )}

        {debugInfo && (
          <div className="mt-4 p-3 bg-muted/50 rounded border">
            <h4 className="text-xs font-medium mb-2">Debug Information:</h4>
            <pre className="text-xs text-muted-foreground whitespace-pre-wrap overflow-auto max-h-40">
              {debugInfo}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
