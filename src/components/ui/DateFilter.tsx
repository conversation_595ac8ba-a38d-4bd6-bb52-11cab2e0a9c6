'use client'

import React, { useState, useEffect } from "react";
import { Calendar, ChevronDown } from "lucide-react";

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const DAYS = Array.from({ length: 31 }, (_, i) => i + 1);

type DateFilterProps = {
  onFilterChange: (filters: { year?: string; month?: string; day?: string }) => void;
};

export function DateFilter({ onFilterChange }: DateFilterProps) {
  const [years, setYears] = useState<string[]>([]);
  const [selectedYear, setSelectedYear] = useState<string | undefined>(undefined);
  const [selectedMonth, setSelectedMonth] = useState<string | undefined>(undefined);
  const [selectedDay, setSelectedDay] = useState<string | undefined>(undefined);
  const [isOpen, setIsOpen] = useState(false);

  // Fetch available years from assets
  useEffect(() => {
    const fetchYears = async () => {
      try {
        // This API endpoint needs to be implemented
        const response = await fetch('/api/photos/years');
        
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.years) {
            // Sort years in descending order (newest first)
            const sortedYears = [...data.years].sort((a, b) => b.localeCompare(a));
            setYears(sortedYears);
          }
        }
      } catch (error) {
        console.error('Error fetching years:', error);
        // Fallback to current year and a few previous years
        const currentYear = new Date().getFullYear();
        const fallbackYears = Array.from({ length: 5 }, (_, i) => String(currentYear - i));
        setYears(fallbackYears);
      }
    };

    fetchYears();
  }, []);

  // Handle year change
  const handleYearChange = (year: string | undefined) => {
    setSelectedYear(year);
    setSelectedMonth(undefined);
    setSelectedDay(undefined);
    
    onFilterChange({ year });
  };

  // Handle month change
  const handleMonthChange = (month: string | undefined) => {
    setSelectedMonth(month);
    setSelectedDay(undefined);
    
    onFilterChange({ 
      year: selectedYear, 
      month: month ? String(MONTHS.indexOf(month) + 1) : undefined 
    });
  };

  // Handle day change
  const handleDayChange = (day: string | undefined) => {
    setSelectedDay(day);
    
    onFilterChange({ 
      year: selectedYear, 
      month: selectedMonth ? String(MONTHS.indexOf(selectedMonth) + 1) : undefined,
      day
    });
  };

  // Handle clearing all filters
  const clearFilters = () => {
    setSelectedYear(undefined);
    setSelectedMonth(undefined);
    setSelectedDay(undefined);
    onFilterChange({});
  };

  return (
    <div className="relative">
      <div 
        className="flex items-center gap-2 p-2 border rounded-md cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Calendar className="h-4 w-4" />
        <span className="text-sm">
          {selectedYear || selectedMonth || selectedDay 
            ? `${selectedYear || ''}${selectedMonth ? ' / ' + selectedMonth : ''}${selectedDay ? ' / ' + selectedDay : ''}`
            : 'Filter by Date'}
        </span>
        <ChevronDown className="h-4 w-4 ml-auto" />
      </div>
      
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-background border rounded-md shadow-md p-4 z-10 w-64">
          <div className="flex flex-col gap-4">
            {/* Year selection */}
            <div>
              <label className="text-xs font-medium block mb-1">Year</label>
              <div className="grid grid-cols-4 gap-1">
                {years.slice(0, 8).map(year => (
                  <button
                    key={year}
                    className={`text-xs py-1 px-2 rounded ${
                      selectedYear === year 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-secondary/50 hover:bg-secondary'
                    }`}
                    onClick={() => handleYearChange(selectedYear === year ? undefined : year)}
                  >
                    {year}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Month selection (only show if year is selected) */}
            {selectedYear && (
              <div>
                <label className="text-xs font-medium block mb-1">Month</label>
                <div className="grid grid-cols-3 gap-1">
                  {MONTHS.map(month => (
                    <button
                      key={month}
                      className={`text-xs py-1 px-1 rounded truncate ${
                        selectedMonth === month 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-secondary/50 hover:bg-secondary'
                      }`}
                      onClick={() => handleMonthChange(selectedMonth === month ? undefined : month)}
                    >
                      {month}
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            {/* Day selection (only show if month is selected) */}
            {selectedYear && selectedMonth && (
              <div>
                <label className="text-xs font-medium block mb-1">Day</label>
                <div className="grid grid-cols-7 gap-1">
                  {DAYS.map(day => (
                    <button
                      key={day}
                      className={`text-xs py-1 rounded ${
                        selectedDay === String(day)
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-secondary/50 hover:bg-secondary'
                      }`}
                      onClick={() => handleDayChange(selectedDay === String(day) ? undefined : String(day))}
                    >
                      {day}
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            {/* Clear filters button */}
            {(selectedYear || selectedMonth || selectedDay) && (
              <button 
                className="text-xs text-primary hover:underline"
                onClick={clearFilters}
              >
                Clear Filters
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 