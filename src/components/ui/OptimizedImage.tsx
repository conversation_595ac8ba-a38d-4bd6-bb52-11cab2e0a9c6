'use client'

import React, { useState, useRef, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  quality?: number;
  priority?: boolean;
  onLoad?: () => void;
  onError?: (e: any) => void;
  placeholder?: 'blur' | 'empty';
  sizes?: string;
}

export function OptimizedImage({
  src,
  alt,
  className = '',
  width,
  height,
  quality = 75,
  priority = false,
  onLoad,
  onError,
  placeholder = 'blur',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(priority);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  // Intersection Observer for lazy loading (unless priority)
  useEffect(() => {
    if (priority) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority]);

  // Generate optimized image URLs
  const generateImageUrl = (originalSrc: string, targetWidth?: number) => {
    // If it's already an API URL, add optimization parameters
    if (originalSrc.includes('/api/storage/')) {
      const url = new URL(originalSrc, window.location.origin);
      if (targetWidth) url.searchParams.set('w', targetWidth.toString());
      url.searchParams.set('q', quality.toString());
      url.searchParams.set('f', 'webp'); // Prefer WebP format
      return url.toString();
    }
    return originalSrc;
  };

  // Generate srcSet for responsive images
  const generateSrcSet = (originalSrc: string) => {
    const widths = [320, 640, 768, 1024, 1280, 1920];
    return widths
      .map(w => `${generateImageUrl(originalSrc, w)} ${w}w`)
      .join(', ');
  };

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = (e: any) => {
    setHasError(true);
    onError?.(e);
  };

  // Show placeholder while not visible or loading
  if (!isVisible || (!isLoaded && !hasError)) {
    return (
      <div
        ref={imgRef}
        className={`${className} ${placeholder === 'blur' ? 'bg-muted/20 animate-pulse' : 'bg-muted/10'} flex items-center justify-center`}
        style={{ width, height }}
      >
        {placeholder === 'blur' && (
          <div className="w-8 h-8 bg-muted/40 rounded animate-pulse"></div>
        )}
      </div>
    );
  }

  // Show error state
  if (hasError) {
    return (
      <div
        className={`${className} bg-muted/20 flex items-center justify-center text-muted-foreground`}
        style={{ width, height }}
      >
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
        </svg>
      </div>
    );
  }

  return (
    <img
      ref={imgRef}
      src={generateImageUrl(src, width)}
      srcSet={generateSrcSet(src)}
      sizes={sizes}
      alt={alt}
      className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
      width={width}
      height={height}
      onLoad={handleLoad}
      onError={handleError}
      loading={priority ? 'eager' : 'lazy'}
      decoding="async"
      data-optimized="true"
    />
  );
}

// Hook for detecting WebP support
export function useWebPSupport() {
  const [supportsWebP, setSupportsWebP] = useState<boolean | null>(null);

  useEffect(() => {
    const checkWebPSupport = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const dataURL = canvas.toDataURL('image/webp');
      setSupportsWebP(dataURL.indexOf('data:image/webp') === 0);
    };

    checkWebPSupport();
  }, []);

  return supportsWebP;
}

// Progressive image loading component
export function ProgressiveImage({
  src,
  lowQualitySrc,
  alt,
  className = '',
  ...props
}: OptimizedImageProps & { lowQualitySrc?: string }) {
  const [isHighQualityLoaded, setIsHighQualityLoaded] = useState(false);

  return (
    <div className="relative">
      {/* Low quality placeholder */}
      {lowQualitySrc && !isHighQualityLoaded && (
        <OptimizedImage
          src={lowQualitySrc}
          alt={alt}
          className={`${className} absolute inset-0 filter blur-sm`}
          quality={10}
          {...props}
        />
      )}
      
      {/* High quality image */}
      <OptimizedImage
        src={src}
        alt={alt}
        className={className}
        onLoad={() => setIsHighQualityLoaded(true)}
        {...props}
      />
    </div>
  );
}

// Image preloader utility
export class ImagePreloader {
  private static cache = new Set<string>();

  static preload(src: string): Promise<void> {
    if (this.cache.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.cache.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  }

  static preloadMultiple(sources: string[]): Promise<void[]> {
    return Promise.all(sources.map(src => this.preload(src)));
  }

  static isPreloaded(src: string): boolean {
    return this.cache.has(src);
  }

  static clearCache(): void {
    this.cache.clear();
  }
}
