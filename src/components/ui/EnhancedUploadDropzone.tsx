'use client'

import React, { useCallback, useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { motion } from "framer-motion";
import { Upload, X, FileCheck, AlertCircle, FolderPlus, Calendar, Settings, Folder } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { cn, formatBytes } from "@/lib/utils";
import { useAssets } from "@/lib/store";
import { useRouter } from "next/navigation";

interface FolderOption {
  value: string;
  label: string;
  path: string;
  type: 'existing' | 'new';
}

export function EnhancedUploadDropzone() {
  const router = useRouter();
  const { addAsset } = useAssets();
  const [files, setFiles] = useState<Array<File & { preview?: string }>>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploading, setUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  
  // Remote upload folder management state
  const [serverFolders, setServerFolders] = useState<FolderOption[]>([]);
  const [selectedServerFolder, setSelectedServerFolder] = useState<string>('');
  const [folderMode, setFolderMode] = useState<'existing' | 'new'>('existing');
  const [newFolderDate, setNewFolderDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [newFolderName, setNewFolderName] = useState<string>('');
  const [batchSize, setBatchSize] = useState<number>(10);
  const [loadingServerFolders, setLoadingServerFolders] = useState(false);

  // Load server folder structure on component mount
  useEffect(() => {
    loadServerFolders();
  }, []);

  // Add some default folders if API is slow
  useEffect(() => {
    if (serverFolders.length === 0 && !loadingServerFolders) {
      // Add current year/month as default
      const currentYear = new Date().getFullYear();
      const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
      const monthName = new Date().toLocaleDateString('en-US', { month: 'short' }).toUpperCase();

      setServerFolders([{
        value: `${currentYear}/${currentMonth}-${monthName}${currentYear}`,
        label: `${currentYear} - ${monthName} ${currentYear} (Current)`,
        path: `${currentYear}/${currentMonth}-${monthName}${currentYear}`,
        type: 'existing'
      }]);
    }
  }, [serverFolders.length, loadingServerFolders]);

  // Load available folders on the server
  const loadServerFolders = async () => {
    setLoadingServerFolders(true);
    try {
      console.log('📁 Loading server folders...');
      const response = await fetch('/api/folders/structure', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        const options: FolderOption[] = [];

        // Add existing server folders with better date parsing
        data.folders.forEach((year: any) => {
          year.children?.forEach((month: any) => {
            const fileCount = month.fileCount || 0;
            const fileCountText = fileCount > 0 ? ` (${fileCount} files)` : '';

            options.push({
              value: `${year.name}/${month.name}`,
              label: `${year.name} - ${month.name}${fileCountText}`,
              path: `${year.name}/${month.name}`,
              type: 'existing'
            });
          });
        });

        // Sort by date (most recent first) - parse YYYY/MM-MMMYYYY format
        options.sort((a, b) => {
          // Extract year and month from path like "2025/01-JAN2025"
          const parseDate = (path: string) => {
            const [year, monthPart] = path.split('/');
            const month = monthPart ? monthPart.split('-')[0] : '01';
            return `${year}-${month}`;
          };

          const dateA = parseDate(a.path);
          const dateB = parseDate(b.path);

          return dateB.localeCompare(dateA); // Most recent first
        });

        setServerFolders(options);
        console.log(`📁 Loaded ${options.length} server folders for remote upload`);
      }
    } catch (error) {
      console.error('Error loading server folder structure:', error);
      // Create some default options if API fails
      const currentYear = new Date().getFullYear();
      const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
      const monthName = new Date().toLocaleDateString('en-US', { month: 'short' }).toUpperCase();

      setServerFolders([{
        value: `${currentYear}/${currentMonth}-${monthName}${currentYear}`,
        label: `${currentYear} - ${monthName} ${currentYear}`,
        path: `${currentYear}/${currentMonth}-${monthName}${currentYear}`,
        type: 'existing'
      }]);
    } finally {
      setLoadingServerFolders(false);
    }
  };

  const generateNewFolderPath = () => {
    const date = new Date(newFolderDate);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const monthName = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
    
    if (newFolderName) {
      return `${year}/${month}-${monthName}${year}/${newFolderName}`;
    } else {
      return `${year}/${month}-${monthName}${year}`;
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => 
      Object.assign(file, { preview: URL.createObjectURL(file) })
    );
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
      'video/*': [],
      'audio/*': [],
      'application/pdf': [],
    }
  });

  const removeFile = (index: number) => {
    setFiles(prev => {
      const newFiles = [...prev];
      const file = newFiles[index];
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const uploadFiles = async () => {
    if (folderMode === 'existing' && !selectedServerFolder) {
      alert('Please select a destination folder on the server');
      return;
    }

    if (folderMode === 'new' && !newFolderDate) {
      alert('Please specify a date for the new folder');
      return;
    }

    setUploading(true);
    setUploadComplete(false);

    const targetFolder = folderMode === 'new' ? generateNewFolderPath() : selectedServerFolder;
    
    try {
      // Process files in batches
      for (let i = 0; i < files.length; i += batchSize) {
        const batch = files.slice(i, i + batchSize);
        
        await Promise.all(batch.map(async (file, batchIndex) => {
          const globalIndex = i + batchIndex;
          
          // Simulate upload progress
          for (let progress = 0; progress <= 100; progress += 20) {
            setUploadProgress(prev => ({ ...prev, [file.name]: progress }));
            await new Promise(r => setTimeout(r, 100));
          }
          
          // Create FormData for file upload
          const formData = new FormData();
          formData.append('file', file);
          formData.append('folder', targetFolder);
          formData.append('createFolder', folderMode === 'new' ? 'true' : 'false');
          
          // Upload to server
          const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          });
          
          if (!response.ok) {
            throw new Error(`Upload failed for ${file.name}`);
          }
          
          const result = await response.json();
          console.log(`Uploaded ${file.name} to ${targetFolder}`, result);
        }));
      }
      
      setUploadComplete(true);
      
      // Clear files and redirect after success
      setTimeout(() => {
        files.forEach(file => {
          if (file.preview) URL.revokeObjectURL(file.preview);
        });
        setFiles([]);
        setUploadProgress({});
        setUploadComplete(false);
        router.push('/');
      }, 2000);
      
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Folder Selection Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            Folder Organization
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Choose where to store your uploaded files on the server. Select an existing folder or create a new date-based folder for organization.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button
              variant={folderMode === 'existing' ? 'default' : 'outline'}
              onClick={() => setFolderMode('existing')}
              className="flex items-center gap-2"
            >
              <Folder className="h-4 w-4" />
              Use Existing Server Folder
            </Button>
            <Button
              variant={folderMode === 'new' ? 'default' : 'outline'}
              onClick={() => setFolderMode('new')}
              className="flex items-center gap-2"
            >
              <FolderPlus className="h-4 w-4" />
              Create New Date Folder
            </Button>
          </div>

          {folderMode === 'existing' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="server-folder-select">Select Server Destination Folder</Label>
                <Select value={selectedServerFolder} onValueChange={setSelectedServerFolder}>
                  <SelectTrigger>
                    <SelectValue placeholder={loadingServerFolders ? "Loading server folders..." : "Choose a folder on the server"} />
                  </SelectTrigger>
                  <SelectContent className="max-h-80">
                    {serverFolders.length > 0 ? (
                      <>
                        {/* Group folders by year for better organization */}
                        {(() => {
                          const foldersByYear = serverFolders.reduce((acc, folder) => {
                            const year = folder.path.split('/')[0];
                            if (!acc[year]) acc[year] = [];
                            acc[year].push(folder);
                            return acc;
                          }, {} as Record<string, typeof serverFolders>);

                          return Object.entries(foldersByYear)
                            .sort(([a], [b]) => b.localeCompare(a)) // Most recent year first
                            .map(([year, folders]) => (
                              <div key={year}>
                                <div className="px-2 py-1 text-xs font-semibold text-muted-foreground bg-muted/50">
                                  {year}
                                </div>
                                {folders.map((option) => (
                                  <SelectItem key={option.value} value={option.value} className="pl-4">
                                    📅 {option.label.split(' - ')[1]} {/* Show just the month part */}
                                  </SelectItem>
                                ))}
                              </div>
                            ));
                        })()}
                      </>
                    ) : (
                      <SelectItem value="" disabled>
                        No folders available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {selectedServerFolder && (
                  <p className="text-sm text-muted-foreground">
                    Files will be uploaded to server folder: <code className="bg-muted px-1 rounded">{selectedServerFolder}</code>
                  </p>
                )}
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={loadServerFolders}
                    disabled={loadingServerFolders}
                    className="h-6 px-2"
                  >
                    🔄 Refresh Folders
                  </Button>
                  <span>•</span>
                  <span>{serverFolders.length} folders available</span>
                </div>
              </div>
            </div>
          )}

          {folderMode === 'new' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-folder-date">Date for New Folder</Label>
                <Input
                  id="new-folder-date"
                  type="date"
                  value={newFolderDate}
                  onChange={(e) => setNewFolderDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-folder-name">Custom Folder Name (Optional)</Label>
                <Input
                  id="new-folder-name"
                  placeholder="e.g., Wedding, Vacation, Event"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                />
              </div>
              <div className="md:col-span-2">
                <p className="text-sm text-muted-foreground">
                  New folder will be created at: <code className="bg-muted px-1 rounded">{generateNewFolderPath()}</code>
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Upload Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="batch-size">Batch Processing Size</Label>
            <Select value={batchSize.toString()} onValueChange={(value) => setBatchSize(parseInt(value))}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 files per batch</SelectItem>
                <SelectItem value="10">10 files per batch</SelectItem>
                <SelectItem value="20">20 files per batch</SelectItem>
                <SelectItem value="50">50 files per batch</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              Larger batches upload faster but use more memory
            </p>
          </div>
        </CardContent>
      </Card>

      {/* File Drop Zone */}
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed border-border rounded-lg p-8 transition-colors",
          isDragActive ? "border-primary bg-primary bg-opacity-5" : "hover:border-primary hover:bg-primary/5",
          files.length > 0 && "h-32"
        )}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center text-center">
          <Upload className="h-10 w-10 text-muted-foreground mb-2" />
          <p className="text-lg font-medium">
            {isDragActive ? "Drop the files here..." : "Drag & drop files here, or click to select files"}
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Support for images, videos, audio, and documents
          </p>
        </div>
      </div>

      {/* File List and Upload Controls */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Files to Upload ({files.length})</CardTitle>
              <div className="flex gap-2">
                {!uploading && !uploadComplete && (
                  <Button variant="outline" onClick={() => setFiles([])}>
                    Clear All
                  </Button>
                )}
                {!uploading && !uploadComplete && files.length > 0 && (
                  <Button onClick={uploadFiles}>
                    Upload Files
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {files.map((file, index) => (
                <div key={index} className="flex items-center p-3 rounded-md border border-border bg-card">
                  <div className="w-10 h-10 mr-3 rounded bg-secondary flex items-center justify-center overflow-hidden">
                    {file.type.startsWith('image/') && file.preview ? (
                      <img src={file.preview} className="w-full h-full object-cover" />
                    ) : (
                      <div className="text-muted-foreground">
                        {file.type.startsWith('video/') && <span>🎬</span>}
                        {file.type.startsWith('audio/') && <span>🎵</span>}
                        {!file.type.startsWith('image/') && !file.type.startsWith('video/') && !file.type.startsWith('audio/') && <span>📄</span>}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">{formatBytes(file.size)}</p>
                  </div>

                  {uploading && (
                    <div className="w-24 h-1.5 bg-secondary rounded-full overflow-hidden mr-2">
                      <div 
                        className="h-full bg-primary" 
                        style={{ width: `${uploadProgress[file.name] || 0}%` }}
                      />
                    </div>
                  )}
                  
                  {uploadComplete ? (
                    <FileCheck className="h-5 w-5 text-green-500" />
                  ) : !uploading ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  ) : (
                    <span className="text-xs font-medium">{uploadProgress[file.name] || 0}%</span>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      
      {uploadComplete && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 rounded-md bg-green-50 text-green-700 flex items-center"
        >
          <FileCheck className="h-5 w-5 mr-2" />
          <p>Upload complete! <span className="font-medium">Redirecting to dashboard...</span></p>
        </motion.div>
      )}
    </div>
  );
}
