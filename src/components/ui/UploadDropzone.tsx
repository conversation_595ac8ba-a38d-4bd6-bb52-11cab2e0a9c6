'use client'

import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { motion } from "framer-motion";
import { Upload, X, FileCheck, AlertCircle } from "lucide-react";
import { cn, formatBytes, createFileUrl, createThumbnailUrl, extractImageMetadata } from "@/lib/utils";
import { useAssets } from "@/lib/store";
import { useRouter } from "next/navigation";

export function UploadDropzone() {
  const router = useRouter();
  const { addAsset } = useAssets();
  const [files, setFiles] = useState<Array<File & { preview?: string }>>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploading, setUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => 
      Object.assign(file, { preview: URL.createObjectURL(file) })
    );
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
      'video/*': [],
      'audio/*': [],
      'application/pdf': [],
      'application/msword': [],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [],
      'application/vnd.ms-excel': [],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [],
    }
  });

  const removeFile = (index: number) => {
    setFiles(prev => {
      const newFiles = [...prev];
      const file = newFiles[index];
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const uploadFiles = async () => {
    setUploading(true);
    setUploadComplete(false);
    
    // Process files one by one with artificial delay to simulate upload
    for (const file of files) {
      // Create progress updates
      for (let progress = 0; progress <= 100; progress += 10) {
        setUploadProgress(prev => ({ ...prev, [file.name]: progress }));
        await new Promise(r => setTimeout(r, 100)); // Delay to simulate network upload
      }
      
      // Create file and thumbnail URLs
      const fileUrl = await createFileUrl(file);
      const thumbnailUrl = await createThumbnailUrl(file);
      
      try {
        // Get the file type more accurately
        const fileType = file.type.startsWith('image/') ? 'image' :
                        file.type.startsWith('video/') ? 'video' :
                        file.type.startsWith('audio/') ? 'audio' :
                        'document';
        
        // Extract metadata for images, but only if file isn't too large
        let metadata = {};
        if (fileType === 'image' && file.size < 5000000) { // 5MB limit
          console.log(`Extracting metadata for ${file.name}`);
          try {
            metadata = await extractImageMetadata(file);
            console.log(`Extracted metadata for ${file.name}:`, metadata);
          } catch (metadataError) {
            console.error(`Failed to extract metadata for ${file.name}:`, metadataError);
          }
        }
        
        // For very large files, store the file name and size but use placeholder URLs
        let finalFileUrl = fileUrl;
        let finalThumbnailUrl = thumbnailUrl;
        
        // If the file is too large for localStorage (>1MB), use a placeholder
        if (file.size > 1000000) { // 1MB limit for actual file content
          console.warn(`File ${file.name} is too large for localStorage storage. Using placeholder URL.`);
          
          // Use placeholder URLs based on file type
          if (fileType === 'image') {
            finalFileUrl = `https://images.unsplash.com/photo-1533827432537-70133748f5c8?w=800`;
            finalThumbnailUrl = `https://images.unsplash.com/photo-1533827432537-70133748f5c8?w=200`;
          } else if (fileType === 'video') {
            finalFileUrl = 'https://sample-videos.com/video321/mp4/720/big_buck_bunny_720p_1mb.mp4';
            finalThumbnailUrl = 'https://images.unsplash.com/photo-1612538498456-e861df91d4d0?q=80&w=200&auto=format&fit=crop';
          }
        }
        
        // Add the asset to the store
        const newAsset = addAsset({
          title: file.name.split('.')[0], // Use filename without extension as title
          description: '',
          filename: file.name,
          fileUrl: finalFileUrl,
          thumbnailUrl: finalThumbnailUrl,
          type: fileType as any,
          size: file.size,
          metadata,
          tags: [],
        });
        
        console.log(`Added asset to store: ${newAsset.id}`, newAsset);
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        // Continue with next file
      }
    }
    
    setUploading(false);
    setUploadComplete(true);
    
    // Clear files after upload is complete
    setTimeout(() => {
      try {
        // Clean up file previews
        files.forEach(file => {
          if (file.preview) URL.revokeObjectURL(file.preview);
        });
        
        // Reset state
        setFiles([]);
        setUploadProgress({});
        setUploadComplete(false);
        
        // Force a reload instead of a simple redirect to ensure fresh data is loaded
        // Always use router.push for navigation in Next.js apps
        router.push('/');
      } catch (error) {
        console.error("Error during post-upload cleanup:", error);
        // Fallback to window.location if there's an issue with the router
        if (typeof window !== 'undefined') {
          window.location.href = '/';
        }
      }
    }, 2000);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed border-border rounded-lg p-8 transition-colors",
          isDragActive ? "border-primary bg-primary bg-opacity-5" : "hover:border-primary hover:bg-primary/5",
          files.length > 0 && "h-32"
        )}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center text-center">
          <Upload className="h-10 w-10 text-muted-foreground mb-2" />
          <p className="text-lg font-medium">
            {isDragActive ? "Drop the files here..." : "Drag & drop files here, or click to select files"}
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Support for images, videos, audio, and documents
          </p>
        </div>
      </div>

      {files.length > 0 && (
        <div className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Files to upload ({files.length})</h3>
            <div className="flex gap-2">
              {!uploading && !uploadComplete && (
                <button
                  onClick={() => setFiles([])}
                  className="px-3 py-1.5 text-sm rounded-md bg-secondary"
                >
                  Clear All
                </button>
              )}
              {!uploading && !uploadComplete && files.length > 0 && (
                <button
                  onClick={uploadFiles}
                  className="px-3 py-1.5 text-sm rounded-md bg-primary text-primary-foreground"
                >
                  Upload Files
                </button>
              )}
            </div>
          </div>

          <div className="space-y-2">
            {files.map((file, index) => (
              <div key={index} className="flex items-center p-3 rounded-md border border-border bg-card">
                <div className="w-10 h-10 mr-3 rounded bg-secondary flex items-center justify-center overflow-hidden">
                  {file.type.startsWith('image/') && file.preview ? (
                    <img src={file.preview} className="w-full h-full object-cover" />
                  ) : (
                    <div className="text-muted-foreground">
                      {file.type.startsWith('video/') && <span>🎬</span>}
                      {file.type.startsWith('audio/') && <span>🎵</span>}
                      {!file.type.startsWith('image/') && !file.type.startsWith('video/') && !file.type.startsWith('audio/') && <span>📄</span>}
                    </div>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{file.name}</p>
                  <p className="text-xs text-muted-foreground">{formatBytes(file.size)}</p>
                </div>

                {uploading && (
                  <div className="w-24 h-1.5 bg-secondary rounded-full overflow-hidden mr-2">
                    <div 
                      className="h-full bg-primary" 
                      style={{ width: `${uploadProgress[file.name] || 0}%` }}
                    />
                  </div>
                )}
                
                {uploadComplete ? (
                  <FileCheck className="h-5 w-5 text-green-500" />
                ) : !uploading ? (
                  <button
                    onClick={() => removeFile(index)}
                    className="p-1 rounded-md hover:bg-secondary"
                  >
                    <X className="h-4 w-4" />
                  </button>
                ) : (
                  <span className="text-xs font-medium">{uploadProgress[file.name] || 0}%</span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      
      {uploadComplete && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 p-4 rounded-md bg-green-50 text-green-700 flex items-center"
        >
          <FileCheck className="h-5 w-5 mr-2" />
          <p>Upload complete! <span className="font-medium">Redirecting to dashboard to view your assets...</span></p>
        </motion.div>
      )}
      
      {/* Storage warning for large files */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="mt-6 p-4 rounded-md border border-amber-200 bg-amber-50"
      >
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-amber-500 mr-2 mt-0.5" />
          <div>
            <h3 className="font-medium text-amber-800">Storage Information</h3>
            <p className="text-sm text-amber-700 mt-1">
              This app uses browser localStorage which has limited capacity (usually 5-10MB). 
              Large files ({'>'}1MB) will be represented with placeholder images/videos. 
              For small files, full content and metadata will be stored.
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
