'use client'

import React, { useState } from 'react';
import { ChevronRight, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

type MetadataValue = string | number | boolean | Date | null | undefined | Record<string, string | number | boolean | Date | null | undefined> | (string | number | boolean | Date | null | undefined)[];

type MetadataDisplayProps = {
  metadata: Record<string, MetadataValue>;
  depth?: number;
  path?: string;
};

export function MetadataDisplay({ metadata, depth = 0, path = '' }: MetadataDisplayProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  const toggleSection = (key: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Format value based on its type
  const formatValue = (value: MetadataValue): string => {
    if (value === null || value === undefined) return 'null';
    if (typeof value === 'boolean') return value ? 'true' : 'false';
    if (typeof value === 'number') {
      // Format numbers with 5 decimal places maximum
      return Number.isInteger(value) ? value.toString() : value.toFixed(5).replace(/\.?0+$/, '');
    }
    if (value instanceof Date) return value.toISOString();
    if (Array.isArray(value)) return `[Array(${value.length})]`;
    if (typeof value === 'object') return '{Object}';
    return String(value);
  };

  // Only show value if it's not an object or array
  const isComplexValue = (value: MetadataValue): boolean => {
    return value !== null && typeof value === 'object';
  };

  // Check if value is a valid GPS coordinate
  const isGPSCoordinate = (key: string, value: MetadataValue): boolean => {
    return (
      (key.includes('GPS') || key.includes('Latitude') || key.includes('Longitude')) &&
      typeof value === 'number'
    );
  };

  // Format GPS coordinates in a human-readable format
  const formatGPSCoordinate = (value: number): string => {
    const absolute = Math.abs(value);
    const degrees = Math.floor(absolute);
    const minutesDecimal = (absolute - degrees) * 60;
    const minutes = Math.floor(minutesDecimal);
    const seconds = ((minutesDecimal - minutes) * 60).toFixed(2);
    
    return `${degrees}° ${minutes}' ${seconds}" ${value >= 0 ? 'N/E' : 'S/W'}`;
  };

  // For known metadata fields, provide more descriptive labels
  const getMetadataLabel = (key: string): string => {
    const metadataLabels: Record<string, string> = {
      Make: 'Camera Make',
      Model: 'Camera Model',
      LensModel: 'Lens Model',
      FNumber: 'Aperture (f-stop)',
      FocalLength: 'Focal Length',
      ISO: 'ISO Speed',
      ExposureTime: 'Exposure Time',
      Flash: 'Flash',
      WhiteBalance: 'White Balance',
      GPSLatitude: 'GPS Latitude',
      GPSLongitude: 'GPS Longitude',
      GPSAltitude: 'GPS Altitude',
      Software: 'Software Used',
      DateTimeOriginal: 'Date Taken',
      PixelXDimension: 'Width (px)',
      PixelYDimension: 'Height (px)',
    };
    
    return metadataLabels[key] || key;
  };

  return (
    <table className="w-full text-sm">
      <tbody>
        {Object.entries(metadata).map(([key, value]) => {
          const fullPath = path ? `${path}.${key}` : key;
          const isExpanded = expandedSections[fullPath] || false;
          const isComplex = isComplexValue(value);
          
          return (
            <React.Fragment key={fullPath}>
              <tr className={cn(
                "border-b border-border/30 last:border-0",
                depth > 0 && "bg-background/40"
              )}>
                <td 
                  className={cn(
                    "py-2 font-medium",
                    depth > 0 && "pl-3"
                  )}
                  style={{ paddingLeft: `${depth * 16}px` }}
                >
                  {isComplex && (
                    <button 
                      onClick={() => toggleSection(fullPath)}
                      className="inline-flex items-center mr-1 p-0.5 rounded hover:bg-secondary/50"
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </button>
                  )}
                  {getMetadataLabel(key)}
                </td>
                <td className="py-2 text-right text-muted-foreground">
                  {!isComplex && isGPSCoordinate(key, value) 
                    ? formatGPSCoordinate(value as number)
                    : !isComplex ? formatValue(value) : ''}
                </td>
              </tr>
              
              {/* Render nested object if expanded */}
              {isComplex && isExpanded && (
                <tr>
                  <td colSpan={2} className="p-0">
                    {Array.isArray(value) ? (
                      <div className="pl-4 py-2 border-t border-border/30 bg-background/20">
                        {value.map((item, index) => (
                          <div key={index} className="mb-1">
                            {typeof item === 'object' ? (
                              <MetadataDisplay 
                                metadata={{ [`${key}[${index}]`]: item }}
                                depth={depth + 1}
                                path={fullPath}
                              />
                            ) : (
                              <div className="pl-8 py-1">
                                <span className="text-muted-foreground">{formatValue(item)}</span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <MetadataDisplay 
                        metadata={value as Record<string, MetadataValue>} 
                        depth={depth + 1}
                        path={fullPath}
                      />
                    )}
                  </td>
                </tr>
              )}
            </React.Fragment>
          );
        })}
      </tbody>
    </table>
  );
}
