'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, RefreshCw, CheckCircle, AlertCircle, FileImage, Folder, Clock, StopCircle } from 'lucide-react';

interface RefreshProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (results: RefreshResults) => void;
  onCancel?: () => void;
}

interface RefreshResults {
  newAssets: number;
  updatedAssets: number;
  deletedAssets: number;
  totalProcessed: number;
  duration: number;
  detectedFiles: string[];
}

interface ProgressState {
  phase: 'starting' | 'scanning' | 'processing' | 'cleanup' | 'completed' | 'error';
  currentFile?: string;
  processedCount: number;
  totalCount: number;
  detectedFiles: string[];
  foldersScanned: string[];
  message: string;
  startTime: number;
}

export function RefreshProgressModal({ isOpen, onClose, onComplete, onCancel }: RefreshProgressModalProps) {
  const [progress, setProgress] = useState<ProgressState>({
    phase: 'starting',
    processedCount: 0,
    totalCount: 0,
    detectedFiles: [],
    foldersScanned: [],
    message: 'Initializing refresh...',
    startTime: Date.now()
  });

  const [results, setResults] = useState<RefreshResults | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cleanup function to ensure all resources are properly cleaned up
  const cleanup = useCallback(() => {
    console.log('🧹 RefreshProgressModal: Cleaning up resources...');

    // Clear polling interval
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;
    }

    // Abort any ongoing fetch requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    setIsProcessing(false);
    setIsCancelling(false);
  }, []);

  // Force cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  // Cancel the refresh process
  const handleCancel = useCallback(() => {
    console.log('🛑 User requested refresh cancellation');
    setIsCancelling(true);

    setProgress(prev => ({
      ...prev,
      phase: 'error',
      message: 'Refresh cancelled by user'
    }));

    // Cleanup resources
    cleanup();

    // Call onCancel callback if provided
    if (onCancel) {
      onCancel();
    }

    // Close modal after a brief delay
    setTimeout(() => {
      onClose();
    }, 1500);
  }, [cleanup, onCancel, onClose]);

  // Start the refresh process when modal opens
  useEffect(() => {
    if (isOpen && !isProcessing) {
      console.log('🚀 RefreshProgressModal: Starting refresh process...');
      startRefreshProcess();
    }

    // Cleanup when modal closes
    if (!isOpen) {
      cleanup();
      // Reset state for next time
      setProgress({
        phase: 'starting',
        processedCount: 0,
        totalCount: 0,
        detectedFiles: [],
        foldersScanned: [],
        message: 'Initializing refresh...',
        startTime: Date.now()
      });
      setResults(null);
      setIsCancelling(false);
    }
  }, [isOpen, isProcessing, cleanup]);

  const startRefreshProcess = async () => {
    const startTime = Date.now();
    setProgress(prev => ({ ...prev, startTime, phase: 'starting' }));
    setIsProcessing(true);

    // Set up timeout to prevent hanging (30 seconds max)
    const timeoutId = setTimeout(() => {
      console.error('❌ Refresh timeout - forcing cleanup');
      cleanup();
      setProgress(prev => ({
        ...prev,
        phase: 'error',
        message: 'Refresh timed out. Please try again.'
      }));
      setTimeout(() => onClose(), 3000);
    }, 30000);

    try {
      // Phase 1: Start the refresh
      setProgress(prev => ({
        ...prev,
        phase: 'scanning',
        message: '🔍 Starting fast scan for new files...'
      }));

      let pollInterval: NodeJS.Timeout;
      let refreshCompleted = false;

      // Store references for cleanup
      pollIntervalRef.current = pollInterval;
      abortControllerRef.current = new AbortController();

      // Start polling for progress updates immediately
      pollInterval = setInterval(async () => {
        try {
          const statusResponse = await fetch('/api/photos/status');
          if (statusResponse.ok) {
            const statusData = await statusResponse.json();

            if (statusData.success) {
              const isIndexing = statusData.isIndexing;
              const isFastIndexing = statusData.isFastIndexing;
              const indexed = statusData.indexed || 0;
              const total = statusData.total || 0;
              const percentage = statusData.percentage || 0;

              if (isIndexing || isFastIndexing) {
                const phase = isFastIndexing ? 'processing' : 'scanning';

                // Enhanced message with current operation and folder info
                let message = '🔍 Checking for changes...';
                let currentProgress = 0;
                let totalProgress = 100;

                // Use enhanced progress information if available
                if (statusData.currentOperation) {
                  message = `📁 ${statusData.currentOperation}`;
                  if (statusData.currentFolder) {
                    message += ` - ${statusData.currentFolder}`;
                  }
                  if (statusData.foldersToCheck > 0) {
                    message += ` (${statusData.foldersChecked}/${statusData.foldersToCheck} folders)`;
                    // Calculate more accurate progress based on folders
                    currentProgress = statusData.foldersChecked;
                    totalProgress = statusData.foldersToCheck;
                  }
                } else {
                  // Fallback to original message format
                  message = isFastIndexing
                    ? `⚡ Fast indexing in progress... ${Math.round(percentage)}% complete`
                    : `🔍 Scanning for new files... ${indexed.toLocaleString()} processed`;
                  currentProgress = indexed;
                  totalProgress = total;
                }

                setProgress(prev => ({
                  ...prev,
                  phase,
                  processedCount: currentProgress,
                  totalCount: totalProgress,
                  message,
                  // Update folders scanned list
                  foldersScanned: statusData.currentFolder && !prev.foldersScanned.includes(statusData.currentFolder)
                    ? [...prev.foldersScanned, statusData.currentFolder]
                    : prev.foldersScanned,
                  // Update detected changes
                  detectedFiles: statusData.detectedChanges || prev.detectedFiles
                }));
              } else if (!refreshCompleted) {
                // Indexing completed but refresh API might still be running
                setProgress(prev => ({
                  ...prev,
                  phase: 'cleanup',
                  message: 'Finalizing refresh and updating database...'
                }));
              }
            }
          }
        } catch (error) {
          console.error('Error polling status:', error);
        }
      }, 1000); // Poll every second

      // Use the new fast refresh API for lightning-fast results (2-5 seconds instead of 1-2 minutes)
      const response = await fetch('/api/photos/fast-refresh', {
        method: 'POST',
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        signal: abortControllerRef.current?.signal
      });

      if (!response.ok) {
        clearInterval(pollInterval);
        throw new Error(`Refresh failed: ${response.statusText}`);
      }

      // Wait for the main refresh API call to complete
      const data = await response.json();
      refreshCompleted = true;

      // Clear polling interval and timeout
      clearInterval(pollInterval);
      clearTimeout(timeoutId);

      if (data.success) {
        // Check if we have refresh results from the API
        console.log('🔍 Full API response data:', JSON.stringify(data, null, 2));

        if (data.refreshResults) {
          console.log('📊 Received refresh results:', JSON.stringify(data.refreshResults, null, 2));
          await completeRefreshWithResults(startTime, data.refreshResults);
        } else {
          console.log('⚠️ No refresh results in API response, using fallback method');
          await completeRefresh(startTime, data);
        }
      } else {
        throw new Error(data.error || 'Refresh failed');
      }

    } catch (error) {
      console.error('Refresh error:', error);

      // Clear timeout and cleanup
      clearTimeout(timeoutId);
      cleanup();

      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Refresh operation was cancelled or timed out.';
        } else {
          errorMessage = error.message;
        }
      }

      setProgress(prev => ({
        ...prev,
        phase: 'error',
        message: `Error: ${errorMessage}`
      }));

      // Auto-close after showing error for 3 seconds
      setTimeout(() => {
        onClose();
      }, 3000);
    }
  };

  const completeRefreshWithResults = async (startTime: number, refreshResults: any) => {
    console.log('🎯 Processing refresh results in modal:', refreshResults);

    setProgress(prev => ({
      ...prev,
      phase: 'cleanup',
      message: 'Finalizing refresh and updating counts...'
    }));

    // Ensure minimum display time so user can see what happened
    const elapsed = Date.now() - startTime;
    const minDisplayTime = 2000; // 2 seconds minimum
    if (elapsed < minDisplayTime) {
      await new Promise(resolve => setTimeout(resolve, minDisplayTime - elapsed));
    }

    const duration = Date.now() - startTime;
    const finalResults: RefreshResults = {
      newAssets: refreshResults.newAssets || 0,
      updatedAssets: refreshResults.updatedAssets || 0,
      deletedAssets: refreshResults.deletedAssets || 0,
      totalProcessed: refreshResults.foldersChecked || 0,
      duration,
      detectedFiles: []
    };

    console.log('📊 Final results for modal display:', finalResults);

    setResults(finalResults);

    // Create a more detailed message based on what actually happened
    let completionMessage = 'Refresh completed!';
    const changes = [];
    if (finalResults.newAssets > 0) changes.push(`${finalResults.newAssets} new`);
    if (finalResults.updatedAssets > 0) changes.push(`${finalResults.updatedAssets} updated`);
    if (finalResults.deletedAssets > 0) changes.push(`${finalResults.deletedAssets} deleted`);

    if (changes.length > 0) {
      completionMessage = `Refresh completed! Found ${changes.join(', ')} assets`;
    } else {
      completionMessage = 'Refresh completed - No changes detected';
    }

    setProgress(prev => ({
      ...prev,
      phase: 'completed',
      message: completionMessage
    }));

    console.log('✅ Modal completion message:', completionMessage);

    // Call onComplete immediately to trigger UI refresh
    console.log('🔄 Calling onComplete immediately to refresh UI...');
    onComplete(finalResults);

    // Auto-close after showing results for a shorter moment (3 seconds)
    setTimeout(() => {
      onClose();
    }, 3000);
  };

  const completeRefresh = async (startTime: number, data?: any) => {
    setProgress(prev => ({
      ...prev,
      phase: 'cleanup',
      message: 'Finalizing refresh and updating counts...'
    }));

    // Get final status to calculate changes
    try {
      const finalStatusResponse = await fetch('/api/photos/status');
      if (finalStatusResponse.ok) {
        const finalStatus = await finalStatusResponse.json();
        if (finalStatus.success) {
          setProgress(prev => ({
            ...prev,
            processedCount: finalStatus.indexed || prev.processedCount,
            totalCount: finalStatus.total || prev.totalCount
          }));
        }
      }
    } catch (error) {
      console.error('Error getting final status:', error);
    }

    // Brief pause to show final status
    await new Promise(resolve => setTimeout(resolve, 1500));

    const duration = Date.now() - startTime;
    const refreshResults: RefreshResults = {
      newAssets: data?.assets?.length || 0, // Number of assets returned in the refresh
      updatedAssets: Math.max(0, progress.processedCount - (data?.assets?.length || 0)),
      deletedAssets: 0, // This would need to be tracked separately
      totalProcessed: progress.processedCount,
      duration,
      detectedFiles: progress.detectedFiles
    };

    setResults(refreshResults);
    setProgress(prev => ({
      ...prev,
      phase: 'completed',
      message: 'Refresh completed successfully!'
    }));

    // Call onComplete immediately to trigger UI refresh
    console.log('🔄 Calling onComplete immediately to refresh UI...');
    onComplete(refreshResults);

    // Auto-close after showing results for a shorter moment (3 seconds)
    setTimeout(() => {
      onClose();
    }, 3000);
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  const getProgressPercentage = () => {
    if (progress.totalCount === 0) return 0;
    return Math.min(100, (progress.processedCount / progress.totalCount) * 100);
  };

  const getPhaseIcon = () => {
    switch (progress.phase) {
      case 'starting':
      case 'scanning':
      case 'processing':
        return <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />;
      case 'cleanup':
        return <RefreshCw className="h-5 w-5 animate-spin text-orange-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />;
    }
  };

  const getPhaseColor = () => {
    switch (progress.phase) {
      case 'starting':
      case 'scanning':
      case 'processing':
        return 'bg-blue-500';
      case 'cleanup':
        return 'bg-orange-500';
      case 'completed':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="bg-background border border-border rounded-lg shadow-xl max-w-2xl w-full mx-4 overflow-hidden"
        >
          {/* Header */}
          <div className="flex justify-between items-center border-b border-border p-4">
            <div className="flex items-center space-x-3">
              {getPhaseIcon()}
              <h2 className="text-xl font-semibold">Asset Refresh</h2>
            </div>
            <div className="flex items-center space-x-2">
              {/* Stop button during processing */}
              {(progress.phase === 'scanning' || progress.phase === 'processing' || progress.phase === 'cleanup') && !isCancelling && (
                <button
                  onClick={handleCancel}
                  className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                  title="Stop refresh"
                >
                  <StopCircle className="h-4 w-4 mr-1" />
                  Stop
                </button>
              )}
              {/* Close button when completed/error */}
              {(progress.phase === 'completed' || progress.phase === 'error') && (
                <button
                  onClick={onClose}
                  className="p-1 rounded-md hover:bg-secondary transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="font-medium">{progress.message}</span>
                <span className="text-muted-foreground">
                  {formatDuration(Date.now() - progress.startTime)}
                </span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2 overflow-hidden">
                <motion.div
                  className={`h-full ${getPhaseColor()} transition-all duration-300`}
                  initial={{ width: 0 }}
                  animate={{ width: `${getProgressPercentage()}%` }}
                />
              </div>
              {progress.totalCount > 0 && (
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{progress.processedCount.toLocaleString()} processed</span>
                  <span>{Math.round(getProgressPercentage())}% complete</span>
                </div>
              )}
            </div>

            {/* Current File */}
            {progress.currentFile && (
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <FileImage className="h-4 w-4" />
                <span className="truncate">{progress.currentFile}</span>
              </div>
            )}

            {/* Folders Scanned */}
            {progress.foldersScanned.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center">
                  <Folder className="h-4 w-4 mr-2" />
                  Folders Scanned ({progress.foldersScanned.length})
                </h4>
                <div className="max-h-24 overflow-y-auto space-y-1">
                  {progress.foldersScanned.slice(-5).map((folder, index) => (
                    <div key={index} className="text-xs text-muted-foreground truncate">
                      📁 {folder}
                    </div>
                  ))}
                  {progress.foldersScanned.length > 5 && (
                    <div className="text-xs text-muted-foreground">
                      ... and {progress.foldersScanned.length - 5} more folders
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Detected Changes (Real-time) */}
            {progress.detectedFiles.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center">
                  <FileImage className="h-4 w-4 mr-2" />
                  Changes Detected ({progress.detectedFiles.length})
                </h4>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {progress.detectedFiles.slice(-10).map((change, index) => (
                    <div key={index} className="text-xs text-muted-foreground truncate">
                      {change.startsWith('+') && <span className="text-green-600">➕ {change}</span>}
                      {change.startsWith('-') && <span className="text-red-600">➖ {change}</span>}
                      {change.startsWith('~') && <span className="text-blue-600">🔄 {change}</span>}
                      {!change.startsWith('+') && !change.startsWith('-') && !change.startsWith('~') && (
                        <span className="text-gray-600">ℹ️ {change}</span>
                      )}
                    </div>
                  ))}
                  {progress.detectedFiles.length > 10 && (
                    <div className="text-xs text-muted-foreground">
                      ... and {progress.detectedFiles.length - 10} more changes
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Results Summary (when completed) */}
            {results && progress.phase === 'completed' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`${
                  (results.newAssets > 0 || results.updatedAssets > 0 || results.deletedAssets > 0)
                    ? "bg-blue-50 border border-blue-200"
                    : "bg-green-50 border border-green-200"
                } rounded-lg p-4 space-y-3`}
              >
                <div className="flex items-center space-x-2">
                  <CheckCircle className={`h-5 w-5 ${
                    (results.newAssets > 0 || results.updatedAssets > 0 || results.deletedAssets > 0)
                      ? "text-blue-600"
                      : "text-green-600"
                  }`} />
                  <h3 className={`font-medium ${
                    (results.newAssets > 0 || results.updatedAssets > 0 || results.deletedAssets > 0)
                      ? "text-blue-800"
                      : "text-green-800"
                  }`}>Refresh Complete!</h3>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className={`${
                        (results.newAssets > 0 || results.updatedAssets > 0 || results.deletedAssets > 0)
                          ? "text-blue-700"
                          : "text-green-700"
                      }`}>New assets:</span>
                      <span className={`font-medium ${
                        results.newAssets > 0 ? "text-blue-800 font-bold" :
                        (results.newAssets === 0 && (results.updatedAssets > 0 || results.deletedAssets > 0)) ? "text-blue-800" : "text-green-800"
                      }`}>{results.newAssets}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className={`${
                        (results.newAssets > 0 || results.updatedAssets > 0 || results.deletedAssets > 0)
                          ? "text-blue-700"
                          : "text-green-700"
                      }`}>Updated:</span>
                      <span className={`font-medium ${
                        results.updatedAssets > 0 ? "text-blue-800 font-bold" :
                        (results.updatedAssets === 0 && (results.newAssets > 0 || results.deletedAssets > 0)) ? "text-blue-800" : "text-green-800"
                      }`}>{results.updatedAssets}</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className={`${
                        (results.newAssets > 0 || results.updatedAssets > 0 || results.deletedAssets > 0)
                          ? "text-blue-700"
                          : "text-green-700"
                      }`}>Removed:</span>
                      <span className={`font-medium ${
                        results.deletedAssets > 0 ? "text-red-800 font-bold" :
                        (results.deletedAssets === 0 && (results.newAssets > 0 || results.updatedAssets > 0)) ? "text-blue-800" : "text-green-800"
                      }`}>{results.deletedAssets}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className={`${
                        (results.newAssets > 0 || results.updatedAssets > 0 || results.deletedAssets > 0)
                          ? "text-blue-700"
                          : "text-green-700"
                      }`}>Duration:</span>
                      <span className={`font-medium ${
                        (results.newAssets > 0 || results.updatedAssets > 0 || results.deletedAssets > 0)
                          ? "text-blue-800"
                          : "text-green-800"
                      }`}>{formatDuration(results.duration)}</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Error State */}
            {progress.phase === 'error' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-50 border border-red-200 rounded-lg p-4"
              >
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <h3 className="font-medium text-red-800">Refresh Failed</h3>
                </div>
                <p className="text-sm text-red-700 mt-2">{progress.message}</p>
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
