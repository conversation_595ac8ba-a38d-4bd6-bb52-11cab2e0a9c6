'use client'

import React from "react";
import { Asset } from "@/lib/types";
import { formatBytes } from "@/lib/utils";
import { File, FileVideo, FileAudio, FileText, HardDrive, Eye } from "lucide-react";

interface AssetListProps {
  assets: Asset[];
  onAssetClick: (asset: Asset) => void;
  loading?: boolean;
  showSkeleton?: boolean;
}

export function AssetList({ assets, onAssetClick, loading = false, showSkeleton = false }: AssetListProps) {
  // Show skeleton when explicitly requested or when loading with no assets
  if (showSkeleton || (loading && assets.length === 0)) {
    return <AssetListSkeleton count={10} />;
  }

  if (assets.length === 0) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">No assets found</p>
      </div>
    );
  }

  const getAssetIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <FileVideo className="h-5 w-5 text-blue-500" />;
      case 'audio':
        return <FileAudio className="h-5 w-5 text-green-500" />;
      case 'document':
        return <FileText className="h-5 w-5 text-orange-500" />;
      default:
        return <File className="h-5 w-5 text-purple-500" />;
    }
  };



  return (
    <div className="space-y-2">
      {/* Header */}
      <div className="grid grid-cols-12 gap-4 px-4 py-2 text-sm font-medium text-muted-foreground border-b border-border">
        <div className="col-span-1"></div>
        <div className="col-span-4">Name</div>
        <div className="col-span-4">Folder</div>
        <div className="col-span-1">Type</div>
        <div className="col-span-1">Size</div>
        <div className="col-span-1">Actions</div>
      </div>

      {/* Asset List */}
      <div className="space-y-1">
        {assets.map((asset) => (
          <div
            key={asset.id}
            onClick={() => onAssetClick(asset)}
            className="grid grid-cols-12 gap-4 px-4 py-4 hover:bg-secondary/50 cursor-pointer rounded-md transition-colors group"
          >
            {/* Thumbnail/Icon */}
            <div className="col-span-1 flex items-center">
              {asset.type === 'image' ? (
                <div className="w-16 h-16 rounded-md overflow-hidden bg-secondary shadow-sm">
                  <img
                    src={asset.thumbnailUrl}
                    alt={asset.filename}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  <div className="hidden w-full h-full flex items-center justify-center">
                    {getAssetIcon(asset.type)}
                  </div>
                </div>
              ) : (
                <div className="w-16 h-16 rounded-md bg-secondary flex items-center justify-center shadow-sm">
                  {getAssetIcon(asset.type)}
                </div>
              )}
            </div>

            {/* Name */}
            <div className="col-span-4 flex items-center min-w-0">
              <div className="truncate">
                <p className="font-medium text-sm truncate">{asset.filename}</p>
              </div>
            </div>

            {/* Folder */}
            <div className="col-span-4 flex items-center min-w-0">
              <div className="truncate">
                <p className="text-sm text-muted-foreground truncate">
                  {asset.filePath.split('/').slice(0, -1).join('/')}
                </p>
              </div>
            </div>

            {/* Type */}
            <div className="col-span-1 flex items-center">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary">
                {asset.type.charAt(0).toUpperCase() + asset.type.slice(1)}
              </span>
            </div>

            {/* Size */}
            <div className="col-span-1 flex items-center">
              <div className="flex items-center text-sm text-muted-foreground">
                <HardDrive className="h-4 w-4 mr-1" />
                {formatBytes(asset.size)}
              </div>
            </div>

            {/* Actions */}
            <div className="col-span-1 flex items-center">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onAssetClick(asset);
                }}
                className="opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-md hover:bg-secondary"
              >
                <Eye className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Skeleton component for loading state
function AssetListSkeleton({ count = 10 }: { count?: number }) {
  return (
    <div className="space-y-2">
      {/* Header */}
      <div className="grid grid-cols-12 gap-4 px-4 py-2 text-sm font-medium text-muted-foreground border-b border-border">
        <div className="col-span-1"></div>
        <div className="col-span-4">Name</div>
        <div className="col-span-4">Folder</div>
        <div className="col-span-1">Type</div>
        <div className="col-span-1">Size</div>
        <div className="col-span-1">Actions</div>
      </div>

      {/* Skeleton Rows */}
      <div className="space-y-1">
        {Array.from({ length: count }).map((_, i) => (
          <div key={i} className="grid grid-cols-12 gap-4 px-4 py-4 rounded-md">
            <div className="col-span-1 flex items-center">
              <div className="w-16 h-16 rounded-md bg-muted animate-pulse"></div>
            </div>
            <div className="col-span-4 flex items-center">
              <div className="h-4 bg-muted rounded animate-pulse w-3/4"></div>
            </div>
            <div className="col-span-4 flex items-center">
              <div className="h-4 bg-muted rounded animate-pulse w-2/3"></div>
            </div>
            <div className="col-span-1 flex items-center">
              <div className="h-6 bg-muted rounded-full animate-pulse w-12"></div>
            </div>
            <div className="col-span-1 flex items-center">
              <div className="h-4 bg-muted rounded animate-pulse w-16"></div>
            </div>
            <div className="col-span-1 flex items-center">
              <div className="h-6 w-6 bg-muted rounded animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
