'use client'

import React, { useState } from "react";
import { Collection } from "@/lib/types";
import { useCollections } from "@/lib/store";
import { formatDate } from "@/lib/utils";
import { Folder, FolderPlus, Edit, Trash, ChevronRight } from "lucide-react";
import { motion } from "framer-motion";

export function CollectionsList() {
  const { collections, addCollection, updateCollection, deleteCollection } = useCollections();
  const [isAddingCollection, setIsAddingCollection] = useState(false);
  const [editingCollection, setEditingCollection] = useState<string | null>(null);
  const [newCollection, setNewCollection] = useState({ name: "", description: "" });
  
  const handleAddCollection = () => {
    const now = new Date().toISOString();
    addCollection({
      name: newCollection.name,
      description: newCollection.description,
      assetCount: 0,
      createdAt: now,
      updatedAt: now,
    });
    setNewCollection({ name: "", description: "" });
    setIsAddingCollection(false);
  };
  
  const handleUpdateCollection = (id: string) => {
    const collection = collections.find(c => c.id === id);
    if (collection) {
      updateCollection(id, {
        name: newCollection.name || collection.name,
        description: newCollection.description || collection.description
      });
    }
    setEditingCollection(null);
    setNewCollection({ name: "", description: "" });
  };
  
  const startEditing = (collection: Collection) => {
    setEditingCollection(collection.id);
    setNewCollection({
      name: collection.name,
      description: collection.description || ""
    });
  };
  
  if (collections.length === 0 && !isAddingCollection) {
    return (
      <div className="text-center p-8">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary bg-opacity-10 mb-4">
          <Folder className="h-8 w-8 text-primary" />
        </div>
        <h2 className="text-xl font-semibold mb-2">No collections yet</h2>
        <p className="text-muted-foreground mb-4">Create collections to organize your assets</p>
        <button
          onClick={() => setIsAddingCollection(true)}
          className="inline-flex items-center px-4 py-2 rounded-md bg-primary text-primary-foreground"
        >
          <FolderPlus className="h-4 w-4 mr-2" />
          Create Collection
        </button>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Collections</h2>
        {!isAddingCollection && (
          <button
            onClick={() => setIsAddingCollection(true)}
            className="inline-flex items-center px-3 py-1.5 rounded-md bg-primary text-primary-foreground text-sm"
          >
            <FolderPlus className="h-4 w-4 mr-1.5" />
            New Collection
          </button>
        )}
      </div>
      
      {isAddingCollection && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          className="border border-border rounded-md p-4 bg-card"
        >
          <h3 className="text-lg font-medium mb-3">Create New Collection</h3>
          <div className="space-y-3">
            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-1">
                Name
              </label>
              <input
                id="name"
                type="text"
                value={newCollection.name}
                onChange={(e) => setNewCollection(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
                placeholder="Collection name"
              />
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium mb-1">
                Description (optional)
              </label>
              <textarea
                id="description"
                value={newCollection.description}
                onChange={(e) => setNewCollection(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
                placeholder="Collection description"
                rows={3}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setIsAddingCollection(false);
                  setNewCollection({ name: "", description: "" });
                }}
                className="px-3 py-1.5 rounded-md border border-border text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleAddCollection}
                disabled={!newCollection.name.trim()}
                className="px-3 py-1.5 rounded-md bg-primary text-primary-foreground text-sm disabled:opacity-50"
              >
                Create
              </button>
            </div>
          </div>
        </motion.div>
      )}
      
      <div className="space-y-2">
        {collections.map((collection) => (
          <div
            key={collection.id}
            className="border border-border rounded-md bg-card overflow-hidden"
          >
            {editingCollection === collection.id ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="p-4 space-y-3"
              >
                <div>
                  <label htmlFor="edit-name" className="block text-sm font-medium mb-1">
                    Name
                  </label>
                  <input
                    id="edit-name"
                    type="text"
                    value={newCollection.name}
                    onChange={(e) => setNewCollection(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
                  />
                </div>
                <div>
                  <label htmlFor="edit-description" className="block text-sm font-medium mb-1">
                    Description
                  </label>
                  <textarea
                    id="edit-description"
                    value={newCollection.description}
                    onChange={(e) => setNewCollection(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
                    rows={3}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <button
                    onClick={() => {
                      setEditingCollection(null);
                      setNewCollection({ name: "", description: "" });
                    }}
                    className="px-3 py-1.5 rounded-md border border-border text-sm"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleUpdateCollection(collection.id)}
                    disabled={!newCollection.name.trim()}
                    className="px-3 py-1.5 rounded-md bg-primary text-primary-foreground text-sm disabled:opacity-50"
                  >
                    Save
                  </button>
                </div>
              </motion.div>
            ) : (
              <div className="flex items-center p-4">
                <div className="flex-shrink-0 mr-3">
                  <Folder className="h-8 w-8 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-base font-medium">{collection.name}</h3>
                  {collection.description && (
                    <p className="text-sm text-muted-foreground truncate">{collection.description}</p>
                  )}
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-xs text-muted-foreground">
                      {collection.assetCount} assets
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Created: {formatDate(collection.createdAt)}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2 ml-3">
                  <button
                    onClick={() => startEditing(collection)}
                    className="p-1.5 rounded-md hover:bg-secondary"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => deleteCollection(collection.id)}
                    className="p-1.5 rounded-md hover:bg-secondary text-destructive hover:text-destructive"
                  >
                    <Trash className="h-4 w-4" />
                  </button>
                  <button className="p-1.5 rounded-md hover:bg-secondary">
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
