'use client';

import React, { useEffect } from "react";

export function ClientErrorHandler({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  useEffect(() => {
    // Add global error handling for client-side errors
    const handleGlobalError = (event: Event) => {
      const errorEvent = event as ErrorEvent;
      console.error('Global error caught:', {
        message: errorEvent.message,
        filename: errorEvent.filename,
        lineno: errorEvent.lineno,
        colno: errorEvent.colno,
        error: errorEvent.error,
        stack: errorEvent.error?.stack
      });

      // Check if error is related to images, chunks, or network issues
      if (errorEvent.message && (
          errorEvent.message.includes('Loading chunk') ||
          errorEvent.message.includes('Loading CSS chunk') ||
          errorEvent.message.includes('image') ||
          errorEvent.message.includes('Failed to fetch') ||
          errorEvent.message.includes('ChunkLoadError') ||
          errorEvent.message.includes('NetworkError') ||
          errorEvent.message.includes('fetch')
        )) {
        console.log('Non-critical error detected, preventing app crash:', errorEvent.message);
        event.preventDefault();

        // Show error message in asset grid if it exists
        const errorElement = document.getElementById('asset-grid-error');
        if (errorElement) {
          errorElement.classList.remove('hidden');
        }
      }
    };

    const handleRejection = (event: Event) => {
      const rejectionEvent = event as PromiseRejectionEvent;
      console.error('Unhandled promise rejection:', {
        reason: rejectionEvent.reason,
        stack: rejectionEvent.reason?.stack
      });

      // Check if it's a non-critical rejection
      const reason = String(rejectionEvent.reason);
      if (reason.includes('fetch') ||
          reason.includes('network') ||
          reason.includes('image') ||
          reason.includes('chunk') ||
          reason.includes('timeout')) {
        console.log('Non-critical promise rejection, preventing app crash:', reason);
        event.preventDefault();
      }
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleRejection);

    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleRejection);
    };
  }, []);

  return <>{children}</>;
} 