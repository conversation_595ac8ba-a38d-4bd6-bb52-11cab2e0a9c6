'use client'

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { Calendar, ChevronDown, ChevronRight } from "lucide-react";
import { useAssets } from "@/lib/store";
import { ClientOnly } from "./ClientOnly";

const MONTHS = [
  { name: 'January', short: 'Jan', number: '1' },
  { name: 'February', short: 'Feb', number: '2' },
  { name: 'March', short: 'Mar', number: '3' },
  { name: 'April', short: 'Apr', number: '4' },
  { name: 'May', short: 'May', number: '5' },
  { name: 'June', short: 'Jun', number: '6' },
  { name: 'July', short: 'Jul', number: '7' },
  { name: 'August', short: 'Aug', number: '8' },
  { name: 'September', short: 'Sep', number: '9' },
  { name: 'October', short: 'Oct', number: '10' },
  { name: 'November', short: 'Nov', number: '11' },
  { name: 'December', short: 'Dec', number: '12' }
];

interface YearMonthFilterProps {
  isExpanded: boolean;
}

function YearMonthFilterContent({ isExpanded }: YearMonthFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setFilters, dateFilters } = useAssets();
  
  const [availableYears, setAvailableYears] = useState<string[]>([]);
  const [expandedYears, setExpandedYears] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);

  // Get current filters - prioritize store over URL
  const currentYear = dateFilters.year || searchParams.get('year');
  const currentMonth = dateFilters.month || searchParams.get('month');

  console.log('🔍 YearMonthFilter current state:', {
    'dateFilters.year': dateFilters.year,
    'searchParams.year': searchParams.get('year'),
    'currentYear': currentYear,
    'currentMonth': currentMonth
  });

  // Fetch available years from the API
  const fetchYears = async () => {
    console.log('🔍 Starting to fetch available years...');
    setLoading(true);

    try {
      const response = await fetch('/api/photos/years', {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      console.log('📡 Years API response status:', response.status);

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📅 Years API response data:', data);

      if (data.success && data.years && Array.isArray(data.years)) {
        // Sort years in descending order (newest first)
        const sortedYears = data.years.sort((a: string, b: string) => parseInt(b) - parseInt(a));
        console.log('✅ Setting available years:', sortedYears);
        setAvailableYears(sortedYears);

        // Auto-expand current year if selected
        if (currentYear && sortedYears.includes(currentYear)) {
          console.log('🔧 Auto-expanding current year:', currentYear);
          setExpandedYears(new Set([currentYear]));
        }
      } else {
        console.warn('⚠️ Invalid API response structure:', data);
        setAvailableYears([]);
      }
    } catch (error) {
      console.error('❌ Error fetching years:', error);
      // Fallback to recent years
      const currentYearNum = new Date().getFullYear();
      const fallbackYears = Array.from({ length: 5 }, (_, i) => String(currentYearNum - i));
      console.log('🔄 Using fallback years:', fallbackYears);
      setAvailableYears(fallbackYears);
    } finally {
      console.log('🏁 Finishing years fetch, setting loading to false');
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔄 YearMonthFilter: Component mounted, fetching years...');
    fetchYears();
  }, []); // Only run once on mount

  // Sync URL parameters with store on initial load
  useEffect(() => {
    const urlYear = searchParams.get('year');
    const urlMonth = searchParams.get('month');

    // Only sync if URL has parameters but store doesn't
    if ((urlYear || urlMonth) && (!dateFilters.year && !dateFilters.month)) {
      console.log('🔄 Syncing URL parameters with store on initial load:', { urlYear, urlMonth });
      const filters: { year?: string; month?: string } = {};
      if (urlYear) filters.year = urlYear;
      if (urlMonth) filters.month = urlMonth;
      setFilters(filters);
    }
  }, [searchParams, dateFilters, setFilters]);

  // Separate effect to handle year expansion when currentYear changes
  useEffect(() => {
    if (currentYear && availableYears.length > 0 && availableYears.includes(currentYear)) {
      console.log('🔧 Expanding year due to currentYear change:', currentYear);
      setExpandedYears(prev => new Set([...prev, currentYear]));
    }
  }, [currentYear, availableYears]);

  const handleYearClick = (year: string) => {
    console.log('🗓️ ===== YEAR CLICK DEBUG =====');
    console.log('🗓️ Year clicked:', year);
    console.log('🗓️ Current year:', currentYear);
    console.log('🗓️ Current dateFilters:', dateFilters);

    // ALWAYS apply the filter - no complex logic
    console.log('🗓️ Applying year filter:', year);
    applyFilter({ year });
    setExpandedYears(prev => new Set([...prev, year]));

    console.log('🗓️ ===== END YEAR CLICK DEBUG =====');
  };

  const handleMonthClick = (year: string, month: string) => {
    console.log('🗓️ Month clicked:', month, 'for year:', year, 'Current:', { currentYear, currentMonth });
    if (currentYear === year && currentMonth === month) {
      // If clicking the same month, go back to year-only filter
      console.log('🗓️ Going back to year-only filter (same month clicked)');
      applyFilter({ year });
    } else {
      // Set year and month filter
      console.log('🗓️ Setting year and month filter:', { year, month });
      applyFilter({ year, month });
    }
  };

  const toggleYearExpansion = (year: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setExpandedYears(prev => {
      const newSet = new Set(prev);
      if (newSet.has(year)) {
        newSet.delete(year);
      } else {
        newSet.add(year);
      }
      return newSet;
    });
  };

  const applyFilter = (filters: { year?: string; month?: string }) => {
    console.log('🔧 Applying filters:', filters);

    // Update the store first
    setFilters(filters);

    // Update URL for bookmarking (simple approach)
    const params = new URLSearchParams();
    if (filters.year) params.set('year', filters.year);
    if (filters.month) params.set('month', filters.month);

    const newUrl = params.toString() ? `/?${params.toString()}` : '/';
    router.replace(newUrl);

    console.log('🔧 Filters applied:', filters);
  };

  const clearFilters = () => {
    console.log('🧹 Clearing all filters');

    // Clear store filters - this will trigger the useEffect in DashboardView
    setFilters({});

    // Clear URL parameters
    const params = new URLSearchParams(searchParams.toString());
    params.delete('year');
    params.delete('month');

    const newUrl = params.toString() ? `/?${params.toString()}` : '/';
    console.log('🧹 Updating URL without reload:', newUrl);

    // Use replace to update URL without page reload
    router.replace(newUrl);

    // Collapse all years
    setExpandedYears(new Set());
  };

  if (loading) {
    console.log('🔄 YearMonthFilter: Loading state, isExpanded:', isExpanded);
    return (
      <div className="space-y-1">
        {isExpanded && (
          <div className="text-xs text-sidebar-foreground/60 px-2 py-1">
            Loading years... ({availableYears.length} loaded)
          </div>
        )}
        {!isExpanded && (
          <div className="flex items-center justify-center py-2">
            <Calendar className="h-4 w-4 text-sidebar-foreground/60" />
          </div>
        )}
      </div>
    );
  }

  console.log('📊 YearMonthFilter: Rendering with', {
    availableYears: availableYears.length,
    isExpanded,
    currentYear,
    currentMonth,
    'dateFilters': dateFilters,
    'searchParams.year': searchParams.get('year')
  });

  if (!isExpanded) {
    // Collapsed view - show calendar icon with click to cycle through years
    return (
      <div className="flex items-center justify-center py-2">
        <button
          onClick={() => {
            // Cycle through available years when sidebar is collapsed
            if (availableYears.length > 0) {
              const currentIndex = currentYear ? availableYears.indexOf(currentYear) : -1;
              const nextIndex = (currentIndex + 1) % availableYears.length;
              const nextYear = availableYears[nextIndex];
              console.log('🗓️ Collapsed sidebar - cycling to year:', nextYear);
              applyFilter({ year: nextYear });
            }
          }}
          className="p-1 hover:bg-sidebar-accent rounded"
          title={currentYear ? `Current: ${currentYear} (click to cycle)` : 'Click to select year'}
        >
          <Calendar className={cn(
            "h-4 w-4",
            (currentYear || currentMonth) ? "text-sidebar-primary" : "text-sidebar-foreground/60"
          )} />
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-1">
      {/* Clear filters button */}
      {(currentYear || currentMonth) && (
        <button
          onClick={clearFilters}
          className="w-full text-xs text-sidebar-primary hover:text-sidebar-primary/80 px-2 py-1 text-left"
        >
          Clear Date Filter
        </button>
      )}

      {/* Years list */}
      <div className="space-y-1 max-h-64 overflow-y-auto">
        {availableYears.length > 0 ? (
          availableYears.map(year => {
            const isYearSelected = currentYear === year;
            const isYearExpanded = expandedYears.has(year);

            return (
              <div key={year}>
                {/* Year item */}
                <div
                  className={cn(
                    "flex items-center py-1 px-2 rounded-md cursor-pointer group text-sm",
                    isYearSelected && !currentMonth
                      ? "bg-sidebar-accent text-sidebar-accent-foreground"
                      : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
                  )}
                  onClick={() => handleYearClick(year)}
                >
                  <button
                    onClick={(e) => toggleYearExpansion(year, e)}
                    className="mr-1 p-0.5 hover:bg-sidebar-accent rounded"
                  >
                    <ChevronRight className={cn(
                      "h-3 w-3 transition-transform",
                      isYearExpanded ? "rotate-90" : ""
                    )} />
                  </button>
                  <Calendar className="h-3 w-3 mr-2" />
                  <span>{year}</span>
                </div>

                {/* Months for expanded year */}
                {isYearExpanded && (
                  <div className="ml-6 space-y-0.5 mt-1">
                    {MONTHS.map(month => {
                      const isMonthSelected = currentYear === year && currentMonth === month.number;

                      return (
                        <div
                          key={month.number}
                          className={cn(
                            "py-1 px-2 rounded-md cursor-pointer text-xs",
                            isMonthSelected
                              ? "bg-sidebar-accent text-sidebar-accent-foreground"
                              : "hover:bg-sidebar-accent/50 text-sidebar-foreground/80"
                          )}
                          onClick={() => handleMonthClick(year, month.number)}
                        >
                          {month.short}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })
        ) : (
          <div className="text-xs text-sidebar-foreground/60 px-2 py-1">
            <div>No years found</div>
            <button
              onClick={() => {
                console.log('🔄 Refreshing years without page reload');
                fetchYears();
              }}
              className="text-xs text-sidebar-primary hover:underline mt-1"
            >
              Refresh
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export function YearMonthFilter({ isExpanded }: YearMonthFilterProps) {
  return (
    <ClientOnly
      fallback={
        <div className="space-y-1">
          {isExpanded && (
            <div className="text-xs text-sidebar-foreground/60 px-2 py-1">
              Loading filter...
            </div>
          )}
          {!isExpanded && (
            <div className="flex items-center justify-center py-2">
              <Calendar className="h-4 w-4 text-sidebar-foreground/60" />
            </div>
          )}
        </div>
      }
    >
      <YearMonthFilterContent isExpanded={isExpanded} />
    </ClientOnly>
  );
}
