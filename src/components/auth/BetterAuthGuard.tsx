'use client'

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { useSession } from '@/lib/auth-client';

interface BetterAuthGuardProps {
  children: React.ReactNode;
}

export function BetterAuthGuard({ children }: BetterAuthGuardProps) {
  const { data: session, isPending } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  // Skip auth check for login page
  if (pathname === '/login') {
    return <>{children}</>;
  }

  // Show loading while checking session
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!session) {
    router.replace('/login');
    return null;
  }

  // If authenticated, render children
  return <>{children}</>;
}
