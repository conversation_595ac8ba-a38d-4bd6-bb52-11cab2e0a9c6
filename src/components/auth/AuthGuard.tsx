'use client'

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    console.log('🔐 AuthGuard: Checking authentication for path:', pathname);

    // Skip auth check for login page
    if (pathname === '/login') {
      console.log('🔐 AuthGuard: Login page detected, allowing access');
      setIsAuthenticated(true);
      return;
    }

    const checkAuth = () => {
      try {
        console.log('🔐 AuthGuard: Checking localStorage...');
        const authStatus = localStorage.getItem('isAuthenticated');
        const user = localStorage.getItem('user');

        console.log('🔐 AuthGuard: Auth status:', authStatus);
        console.log('🔐 AuthGuard: User data exists:', !!user);

        if (authStatus === 'true' && user) {
          console.log('🔐 AuthGuard: User is authenticated, allowing access');
          setIsAuthenticated(true);
        } else {
          console.log('🔐 AuthGuard: User not authenticated, redirecting to login');
          setIsAuthenticated(false);
          window.location.href = '/login';
        }
      } catch (error) {
        console.error('🔐 AuthGuard: Error checking auth:', error);
        setIsAuthenticated(false);
        window.location.href = '/login';
      }
    };

    // Check immediately
    checkAuth();
  }, [pathname]);

  // Show loading while checking authentication
  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  // If not authenticated, don't render children (redirect will happen)
  if (!isAuthenticated) {
    return null;
  }

  // If authenticated, render children
  return <>{children}</>;
}
