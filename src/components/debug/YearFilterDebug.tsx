'use client'

import React, { useState, useEffect } from 'react';

export function YearFilterDebug() {
  const [years, setYears] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchYears = async () => {
      try {
        console.log('🔍 Debug: Fetching years...');
        const response = await fetch('/api/photos/years');
        const data = await response.json();
        
        console.log('📅 Debug: Years response:', data);
        
        if (data.success && data.years) {
          setYears(data.years);
        } else {
          setError('No years found');
        }
      } catch (err) {
        console.error('❌ Debug: Error fetching years:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchYears();
  }, []);

  if (loading) {
    return <div className="p-4 bg-blue-100 text-blue-800">Loading years...</div>;
  }

  if (error) {
    return <div className="p-4 bg-red-100 text-red-800">Error: {error}</div>;
  }

  return (
    <div className="p-4 bg-green-100 text-green-800">
      <h3 className="font-bold">Years Debug:</h3>
      <ul>
        {years.map(year => (
          <li key={year}>📅 {year}</li>
        ))}
      </ul>
    </div>
  );
}
