'use client'

import React, { useState, useEffect } from 'react';
import { getMemoryUsage, analyzeBundleSize } from '@/lib/utils/performance';

interface PerformanceStats {
  memory?: {
    used: number;
    total: number;
    limit: number;
  };
  bundle?: {
    jsCount: number;
    cssCount: number;
    totalResources: number;
    loadTime: number;
  };
  renderCount: number;
  lastUpdate: Date;
}

export function PerformanceMonitor() {
  const [stats, setStats] = useState<PerformanceStats>({
    renderCount: 0,
    lastUpdate: new Date()
  });
  const [isVisible, setIsVisible] = useState(false);

  // Track render count
  useEffect(() => {
    setStats(prev => ({
      ...prev,
      renderCount: prev.renderCount + 1,
      lastUpdate: new Date()
    }));
  });

  // Update performance stats periodically
  useEffect(() => {
    const updateStats = () => {
      const memory = getMemoryUsage();
      const bundle = analyzeBundleSize();
      
      setStats(prev => ({
        ...prev,
        memory: memory || prev.memory,
        bundle: bundle || prev.bundle
      }));
    };

    // Initial update
    updateStats();

    // Update every 5 seconds
    const interval = setInterval(updateStats, 5000);
    return () => clearInterval(interval);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-mono shadow-lg hover:bg-blue-700"
      >
        📊 Perf
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-black/90 text-white p-4 rounded-lg shadow-xl min-w-[300px] font-mono text-xs">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold text-sm">Performance Monitor</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-white"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-2">
            <div>
              <strong>Renders:</strong> {stats.renderCount}
            </div>
            
            <div>
              <strong>Last Update:</strong> {stats.lastUpdate.toLocaleTimeString()}
            </div>
            
            {stats.memory && (
              <div>
                <strong>Memory:</strong>
                <div className="ml-2">
                  Used: {stats.memory.used}MB
                </div>
                <div className="ml-2">
                  Total: {stats.memory.total}MB
                </div>
                <div className="ml-2">
                  Limit: {stats.memory.limit}MB
                </div>
                <div className="ml-2">
                  Usage: {Math.round((stats.memory.used / stats.memory.limit) * 100)}%
                </div>
              </div>
            )}
            
            {stats.bundle && (
              <div>
                <strong>Bundle:</strong>
                <div className="ml-2">
                  JS Files: {stats.bundle.jsCount}
                </div>
                <div className="ml-2">
                  CSS Files: {stats.bundle.cssCount}
                </div>
                <div className="ml-2">
                  Total Resources: {stats.bundle.totalResources}
                </div>
                <div className="ml-2">
                  Load Time: {Math.round(stats.bundle.loadTime)}ms
                </div>
              </div>
            )}
            
            <div className="pt-2 border-t border-gray-600">
              <button
                onClick={() => {
                  console.log('🧹 Forcing garbage collection...');
                  if ('gc' in window) {
                    (window as any).gc();
                  }
                }}
                className="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs"
              >
                Force GC
              </button>
              
              <button
                onClick={() => {
                  console.log('📊 Performance report:', stats);
                  console.log('🎯 Cache stats:', {
                    // Add cache stats here if needed
                  });
                }}
                className="bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs ml-2"
              >
                Log Stats
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Hook for tracking component performance
export function usePerformanceTracking(componentName: string) {
  const renderCount = React.useRef(0);
  const mountTime = React.useRef(Date.now());
  
  React.useEffect(() => {
    renderCount.current += 1;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 ${componentName} rendered ${renderCount.current} times`);
    }
  });
  
  React.useEffect(() => {
    const startTime = mountTime.current;
    
    return () => {
      const lifetime = Date.now() - startTime;
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏱️ ${componentName} lifetime: ${lifetime}ms, renders: ${renderCount.current}`);
      }
    };
  }, [componentName]);
  
  return {
    renderCount: renderCount.current,
    lifetime: Date.now() - mountTime.current
  };
}
