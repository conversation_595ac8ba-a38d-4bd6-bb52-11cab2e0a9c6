'use client'

import React, { useState } from 'react';
import { Settings, Database, AlertTriangle, Loader2, RefreshCw, Trash2 } from 'lucide-react';

export function SettingsView() {
  const [isFullIndexing, setIsFullIndexing] = useState(false);
  const [indexingStatus, setIndexingStatus] = useState<string>('');
  const [isCleaningUp, setIsCleaningUp] = useState(false);
  const [cleanupStatus, setCleanupStatus] = useState<string>('');

  // Full index function - clears database and reindexes everything
  const triggerFullIndex = async () => {
    if (!confirm('⚠️ WARNING: Full Index will clear the database and reindex all files. This may take several minutes. Are you sure you want to continue?')) {
      return;
    }

    try {
      setIsFullIndexing(true);
      setIndexingStatus('Starting full index...');
      
      const response = await fetch('/api/photos/index', {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error('Failed to start full indexing');
      }
      
      const data = await response.json();
      setIndexingStatus('Full indexing started in background. This may take several minutes.');
      
      // Check status periodically
      const checkStatus = setInterval(async () => {
        try {
          const statusResponse = await fetch('/api/photos/status');
          if (statusResponse.ok) {
            const statusData = await statusResponse.json();
            if (!statusData.isIndexing) {
              setIndexingStatus('Full indexing completed!');
              clearInterval(checkStatus);
              setTimeout(() => {
                setIsFullIndexing(false);
                setIndexingStatus('');
              }, 3000);
            }
          }
        } catch (error) {
          console.error('Error checking status:', error);
        }
      }, 5000);
      
    } catch (error) {
      console.error('Error triggering full indexing:', error);
      setIndexingStatus('Error starting full indexing. Please try again.');
      setTimeout(() => {
        setIsFullIndexing(false);
        setIndexingStatus('');
      }, 3000);
    }
  };

  // Cleanup function - removes deleted files from database
  const triggerCleanup = async () => {
    if (!confirm('🧹 This will scan the database and remove records for files that no longer exist on disk. This is safe and recommended for database maintenance. Continue?')) {
      return;
    }

    try {
      setIsCleaningUp(true);
      setCleanupStatus('Starting database cleanup...');

      const response = await fetch('/api/photos/cleanup-deleted', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to start cleanup');
      }

      const data = await response.json();

      if (data.deletedCount > 0) {
        setCleanupStatus(`Cleanup completed! Removed ${data.deletedCount} deleted files from database.`);
      } else {
        setCleanupStatus('Cleanup completed! Database is already clean - no deleted files found.');
      }

      setTimeout(() => {
        setIsCleaningUp(false);
        setCleanupStatus('');
      }, 5000);

    } catch (error) {
      console.error('Error triggering cleanup:', error);
      setCleanupStatus('Error during cleanup. Please try again.');
      setTimeout(() => {
        setIsCleaningUp(false);
        setCleanupStatus('');
      }, 3000);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center gap-2 mb-2">
          <Settings className="h-8 w-8" />
          Settings
        </h1>
        <p className="text-muted-foreground">
          Configure your Asset Hub settings and perform system maintenance.
        </p>
      </div>

      {/* Database & Indexing Section */}
      <div className="bg-card border border-border rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2 mb-4">
          <Database className="h-5 w-5" />
          Database & Indexing
        </h2>
        
        <div className="space-y-4">
          <div className="bg-muted/20 border border-border rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-amber-700 dark:text-amber-400 mb-2">
                  Full Index - Advanced Operation
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  This operation will clear the entire database and reindex all files from scratch. 
                  Use this only when you need to completely rebuild the asset database or if you're 
                  experiencing data inconsistencies.
                </p>
                <ul className="text-sm text-muted-foreground mb-4 space-y-1">
                  <li>• Clears all existing asset records</li>
                  <li>• Scans the entire photo directory</li>
                  <li>• Rebuilds metadata for all files</li>
                  <li>• May take several minutes to complete</li>
                </ul>
                
                {indexingStatus && (
                  <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-md">
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      {indexingStatus}
                    </p>
                  </div>
                )}
                
                <button
                  onClick={triggerFullIndex}
                  disabled={isFullIndexing}
                  className="flex items-center gap-2 px-4 py-2 bg-amber-600 hover:bg-amber-700 disabled:bg-amber-400 text-white rounded-md text-sm font-medium transition-colors"
                >
                  {isFullIndexing ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Full Indexing...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4" />
                      Start Full Index
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="bg-muted/20 border border-border rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Trash2 className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-blue-700 dark:text-blue-400 mb-2">
                  Database Cleanup - Maintenance Operation
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  This operation scans the database and removes records for files that no longer exist on disk.
                  This is safe and helps keep your database synchronized with your file system.
                </p>
                <ul className="text-sm text-muted-foreground mb-4 space-y-1">
                  <li>• Removes database records for deleted files</li>
                  <li>• Does not delete any actual files</li>
                  <li>• Safe operation - only cleans orphaned records</li>
                  <li>• Improves database performance and accuracy</li>
                </ul>

                {cleanupStatus && (
                  <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-md">
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      {cleanupStatus}
                    </p>
                  </div>
                )}

                <button
                  onClick={triggerCleanup}
                  disabled={isCleaningUp}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md text-sm font-medium transition-colors"
                >
                  {isCleaningUp ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Cleaning Up...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4" />
                      Clean Up Database
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="bg-muted/10 border border-border rounded-lg p-4">
            <h3 className="font-medium mb-2">Database Operations Explained</h3>
            <div className="text-sm text-muted-foreground space-y-2">
              <p>
                <strong>Regular Refresh (Dashboard):</strong> Quickly scans for new and recently modified files
                without clearing the database. Automatically includes cleanup of deleted files. Use this for day-to-day updates.
              </p>
              <p>
                <strong>Database Cleanup (Settings):</strong> Removes database records for files that no longer exist on disk.
                Safe maintenance operation that keeps your database synchronized.
              </p>
              <p>
                <strong>Full Index (Settings):</strong> Completely rebuilds the database from scratch.
                Use this only when experiencing issues or after major file system changes.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Future Settings Sections */}
      <div className="bg-card border border-border rounded-lg p-6 opacity-60">
        <h2 className="text-xl font-semibold mb-4">Additional Settings</h2>
        <p className="text-muted-foreground text-sm">
          More configuration options will be available here in future updates, including:
        </p>
        <ul className="text-sm text-muted-foreground mt-2 space-y-1">
          <li>• Storage path configuration</li>
          <li>• AI analysis settings</li>
          <li>• User management</li>
          <li>• Backup and sync options</li>
        </ul>
      </div>
    </div>
  );
}
