'use client'

import React, { useState, useEffect } from 'react';
import {
  ChevronDown,
  ChevronRight,
  Folder,
  FolderOpen,
  RefreshCw,
  BarChart3,
  FileImage,
  Files,
  HardDrive,
  AlertCircle,
  Info,
  FileText,
  Video,
  Music,
  Archive
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshProgressModal } from '@/components/ui/RefreshProgressModal';

interface FolderTreeNode {
  id: string;
  name: string;
  path: string;
  parentPath: string | null;
  fileCount: number;
  imageCount: number;
  totalSize: string;
  lastModified: Date;
  lastScanned: Date;
  children: FolderTreeNode[];
  level: number;
}

interface FolderTreeStats {
  totalFolders: number;
  totalFiles: number;
  totalImages: number;
  totalSize: string;
  lastUpdated: Date;
}

interface FileTypeStats {
  extension: string;
  count: number;
  totalSize: number;
  percentage: number;
}

interface FileTypesResponse {
  success: boolean;
  fileTypes: FileTypeStats[];
  totalFiles: number;
  totalSize: number;
  lastUpdated: Date;
  error?: string;
}

interface StatsResponse {
  success: boolean;
  tree: FolderTreeNode[];
  stats: FolderTreeStats;
  source: 'folder_tracking' | 'assets_table';
  message: string;
  error?: string;
}

export function StatsView() {
  const [data, setData] = useState<StatsResponse | null>(null);
  const [fileTypes, setFileTypes] = useState<FileTypeStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [refreshing, setRefreshing] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('Component mounted');
  const [showRefreshModal, setShowRefreshModal] = useState(false);

  const fetchFileTypes = async () => {
    try {
      console.log('🔍 Fetching file types from /api/stats/file-types...');
      const response = await fetch('/api/stats/file-types');

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📊 File types result:', { success: result.success, typesCount: result.fileTypes?.length });

      if (result.success) {
        setFileTypes(result.fileTypes || []);
        console.log('✅ File types data loaded successfully');
      }
    } catch (err) {
      console.error('❌ Error fetching file types:', err);
      // Don't set error state for file types, just log it
    }
  };

  const fetchStats = async () => {
    try {
      setDebugInfo('Starting fetch...');
      console.log('🔍 Fetching stats from /api/stats/folder-tree...');
      setError(null);

      setDebugInfo('Calling fetch API...');
      const response = await fetch('/api/stats/folder-tree');
      console.log('📊 Response status:', response.status);
      setDebugInfo(`Response received: ${response.status}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      setDebugInfo('Parsing JSON...');
      const result = await response.json();
      console.log('📊 API Result:', { success: result.success, statsCount: result.stats?.totalFolders });
      setDebugInfo(`JSON parsed. Success: ${result.success}`);

      if (result.success) {
        // The API should now return properly sorted data, but we'll keep client-side sorting as backup
        const sortedResult = {
          ...result,
          tree: sortFoldersRecursively(result.tree)
        };
        setData(sortedResult);

        // Auto-expand root level folders
        const rootPaths = sortedResult.tree.map((node: FolderTreeNode) => node.path);
        setExpandedFolders(new Set(rootPaths.slice(0, 5))); // Expand first 5 root folders
        console.log('✅ Stats data loaded successfully');
        setDebugInfo(`Data loaded: ${result.stats.totalFolders} folders`);
      } else {
        setError(result.error || 'Failed to load statistics');
        setDebugInfo(`API error: ${result.error}`);
      }
    } catch (err) {
      console.error('❌ Error fetching stats:', err);
      const errorMsg = err instanceof Error ? err.message : 'Failed to load statistics';
      setError(errorMsg);
      setDebugInfo(`Fetch error: ${errorMsg}`);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    console.log('🚀 StatsView component mounted, fetching stats...');
    setDebugInfo('useEffect triggered');
    fetchStats();
    fetchFileTypes();
  }, []);

  const handleRefresh = () => {
    console.log('🔄 Starting fast refresh from Stats page...');
    setRefreshing(true);
    setShowRefreshModal(true);
  };

  // Handle refresh completion
  const handleRefreshComplete = async (results: any) => {
    console.log('🎉 REFRESH COMPLETED! Results:', results);
    console.log('🔄 About to force refresh all stats data to show changes immediately...');

    // Reset refresh state first
    setRefreshing(false);
    setShowRefreshModal(false);

    // Force refresh the stats data to show the latest changes
    try {
      console.log('📡 Calling fetchStats() and fetchFileTypes() with force refresh...');

      // Force refresh both stats and file types
      await fetchStats();
      await fetchFileTypes();

      console.log('✅ Stats refreshed after modal completion - should show new files now!');

      // If data doesn't seem to update, force a page reload as fallback
      setTimeout(() => {
        console.log('🔄 Ensuring fresh data is displayed...');
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('❌ Error refreshing stats after modal completion:', error);
      // On error, try page reload as fallback
      setTimeout(() => {
        console.log('🔄 Error occurred, reloading page as fallback...');
        window.location.reload();
      }, 1000);
    }
  };

  const toggleFolder = (path: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedFolders(newExpanded);
  };

  const formatFileSize = (bytes: string | number) => {
    const size = typeof bytes === 'string' ? parseInt(bytes) : bytes;
    if (size === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(size) / Math.log(k));
    return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const getFileTypeIcon = (extension: string) => {
    const ext = extension.toLowerCase();

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'ico'].includes(ext)) {
      return <FileImage className="h-4 w-4 text-green-500" />;
    }

    // Video files
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp'].includes(ext)) {
      return <Video className="h-4 w-4 text-red-500" />;
    }

    // Audio files
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'].includes(ext)) {
      return <Music className="h-4 w-4 text-purple-500" />;
    }

    // Archive files
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(ext)) {
      return <Archive className="h-4 w-4 text-orange-500" />;
    }

    // Text/Document files
    if (['txt', 'doc', 'docx', 'pdf', 'rtf', 'odt'].includes(ext)) {
      return <FileText className="h-4 w-4 text-blue-500" />;
    }

    // Default for other files
    return <Files className="h-4 w-4 text-gray-500" />;
  };

  // Sort folders to show most recent first - improved date parsing
  const sortFoldersRecursively = (nodes: FolderTreeNode[]): FolderTreeNode[] => {
    const sortedNodes = [...nodes].sort((a, b) => {
      // Sort years in descending order (2025, 2024, 2023...)
      if (/^\d{4}$/.test(a.name) && /^\d{4}$/.test(b.name)) {
        return parseInt(b.name) - parseInt(a.name);
      }

      // Enhanced date extraction function
      const extractDate = (name: string): Date | null => {
        // Pattern 1: DD-MM-YYYY (like "15-06-2025", "28-02-2025", "01-01-2025")
        const ddmmMatch = name.match(/(\d{1,2})-(\d{1,2})-(\d{4})/);
        if (ddmmMatch) {
          const day = parseInt(ddmmMatch[1]);
          const month = parseInt(ddmmMatch[2]) - 1; // JavaScript months are 0-based
          const year = parseInt(ddmmMatch[3]);
          return new Date(year, month, day);
        }

        // Pattern 2: YYYY-MM-DD (like "2025-06-15")
        const yyyymmMatch = name.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
        if (yyyymmMatch) {
          const year = parseInt(yyyymmMatch[1]);
          const month = parseInt(yyyymmMatch[2]) - 1;
          const day = parseInt(yyyymmMatch[3]);
          return new Date(year, month, day);
        }

        // Pattern 3: Month folders like "06JUN2025", "03MAR2025", "02FEB2025", "01JAN2025"
        const monthMatch = name.match(/(\d{2})([A-Z]{3})(\d{4})/);
        if (monthMatch) {
          const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
          const day = parseInt(monthMatch[1]);
          const monthIndex = monthNames.indexOf(monthMatch[2]);
          const year = parseInt(monthMatch[3]);
          if (monthIndex !== -1) {
            return new Date(year, monthIndex, day);
          }
        }

        // Pattern 4: Just month folders like "JUN2025", "MAR2025", "FEB2025", "JAN2025"
        const monthOnlyMatch = name.match(/([A-Z]{3})(\d{4})/);
        if (monthOnlyMatch) {
          const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
          const monthIndex = monthNames.indexOf(monthOnlyMatch[1]);
          const year = parseInt(monthOnlyMatch[2]);
          if (monthIndex !== -1) {
            return new Date(year, monthIndex, 1); // First day of month
          }
        }

        return null;
      };

      const dateA = extractDate(a.name);
      const dateB = extractDate(b.name);

      // If both have dates, sort by date (most recent first)
      if (dateA && dateB) {
        return dateB.getTime() - dateA.getTime();
      }

      // If only one has a date, prioritize the one with date
      if (dateA && !dateB) return -1;
      if (!dateA && dateB) return 1;

      // Default: sort by name descending (most recent first for text)
      return b.name.localeCompare(a.name);
    });

    // Recursively sort children
    return sortedNodes.map(node => ({
      ...node,
      children: sortFoldersRecursively(node.children)
    }));
  };

  const renderFolderNode = (node: FolderTreeNode): React.ReactNode => {
    const isExpanded = expandedFolders.has(node.path);
    const hasChildren = node.children.length > 0;
    const indentLevel = node.level * 20;

    return (
      <div key={node.path} className="select-none">
        <div 
          className="flex items-center py-2 px-3 hover:bg-secondary/50 rounded-md cursor-pointer group"
          style={{ paddingLeft: `${12 + indentLevel}px` }}
        >
          <span 
            className="mr-2 cursor-pointer flex-shrink-0" 
            onClick={() => hasChildren && toggleFolder(node.path)}
          >
            {hasChildren ? (
              isExpanded ? 
                <ChevronDown className="h-4 w-4 text-muted-foreground" /> : 
                <ChevronRight className="h-4 w-4 text-muted-foreground" />
            ) : (
              <span className="w-4"></span>
            )}
          </span>
          
          <span className="mr-2 flex-shrink-0">
            {isExpanded && hasChildren ? 
              <FolderOpen className="h-4 w-4 text-blue-500" /> : 
              <Folder className="h-4 w-4 text-blue-500" />
            }
          </span>
          
          <span className="font-medium text-sm flex-grow min-w-0">
            {node.name}
            {node.fileCount > 0 && (
              <span className="ml-2 text-xs text-muted-foreground font-normal">
                ({formatNumber(node.fileCount)})
              </span>
            )}
          </span>

          <div className="flex items-center gap-2 ml-4 flex-shrink-0">
            <span className="text-xs text-muted-foreground">
              {formatFileSize(node.totalSize)}
            </span>
          </div>
        </div>
        
        {isExpanded && hasChildren && (
          <div>
            {node.children.map(child => renderFolderNode(child))}
          </div>
        )}
      </div>
    );
  };

  console.log('🎯 StatsView render - loading:', loading, 'error:', error, 'data:', !!data);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>Loading folder statistics...</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Debug: {debugInfo}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            No statistics data available.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <BarChart3 className="h-8 w-8" />
            Folder Statistics
          </h1>
          <p className="text-muted-foreground mt-1">
            Overview of your asset collection organized by folders
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={refreshing}
          variant="outline"
          title="Fast refresh: scans for new files"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Data Source Info */}
      {data.source === 'assets_table' && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {data.message} - Statistics are built from the assets table which may be slower for large collections.
          </AlertDescription>
        </Alert>
      )}

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Folders</CardTitle>
            <Folder className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.stats.totalFolders)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Images</CardTitle>
            <FileImage className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.stats.totalImages)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Files</CardTitle>
            <Files className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.stats.totalFiles)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Size</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatFileSize(data.stats.totalSize)}</div>
          </CardContent>
        </Card>
      </div>

      {/* File Type Statistics */}
      {fileTypes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Files className="h-5 w-5" />
              File Types
            </CardTitle>
            <CardDescription>
              Breakdown of files by extension ({formatNumber(fileTypes.reduce((sum, ft) => sum + ft.count, 0))} total files)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {fileTypes.slice(0, 12).map((fileType) => (
                <div key={fileType.extension} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-2">
                    {getFileTypeIcon(fileType.extension)}
                    <span className="font-medium text-sm">
                      .{fileType.extension}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold">{formatNumber(fileType.count)}</div>
                    <div className="text-xs text-muted-foreground">
                      {fileType.percentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {fileTypes.length > 12 && (
              <div className="mt-4 text-center text-sm text-muted-foreground">
                Showing top 12 file types out of {fileTypes.length} total
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Folder Tree */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            Folder Structure
          </CardTitle>
          <CardDescription>
            Expandable tree view of all folders with asset counts and sizes.
            Last updated: {new Date(data.stats.lastUpdated).toLocaleString()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="max-h-[600px] overflow-auto border rounded-md p-2">
            {data.tree.length > 0 ? (
              <div className="space-y-1">
                {data.tree.map(node => renderFolderNode(node))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No folders found
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Refresh Progress Modal */}
      <RefreshProgressModal
        isOpen={showRefreshModal}
        onClose={() => {
          console.log('🚪 RefreshProgressModal: Manual close triggered from Stats page');
          setShowRefreshModal(false);
          setRefreshing(false);
        }}
        onComplete={handleRefreshComplete}
        onCancel={() => {
          console.log('🛑 RefreshProgressModal: User cancelled refresh from Stats page');
          setShowRefreshModal(false);
          setRefreshing(false);
        }}
      />
    </div>
  );
}
