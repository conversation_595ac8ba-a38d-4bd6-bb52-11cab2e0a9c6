'use client'

import React, { useState, useEffect } from "react";
import { AssetGrid } from "@/components/ui/AssetGrid";
import { AssetList } from "@/components/ui/AssetList";
import { AssetDetails } from "@/components/ui/AssetDetails";
import { DashboardSkeleton, LoadingState } from "@/components/ui/SkeletonLoader";
import { Asset } from "@/lib/types";
import { Grid, List, Image, Video, FileAudio, File } from "lucide-react";

interface AssetTypeViewProps {
  assetType: 'image' | 'video' | 'audio' | 'document';
  title: string;
}

const typeIcons = {
  image: Image,
  video: Video,
  audio: FileAudio,
  document: File,
};

export function AssetTypeView({ assetType, title }: AssetTypeViewProps) {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  const IconComponent = typeIcons[assetType];

  useEffect(() => {
    fetchAssetsByType();
  }, [assetType]);

  const fetchAssetsByType = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/photos/optimized?type=${assetType}&limit=100`);
      if (!response.ok) {
        throw new Error('Failed to fetch assets');
      }

      const data = await response.json();
      if (data.success) {
        setAssets(data.assets || []);
      } else {
        throw new Error(data.error || 'Failed to fetch assets');
      }
    } catch (err) {
      console.error('Error fetching assets:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch assets');
    } finally {
      setLoading(false);
    }
  };

  const handleAssetClick = (asset: Asset) => {
    setSelectedAsset(asset);
  };

  if (loading) {
    return <DashboardSkeleton />;
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <IconComponent className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Error loading {title.toLowerCase()}</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <button
              onClick={fetchAssetsByType}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <IconComponent className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">{title}</h1>
            <p className="text-muted-foreground">
              {assets.length} {title.toLowerCase()} found
            </p>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode("grid")}
            className={`p-2 rounded-md ${
              viewMode === "grid" 
                ? "bg-primary text-primary-foreground" 
                : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
            }`}
          >
            <Grid className="h-4 w-4" />
          </button>
          <button
            onClick={() => setViewMode("list")}
            className={`p-2 rounded-md ${
              viewMode === "list" 
                ? "bg-primary text-primary-foreground" 
                : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
            }`}
          >
            <List className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Content */}
      {assets.length === 0 ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <IconComponent className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">No {title.toLowerCase()} found</h2>
            <p className="text-muted-foreground">
              Upload some {title.toLowerCase()} to get started
            </p>
          </div>
        </div>
      ) : (
        <>
          {viewMode === "grid" ? (
            <AssetGrid
              assets={assets}
              onAssetClick={handleAssetClick}
              loading={loading}
            />
          ) : (
            <AssetList
              assets={assets}
              onAssetClick={handleAssetClick}
              loading={loading}
            />
          )}

          {/* Asset Details Modal */}
          {selectedAsset && (
            <AssetDetails
              asset={selectedAsset}
              onClose={() => setSelectedAsset(null)}
            />
          )}
        </>
      )}
    </div>
  );
}
