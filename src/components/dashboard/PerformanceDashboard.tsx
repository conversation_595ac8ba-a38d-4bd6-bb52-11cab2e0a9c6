'use client'

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Database, 
  Search, 
  Image, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';

interface PerformanceMetrics {
  summary: {
    totalRequests: number;
    averageResponseTime: number;
    slowestEndpoint: string;
    fastestEndpoint: string;
    errorRate: number;
  };
  endpoints: Record<string, {
    count: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    errors: number;
  }>;
  categories: Record<string, {
    count: number;
    averageTime: number;
    totalTime: number;
  }>;
  recommendations: string[];
}

export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchMetrics = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/performance');
      const data = await response.json();
      
      if (data.success) {
        setMetrics(data.report);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearMetrics = async () => {
    try {
      const response = await fetch('/api/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'clear' })
      });
      
      if (response.ok) {
        await fetchMetrics();
      }
    } catch (error) {
      console.error('Error clearing metrics:', error);
    }
  };

  useEffect(() => {
    fetchMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const getPerformanceColor = (time: number) => {
    if (time < 100) return 'text-green-600';
    if (time < 500) return 'text-yellow-600';
    if (time < 1000) return 'text-orange-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (time: number) => {
    if (time < 100) return <Badge variant="default" className="bg-green-100 text-green-800">Excellent</Badge>;
    if (time < 500) return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Good</Badge>;
    if (time < 1000) return <Badge variant="default" className="bg-orange-100 text-orange-800">Fair</Badge>;
    return <Badge variant="destructive">Poor</Badge>;
  };

  if (!metrics) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Performance Dashboard</h2>
          <Button onClick={fetchMetrics} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Dashboard</h2>
          {lastUpdated && (
            <p className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={clearMetrics}>
            Clear Metrics
          </Button>
          <Button onClick={fetchMetrics} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.summary.totalRequests.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPerformanceColor(metrics.summary.averageResponseTime)}`}>
              {metrics.summary.averageResponseTime}ms
            </div>
            <div className="mt-1">
              {getPerformanceBadge(metrics.summary.averageResponseTime)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${metrics.summary.errorRate > 5 ? 'text-red-600' : 'text-green-600'}`}>
              {metrics.summary.errorRate.toFixed(1)}%
            </div>
            <Progress 
              value={Math.min(metrics.summary.errorRate, 100)} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Healthy</div>
            <p className="text-xs text-muted-foreground mt-1">
              System performing well
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Category Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Performance by Category</CardTitle>
          <CardDescription>Average response times across different system components</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(metrics.categories).map(([category, stats]) => (
              <div key={category} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {category === 'database' && <Database className="h-4 w-4" />}
                  {category === 'search' && <Search className="h-4 w-4" />}
                  {category === 'thumbnail' && <Image className="h-4 w-4" />}
                  {category === 'api' && <Activity className="h-4 w-4" />}
                  <span className="capitalize font-medium">{category.replace('_', ' ')}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-muted-foreground">
                    {stats.count} requests
                  </span>
                  <span className={`font-mono ${getPerformanceColor(stats.averageTime)}`}>
                    {stats.averageTime}ms
                  </span>
                  {getPerformanceBadge(stats.averageTime)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Performance Recommendations</span>
          </CardTitle>
          <CardDescription>Suggestions to improve system performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {metrics.recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-2 p-3 bg-blue-50 rounded-lg">
                <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm">{recommendation}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Endpoints */}
      <Card>
        <CardHeader>
          <CardTitle>Endpoint Performance</CardTitle>
          <CardDescription>Response times for API endpoints</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(metrics.endpoints)
              .sort(([,a], [,b]) => b.averageTime - a.averageTime)
              .slice(0, 10)
              .map(([endpoint, stats]) => (
                <div key={endpoint} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex-1">
                    <div className="font-mono text-sm">{endpoint}</div>
                    <div className="text-xs text-muted-foreground">
                      {stats.count} requests • {stats.errors} errors
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`font-mono ${getPerformanceColor(stats.averageTime)}`}>
                      {stats.averageTime}ms
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {stats.minTime}-{stats.maxTime}ms
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
