'use client'

import React, { useState, useEffect, useRef, useCallback } from "react";
import { AssetGrid } from "@/components/ui/AssetGrid";
import { VirtualizedAssetGrid } from "@/components/ui/VirtualizedAssetGrid";
import { AssetList } from "@/components/ui/AssetList";
import { AssetDetails } from "@/components/ui/AssetDetails";
import { RefreshProgressModal } from "@/components/ui/RefreshProgressModal";
import { DashboardSkeleton, LoadingState, EmptyState, FilterLoading } from "@/components/ui/SkeletonLoader";

import { useAssets, useSearch } from "@/lib/store";
import { Asset } from "@/lib/types";
import { Grid, List, SlidersHorizontal, Search, RefreshCw, Upload, Loader2 } from "lucide-react";
import { PerformanceIndicator } from "@/components/performance/PerformanceIndicator";


export function DashboardView() {
  console.log('🎯 DashboardView component rendering... [UPDATED VERSION]');
  const { assets, loading, error, hasMore, total, loadMore, updateAsset, deleteAsset, scanProgress, dateFilters, setFilters, fetchAssets, hasAttemptedLoad } = useAssets();
  console.log('🎯 fetchAssets function check:', { fetchAssets: !!fetchAssets, type: typeof fetchAssets });
  console.log('🎯 useAssets hook values:', { hasAttemptedLoad, assetsLength: assets.length, loading });
  const { searchQuery, setSearchQuery, searchResults } = useSearch(dateFilters);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filterOpen, setFilterOpen] = useState(false);
  const [containerDimensions, setContainerDimensions] = useState({ width: 1200, height: 600 });
  const [useVirtualScrolling, setUseVirtualScrolling] = useState(false);
  const [showRefreshModal, setShowRefreshModal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);



  // Infinite scroll ref
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Debug logging for assets changes
  useEffect(() => {
    console.log('🔍 DashboardView: assets state changed', {
      assetsLength: assets.length,
      loading,
      error,
      firstAsset: assets[0]?.filename,
      total,
      hasMore
    });
  }, [assets, loading, error, total, hasMore]);



  // Auto-load assets on initial mount
  useEffect(() => {
    console.log('🚀 useEffect TRIGGERED:', {
      hasAttemptedLoad,
      fetchAssetsType: typeof fetchAssets,
      fetchAssetsExists: !!fetchAssets,
      dependencyArray: [fetchAssets, hasAttemptedLoad]
    });

    if (!fetchAssets) {
      console.error('🚨 fetchAssets is undefined! Cannot load assets.');
      return;
    }

    if (!hasAttemptedLoad) {
      console.log('🚀 ===== INITIAL DASHBOARD LOAD =====');
      console.log('🚀 Loading assets for the first time...');
      fetchAssets(true).then(() => {
        console.log('🚀 Initial fetchAssets completed successfully');
      }).catch(error => {
        console.error('🚀 Initial fetchAssets failed:', error);
      });
      console.log('🚀 ===== END INITIAL DASHBOARD LOAD =====');
    } else {
      console.log('🚀 Skipping initial load - hasAttemptedLoad is true');
    }
  }, [fetchAssets, hasAttemptedLoad]);

  // Load assets when filters change (but not on initial mount)
  useEffect(() => {
    if (hasAttemptedLoad) {
      console.log('🚀 ===== DASHBOARD FILTER CHANGE =====');
      console.log('🚀 Current dateFilters:', dateFilters);
      console.log('🚀 Assets count before fetch:', assets.length);
      console.log('🚀 Loading state:', loading);
      console.log('🚀 About to call fetchAssets(true)...');

      fetchAssets(true).then(() => {
        console.log('🚀 fetchAssets completed successfully');
        console.log('🚀 New assets count:', assets.length);
      }).catch(error => {
        console.error('🚀 fetchAssets failed:', error);
      });

      console.log('🚀 ===== END DASHBOARD FILTER CHANGE =====');
    }
  }, [dateFilters.year, dateFilters.month, dateFilters.day]);

  // Filter changes are now handled by the main asset loading useEffect above

  // Smart refresh function - detects new/deleted files without full scan
  const smartRefresh = useCallback(async () => {
    console.log('🔄 Smart refresh triggered from dashboard button');
    setIsRefreshing(true);
    setShowRefreshModal(true);
  }, []);

  // Infinite scroll logic - only for main asset list, not search results
  const handleLoadMore = useCallback(() => {
    // Only trigger infinite scroll for main asset list, not search results
    const isShowingSearchResults = searchQuery.trim().length > 0;

    if (!loading && hasMore && !isShowingSearchResults) {
      console.log('🔄 Infinite scroll triggered - loading more assets');
      loadMore();
    }
  }, [loading, hasMore, loadMore, searchQuery]);

  // Enhanced intersection observer for infinite scroll with performance optimizations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting) {
          // Debounce the load more to prevent rapid firing
          const timeoutId = setTimeout(() => {
            handleLoadMore();
          }, 100);
          return () => clearTimeout(timeoutId);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '200px', // Increased to 200px for smoother experience
      }
    );

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [handleLoadMore]);

  // Container resize observer for virtual scrolling
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Check if ResizeObserver is available
    if (typeof ResizeObserver === 'undefined') {
      console.warn('ResizeObserver not available, using fallback dimensions');
      setContainerDimensions({ width: 1200, height: 600 });
      return;
    }

    try {
      const resizeObserver = new ResizeObserver((entries) => {
        const entry = entries[0];
        if (entry) {
          const { width, height } = entry.contentRect;
          setContainerDimensions({ width, height: Math.max(height, 400) });
        }
      });

      resizeObserver.observe(container);
      return () => resizeObserver.disconnect();
    } catch (error) {
      console.error('Error setting up ResizeObserver:', error);
      setContainerDimensions({ width: 1200, height: 600 });
    }
  }, []);

  // Temporarily disable virtual scrolling to isolate issues
  useEffect(() => {
    // Disable virtual scrolling for now to prevent crashes
    setUseVirtualScrolling(false);
  }, []);

  // Handle refresh completion
  const handleRefreshComplete = async (results: any) => {
    console.log('🎉 REFRESH COMPLETED! Results:', results);
    console.log('🔄 About to force refresh all data to show changes immediately...');

    // Reset refresh state first
    setIsRefreshing(false);
    setShowRefreshModal(false);

    // Clear any caches that might be preventing fresh data
    try {
      // Clear browser caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
        console.log('🧹 Cleared browser caches');
      }
    } catch (error) {
      console.log('⚠️ Could not clear browser caches:', error);
    }

    // Force refresh the assets list to show the latest changes
    try {
      console.log('📡 Calling fetchAssets(true, true) with force refresh to bypass loading check...');

      // Force refresh with cache busting - bypass loading check with forceRefresh=true
      await fetchAssets(true, true);

      console.log('✅ Assets refreshed after modal completion - should show new files now!');
      console.log('📊 Current assets count:', assets.length);

      // Additional fallback: Direct API call to ensure fresh data
      console.log('🔄 Making direct API call as additional verification...');
      const directResponse = await fetch(`/api/photos?limit=50&offset=0&_t=${Date.now()}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (directResponse.ok) {
        const directData = await directResponse.json();
        console.log('🔍 Direct API verification:', {
          success: directData.success,
          assetsCount: directData.assets?.length,
          total: directData.total,
          firstAsset: directData.assets?.[0]?.filename
        });
      }

    } catch (error) {
      console.error('❌ Error refreshing assets after modal completion:', error);
      // On error, try page reload as fallback
      setTimeout(() => {
        console.log('🔄 Error occurred, reloading page as fallback...');
        window.location.reload();
      }, 1000);
    }
  };

  // Handle date filter changes
  const handleDateFilterChange = (filters: { year?: string; month?: string; day?: string }) => {
    setFilters(filters);
    // Reset asset selection when filters change
    setSelectedAsset(null);
  };

  // Assets are now sorted by database, no need for frontend sorting
  const displayedAssets = searchQuery.trim()
    ? searchResults // Search results maintain their own sorting
    : assets; // Database assets are already sorted by date (newest first)

  // Debug logging
  console.log('🔍 DashboardView Debug:', {
    assetsLength: assets.length,
    displayedAssetsLength: displayedAssets.length,
    loading,
    error,
    searchQuery: searchQuery.trim(),
    searchResultsLength: searchResults.length,
    dateFilters,
    hasActiveFilters: !!(dateFilters.year || dateFilters.month || dateFilters.day),
    timestamp: new Date().toISOString(),
    urlParams: {
      year: dateFilters.year || null,
      month: dateFilters.month || null,
      day: dateFilters.day || null
    }
  });

  // Ensure assets are loaded - fallback mechanism
  useEffect(() => {
    if (!loading && assets.length === 0 && hasAttemptedLoad && !error) {
      console.log('🔄 DashboardView: No assets loaded despite hasAttemptedLoad=true, forcing fetchAssets...');
      fetchAssets(true).catch(error => {
        console.error('🔄 DashboardView: Failed to force load assets:', error);
      });
    }
  }, [loading, assets.length, hasAttemptedLoad, error, fetchAssets]);

  const handleAssetClick = (asset: Asset) => {
    setSelectedAsset(asset);
  };

  const handleAssetUpdate = (id: string, updates: Partial<Asset>) => {
    updateAsset(id, updates);
    // Also update the selected asset if it's the one being edited
    if (selectedAsset && selectedAsset.id === id) {
      setSelectedAsset({
        ...selectedAsset,
        ...updates,
      });
    }
  };

  const handleAssetDelete = (id: string) => {
    deleteAsset(id);
    setSelectedAsset(null);
  };

  const handleAssetStarUpdate = (updatedAsset: Asset) => {
    // Update the asset in the store
    updateAsset(updatedAsset.id, updatedAsset);

    // Update selected asset if it's the same one
    if (selectedAsset && selectedAsset.id === updatedAsset.id) {
      setSelectedAsset(updatedAsset);
    }
  };

  // Show initial loading state with skeleton
  if (loading && assets.length === 0) {
    return <DashboardSkeleton />;
  }

  // Show manual load prompt when no assets are loaded (fallback)
  // Only show this after we've attempted to load assets at least once
  if (!loading && assets.length === 0 && !error && hasAttemptedLoad) {
    return (
      <EmptyState
        title="No Assets Found"
        description="The dashboard automatically loads the most recent assets. If you see this message, there might be no assets in the database or they need to be refreshed."
        icon={<Grid className="h-12 w-12 text-muted-foreground" />}
        action={
          <a
            href="/upload"
            className="inline-flex items-center px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90"
          >
            <Upload className="mr-2 h-4 w-4" />
            Upload Assets
          </a>
        }
      />
    );
  }

  if (error) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="bg-destructive/10 p-6 rounded-lg max-w-md text-center">
          <p className="text-destructive font-medium mb-2">Error loading assets</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  // Apply date filtering - temporarily simplified to show all assets
  const filteredAssets = displayedAssets;

  return (
    <div className="space-y-6 px-1 relative">
      {/* DEBUG: Manual Asset Loading Button */}
      {!hasAttemptedLoad && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <div className="flex items-center justify-between">
            <div>
              <strong>Debug:</strong> useEffect not triggered. hasAttemptedLoad: {hasAttemptedLoad.toString()}, fetchAssets: {typeof fetchAssets}
            </div>
            <button
              onClick={() => {
                console.log('🔧 DEBUG: Manual fetchAssets trigger');
                console.log('🔧 fetchAssets exists:', !!fetchAssets);
                console.log('🔧 fetchAssets type:', typeof fetchAssets);
                if (fetchAssets) {
                  fetchAssets(true);
                } else {
                  console.error('🚨 fetchAssets is undefined!');
                }
              }}
              className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 text-sm"
            >
              Manual Load
            </button>
          </div>
        </div>
      )}



      {/* Loading overlay when filtering */}
      {loading && assets.length > 0 && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg">
          <LoadingState
            title="Applying filters..."
            description="Filtering your assets based on the selected criteria"
            showSpinner={true}
            className="bg-card border border-border rounded-lg shadow-lg p-6"
          />
        </div>
      )}

      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        {/* Date Filter Dropdowns */}
        <div className="flex items-center gap-2">
          {loading && assets.length > 0 && (
            <FilterLoading className="mr-2" />
          )}
          <select
            value={dateFilters.year || ''}
            onChange={(e) => {
              const year = e.target.value || undefined;
              console.log('🗓️ Year dropdown changed:', year);
              setFilters({ year });
            }}
            className="px-2 py-1 text-xs border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary"
            disabled={loading}
          >
            <option value="">All Years</option>
            <option value="2025">2025</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
            <option value="2021">2021</option>
            <option value="2020">2020</option>
            <option value="2019">2019</option>
            <option value="2018">2018</option>
          </select>

          {/* Month Dropdown (show when year is selected) */}
          {dateFilters.year && (
            <select
              value={dateFilters.month || ''}
              onChange={(e) => {
                const month = e.target.value || undefined;
                console.log('🗓️ Month dropdown changed:', month);
                setFilters({ year: dateFilters.year, month });
              }}
              className="px-2 py-1 text-xs border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary"
              disabled={loading}
            >
              <option value="">All Months</option>
              <option value="1">Jan</option>
              <option value="2">Feb</option>
              <option value="3">Mar</option>
              <option value="4">Apr</option>
              <option value="5">May</option>
              <option value="6">Jun</option>
              <option value="7">Jul</option>
              <option value="8">Aug</option>
              <option value="9">Sep</option>
              <option value="10">Oct</option>
              <option value="11">Nov</option>
              <option value="12">Dec</option>
            </select>
          )}

          {/* Clear Filter */}
          {(dateFilters.year || dateFilters.month) && (
            <button
              onClick={() => {
                console.log('🧹 Clearing filters from dashboard');
                setFilters({});
              }}
              className="px-2 py-1 text-xs bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/80"
            >
              Clear
            </button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <input
              type="search"
              placeholder="Search assets..."
              className="w-full md:w-[32rem] h-9 rounded-md border border-input bg-background px-3 py-1 pl-9 text-sm focus:outline-none focus:ring-1 focus:ring-ring"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <button
            onClick={() => setFilterOpen(!filterOpen)}
            className={`p-2 rounded-md ${
              filterOpen ? "bg-secondary" : "hover:bg-secondary"
            }`}
          >
            <SlidersHorizontal className="h-5 w-5" />
          </button>

          <div className="flex items-center space-x-3">
            {/* Refresh button */}
            <button
              onClick={smartRefresh}
              disabled={isRefreshing}
              className="inline-flex items-center rounded-md bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground hover:bg-primary/90 disabled:opacity-50 transition-colors"
              title="Fast refresh: scans for new files"
            >
              {isRefreshing ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </button>

            {/* Asset count and view mode indicator */}
            <div className="text-sm text-muted-foreground">
              {filteredAssets.length.toLocaleString()} assets • {viewMode} view
            </div>

            {/* View toggle buttons */}
            <div className="flex rounded-md border border-border overflow-hidden">
              <button
                onClick={() => setViewMode("grid")}
                title="Grid View"
                className={`p-2 transition-colors ${
                  viewMode === "grid"
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-secondary/70"
                }`}
              >
                <Grid className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode("list")}
                title="List View"
                className={`p-2 transition-colors ${
                  viewMode === "list"
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-secondary/70"
                }`}
              >
                <List className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
      

      


      {/* Show active filters if any */}
      {(dateFilters.year || dateFilters.month || dateFilters.day) && (
        <div className="flex items-center gap-2 bg-secondary/30 p-2 rounded-md">
          <span className="text-sm font-medium">Active Filters:</span>
          {dateFilters.year && (
            <span className="bg-secondary text-xs rounded-md px-2 py-1">
              Year: {dateFilters.year}
            </span>
          )}
          {dateFilters.month && (
            <span className="bg-secondary text-xs rounded-md px-2 py-1">
              Month: {parseInt(dateFilters.month, 10)}
            </span>
          )}
          {dateFilters.day && (
            <span className="bg-secondary text-xs rounded-md px-2 py-1">
              Day: {dateFilters.day}
            </span>
          )}
          <button
            className="text-xs text-primary hover:underline ml-auto mr-2"
            onClick={() => handleDateFilterChange({})}
          >
            Clear All Filters
          </button>
        </div>
      )}
      
      {filteredAssets.length === 0 ? (
        <EmptyState
          title="No assets found"
          description={
            searchQuery.trim()
              ? "Try adjusting your search query or clear the search"
              : (dateFilters.year || dateFilters.month || dateFilters.day)
                ? "No assets match the current date filters. Try adjusting or clearing the filters."
                : scanProgress.isIndexing
                  ? "Assets are being indexed in the background. Please wait."
                  : "Manual mode: Use 'Refresh & Index' to scan for new photos and refresh the view."
          }
          icon={<Grid className="h-12 w-12 text-muted-foreground" />}
          action={
            scanProgress.total > 0 ? (
              <div className="w-full max-w-md">
                <div className="h-2 w-full bg-secondary rounded-full overflow-hidden mb-2">
                  <div
                    className="h-full bg-primary transition-all duration-300"
                    style={{ width: `${Math.min(100, (scanProgress.current / Math.max(1, scanProgress.total)) * 100)}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span className="font-medium">Total Photos: {scanProgress.total.toLocaleString('en-US')}</span>
                  {scanProgress.isIndexing && (
                    <span>
                      Indexing: {Math.round((scanProgress.current / Math.max(1, scanProgress.total)) * 100)}% complete
                    </span>
                  )}
                </div>
              </div>
            ) : searchQuery.trim() ? (
              <button
                onClick={() => setSearchQuery('')}
                className="inline-flex items-center px-4 py-2 rounded-md bg-secondary text-secondary-foreground hover:bg-secondary/80"
              >
                Clear Search
              </button>
            ) : (dateFilters.year || dateFilters.month || dateFilters.day) ? (
              <button
                onClick={() => setFilters({})}
                className="inline-flex items-center px-4 py-2 rounded-md bg-secondary text-secondary-foreground hover:bg-secondary/80"
              >
                Clear Filters
              </button>
            ) : null
          }
        />
      ) : (
        <div ref={containerRef} className="w-full h-full min-h-[600px]">
          {viewMode === "grid" ? (
            useVirtualScrolling ? (
              <VirtualizedAssetGrid
                assets={filteredAssets}
                onAssetClick={handleAssetClick}
                loading={loading}
                containerHeight={containerDimensions.height}
                containerWidth={containerDimensions.width}
              />
            ) : (
              <AssetGrid
                assets={filteredAssets}
                onAssetClick={handleAssetClick}
                loading={loading}
                onAssetUpdate={handleAssetStarUpdate}
              />
            )
          ) : (
            <AssetList
              assets={filteredAssets}
              onAssetClick={handleAssetClick}
              loading={loading}
            />
          )}
        </div>
      )}
      
      {/* Infinite scroll trigger - only show for main asset list, not search results */}
      {hasMore && !searchQuery.trim() && (
        <div
          ref={loadMoreRef}
          className="h-20 flex items-center justify-center"
        >
          {loading && (
            <LoadingState
              title="Loading more assets..."
              description="Fetching additional assets from your collection"
              showSpinner={true}
              className="h-auto p-4"
            />
          )}
        </div>
      )}

      {selectedAsset && (
        <AssetDetails
          asset={selectedAsset}
          onClose={() => setSelectedAsset(null)}
          onUpdate={handleAssetUpdate}
          onDelete={handleAssetDelete}
          onAssetUpdate={handleAssetStarUpdate}
        />
      )}

      <RefreshProgressModal
        isOpen={showRefreshModal}
        onClose={() => {
          console.log('🚪 RefreshProgressModal: Manual close triggered');
          setShowRefreshModal(false);
          setIsRefreshing(false);
        }}
        onComplete={handleRefreshComplete}
        onCancel={() => {
          console.log('🛑 RefreshProgressModal: User cancelled refresh');
          setShowRefreshModal(false);
          setIsRefreshing(false);
        }}
      />

      {/* Performance Indicator - Temporarily disabled */}
      {/* <PerformanceIndicator /> */}
    </div>
  );
}
