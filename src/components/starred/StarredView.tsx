'use client'

import React, { useState, useEffect } from "react";
import { AssetGrid } from "@/components/ui/AssetGrid";
import { AssetList } from "@/components/ui/AssetList";
import { AssetDetails } from "@/components/ui/AssetDetails";
import { DashboardSkeleton } from "@/components/ui/SkeletonLoader";
import { Asset } from "@/lib/types";
import { Star, Grid, List } from "lucide-react";

export function StarredView() {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  useEffect(() => {
    fetchStarredAssets();
  }, []);

  const fetchStarredAssets = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/assets/starred');
      if (!response.ok) {
        throw new Error('Failed to fetch starred assets');
      }

      const data = await response.json();
      if (data.success) {
        setAssets(data.assets || []);
      } else {
        throw new Error(data.error || 'Failed to fetch starred assets');
      }
    } catch (err) {
      console.error('Error fetching starred assets:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch starred assets');
    } finally {
      setLoading(false);
    }
  };

  const handleAssetClick = (asset: Asset) => {
    setSelectedAsset(asset);
  };

  const handleAssetUpdate = (updatedAsset: Asset) => {
    if (!updatedAsset.isStarred) {
      // Remove from starred list if unstarred
      setAssets(prev => prev.filter(asset => asset.id !== updatedAsset.id));
    } else {
      // Update the asset in the list
      setAssets(prev => prev.map(asset => 
        asset.id === updatedAsset.id ? updatedAsset : asset
      ));
    }
  };

  if (loading) {
    return <DashboardSkeleton />;
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Error loading starred assets</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <button
              onClick={fetchStarredAssets}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Star className="h-8 w-8 text-yellow-500 fill-yellow-500" />
          <div>
            <h1 className="text-3xl font-bold">Starred Assets</h1>
            <p className="text-muted-foreground">
              {assets.length} starred {assets.length === 1 ? 'asset' : 'assets'}
            </p>
          </div>
        </div>

        {/* View Mode Toggle */}
        {assets.length > 0 && (
          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode("grid")}
              className={`p-2 rounded-md ${
                viewMode === "grid" 
                  ? "bg-primary text-primary-foreground" 
                  : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
              }`}
            >
              <Grid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`p-2 rounded-md ${
                viewMode === "list" 
                  ? "bg-primary text-primary-foreground" 
                  : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
              }`}
            >
              <List className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Content */}
      {assets.length === 0 ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Star className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">No starred assets</h2>
            <p className="text-muted-foreground">
              Star your favorite assets to see them here
            </p>
          </div>
        </div>
      ) : (
        <>
          {viewMode === "grid" ? (
            <AssetGrid
              assets={assets}
              onAssetClick={handleAssetClick}
              loading={loading}
            />
          ) : (
            <AssetList
              assets={assets}
              onAssetClick={handleAssetClick}
              loading={loading}
            />
          )}

          {/* Asset Details Modal */}
          {selectedAsset && (
            <AssetDetails
              asset={selectedAsset}
              onClose={() => setSelectedAsset(null)}
              onAssetUpdate={handleAssetUpdate}
            />
          )}
        </>
      )}
    </div>
  );
}
