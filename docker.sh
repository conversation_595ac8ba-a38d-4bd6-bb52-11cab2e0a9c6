#!/bin/bash

echo "Cleaning up processes..."

# Kill any existing Next.js processes
pkill -f "next dev"

# Kill any process using port 8080
sudo fuser -k 8080/tcp

# Wait for processes to fully terminate
sleep 3

# Stop and remove all containers
echo "Stopping containers..."
docker compose down

# Remove any dangling containers
echo "Cleaning up Docker..."
docker container prune -f

# Rebuild and start containers
echo "Starting containers..."
docker compose up --build -d

# Show logs
echo "Showing logs..."
docker compose logs -f 