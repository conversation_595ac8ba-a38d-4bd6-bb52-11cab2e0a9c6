<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Login Test</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
            <small style="color: #666;">Correct: <EMAIL> (note: aawsat, not aawsta)</small>
        </div>

        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="Asharq!@2025" required>
            <small style="color: #666;">Correct: Asharq!@2025</small>
        </div>
        
        <button type="submit">Test Login</button>
    </form>
    
    <div id="result"></div>
    
    <h2>Current Authentication Status</h2>
    <div id="authStatus"></div>
    
    <button onclick="checkAuth()">Check Auth Status</button>
    <button onclick="clearAuth()">Clear Auth</button>
    <button onclick="testDashboard()">Test Dashboard Access</button>

    <script>
        function updateAuthStatus() {
            const isAuth = localStorage.getItem('isAuthenticated');
            const user = localStorage.getItem('user');
            
            document.getElementById('authStatus').innerHTML = `
                <p><strong>isAuthenticated:</strong> ${isAuth}</p>
                <p><strong>user:</strong> ${user ? JSON.stringify(JSON.parse(user), null, 2) : 'null'}</p>
            `;
        }
        
        function checkAuth() {
            updateAuthStatus();
        }
        
        function clearAuth() {
            localStorage.removeItem('isAuthenticated');
            localStorage.removeItem('user');
            updateAuthStatus();
        }
        
        async function testDashboard() {
            try {
                const response = await fetch('/dashboard');
                const text = await response.text();
                console.log('Dashboard response:', response.status, text.substring(0, 200));
                
                document.getElementById('result').innerHTML = `
                    <div class="success">
                        <strong>Dashboard Test:</strong> Status ${response.status}<br>
                        Response length: ${text.length} characters
                    </div>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="error">
                        <strong>Dashboard Test Error:</strong> ${error.message}
                    </div>
                `;
            }
        }
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                console.log('Attempting login...');
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                const data = await response.json();
                console.log('Login response:', data);
                
                if (response.ok && data.success) {
                    // Store user in localStorage
                    localStorage.setItem('user', JSON.stringify(data.user));
                    localStorage.setItem('isAuthenticated', 'true');
                    
                    document.getElementById('result').innerHTML = `
                        <div class="success">
                            <strong>Login Successful!</strong><br>
                            User: ${data.user.name} (${data.user.email})<br>
                            Role: ${data.user.role}
                        </div>
                    `;
                    
                    updateAuthStatus();
                } else {
                    document.getElementById('result').innerHTML = `
                        <div class="error">
                            <strong>Login Failed:</strong> ${data.error || 'Unknown error'}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById('result').innerHTML = `
                    <div class="error">
                        <strong>Network Error:</strong> ${error.message}
                    </div>
                `;
            }
        });
        
        // Initialize auth status on page load
        updateAuthStatus();
    </script>
</body>
</html>
